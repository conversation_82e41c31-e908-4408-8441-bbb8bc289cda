// Chrome扩展 - Background Service Worker
// 处理popup消息并与content script通信

console.log('🚀 Background Service Worker 已启动');

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('📨 Background收到消息:', request);
  
  if (request.action === 'processText') {
    // 获取当前活动标签页
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        // 向content script发送处理消息
        chrome.tabs.sendMessage(tabs[0].id, { action: 'processText' }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('❌ 发送消息到content script失败:', chrome.runtime.lastError);
            sendResponse({ success: false, message: '无法连接到页面内容脚本' });
          } else {
            console.log('✅ Content script响应:', response);
            sendResponse(response);
          }
        });
      } else {
        sendResponse({ success: false, message: '无法获取当前标签页' });
      }
    });
    
    return true; // 保持消息通道开放
  }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    console.log('📄 标签页加载完成:', tab.url);
  }
});

// 监听扩展图标点击事件（备选方案）
chrome.action.onClicked.addListener((tab) => {
  console.log('🖱️ 扩展图标被点击，标签页:', tab.url);

  // 向当前标签页的content script发送处理消息
  chrome.tabs.sendMessage(tab.id, { action: 'processText' }, (response) => {
    if (chrome.runtime.lastError) {
      console.error('❌ 发送消息到content script失败:', chrome.runtime.lastError);
    } else {
      console.log('✅ Content script响应:', response);
    }
  });
});

console.log('✅ Background Service Worker 初始化完成');
