// Add debug logging when service worker starts
console.log('Background script loaded');

// 简单的测试函数
function testTextNodeExtraction() {
  console.log('🧪 Testing text node extraction...');
  console.log('Document body exists:', !!document.body);

  // 简单的文本节点查找
  const walker = document.createTreeWalker(
    document.body,
    NodeFilter.SHOW_TEXT,
    {
      acceptNode: function(node) {
        // 跳过空白文本和特殊标签
        const parent = node.parentElement;
        if (!parent || ['SCRIPT', 'STYLE', 'NOSCRIPT'].includes(parent.tagName)) {
          return NodeFilter.FILTER_REJECT;
        }
        if (node.textContent.trim().length === 0) {
          return NodeFilter.FILTER_REJECT;
        }
        return NodeFilter.FILTER_ACCEPT;
      }
    }
  );

  const textNodes = [];
  let node;
  while (node = walker.nextNode()) {
    textNodes.push(node);
  }

  console.log(`🔍 TreeWalker found ${textNodes.length} text nodes`);
  textNodes.slice(0, 3).forEach((node, index) => {
    console.log(`  ${index + 1}: "${node.textContent.trim().substring(0, 30)}..."`);
  });

  return textNodes;
}

// Function to process text nodes and replace via API with viewport optimization
async function processAndReplaceText() {
  console.log('🚀 processAndReplaceText function executed in page context');
  console.log('📄 Document body:', document.body);
  console.log('📊 Document ready state:', document.readyState);

  // 全局变量存储已处理的文本节点
  window.processedTextNodes = window.processedTextNodes || new Set();
  window.isProcessing = window.isProcessing || false;

  // 内嵌API调用函数 - 添加超时控制
  async function callTextProcessingAPI(text, timeout = 10000) {
    const API_URL = 'http://localhost:8000/translate';

    try {
      console.log(`📡 调用本地API: "${text.substring(0, 30)}..."`);

      // 创建超时Promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API请求超时')), timeout);
      });

      // 创建fetch Promise
      const fetchPromise = fetch(API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: text
        })
      });

      // 使用Promise.race实现超时控制
      const response = await Promise.race([fetchPromise, timeoutPromise]);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`📦 API响应:`, data);

      // 根据你的API响应格式：{ "result": "处理后的文本", "status": "success" }
      if (data.status === 'success' && data.result) {
        console.log(`✅ API处理成功: "${data.result.substring(0, 50)}..."`);
        return data.result;
      } else {
        console.warn('⚠️ API响应格式异常:', data);
        return text;
      }

    } catch (error) {
      console.error('❌ API调用失败:', error);
      // 如果API调用失败，返回原文本
      return text;
    }
  }

  // 并发控制器 - 限制同时进行的API请求数量
  class ConcurrencyController {
    constructor(maxConcurrency = 3) {
      this.maxConcurrency = maxConcurrency;
      this.running = 0;
      this.queue = [];
    }

    async execute(asyncFunction) {
      return new Promise((resolve, reject) => {
        this.queue.push({
          asyncFunction,
          resolve,
          reject
        });
        this.tryNext();
      });
    }

    async tryNext() {
      if (this.running >= this.maxConcurrency || this.queue.length === 0) {
        return;
      }

      this.running++;
      const { asyncFunction, resolve, reject } = this.queue.shift();

      try {
        const result = await asyncFunction();
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        this.running--;
        this.tryNext();
      }
    }
  }

  // 检查元素是否在视窗内
  function isElementInViewport(element) {
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;

    return (
      rect.top >= -100 && // 提前100px开始处理
      rect.left >= -100 &&
      rect.bottom <= windowHeight + 100 && // 延后100px停止处理
      rect.right <= windowWidth + 100
    );
  }

  // 获取视窗内的文本节点
  function getVisibleTextNodes(element) {
    const textNodes = [];

    function walk(node) {
      if (node.nodeType === 3) { // TEXT_NODE
        const text = node.textContent.trim();
        if (text.length > 0) {
          // 跳过script、style等标签内的文本
          const parent = node.parentElement;
          if (parent && !['SCRIPT', 'STYLE', 'NOSCRIPT', 'HEAD'].includes(parent.tagName)) {
            // 检查父元素是否在视窗内
            if (isElementInViewport(parent)) {
              textNodes.push(node);
            }
          }
        }
      } else if (node.nodeType === 1) { // ELEMENT_NODE
        // 跳过script、style等标签
        if (!['SCRIPT', 'STYLE', 'NOSCRIPT', 'HEAD'].includes(node.tagName)) {
          for (let child of node.childNodes) {
            walk(child);
          }
        }
      }
    }

    walk(element);
    return textNodes;
  }

  // 内嵌简单的文本节点获取函数（获取所有文本节点）
  function getAllTextNodes(element) {
    const textNodes = [];

    function walk(node) {
      if (node.nodeType === 3) { // TEXT_NODE
        const text = node.textContent.trim();
        if (text.length > 0) {
          // 跳过script、style等标签内的文本
          const parent = node.parentElement;
          if (parent && !['SCRIPT', 'STYLE', 'NOSCRIPT', 'HEAD'].includes(parent.tagName)) {
            textNodes.push(node);
          }
        }
      } else if (node.nodeType === 1) { // ELEMENT_NODE
        // 跳过script、style等标签
        if (!['SCRIPT', 'STYLE', 'NOSCRIPT', 'HEAD'].includes(node.tagName)) {
          for (let child of node.childNodes) {
            walk(child);
          }
        }
      }
    }

    walk(element);
    return textNodes;
  }

  // 处理文本节点的函数 - 改为异步并发处理
  async function processTextNodes(textNodes, description = '') {
    if (window.isProcessing) {
      console.log('⏸️ 已有处理任务在进行中，跳过');
      return;
    }

    window.isProcessing = true;

    // 初始化并发控制器（最多同时3个请求）
    if (!window.concurrencyController) {
      window.concurrencyController = new ConcurrencyController(3);
    }

    // 过滤出有意义且未处理的文本节点
    const validTextNodes = textNodes.filter(node => {
      const text = node.textContent.trim();
      const nodeId = `${node.parentElement.tagName}_${text}`;

      return (
        text.length > 0 &&
        text.length < 500 &&
        !window.processedTextNodes.has(nodeId) &&
        !text.startsWith('<处理后>') // 避免重复处理
      );
    });

    if (validTextNodes.length === 0) {
      console.log(`⏭️ ${description}没有新的文本节点需要处理`);
      window.isProcessing = false;
      return;
    }

    console.log(`✅ ${description}开始并发处理 ${validTextNodes.length} 个新的文本节点`);

    // 创建处理任务数组
    const processingTasks = validTextNodes.map((textNode, index) => {
      const originalText = textNode.textContent.trim();
      const nodeId = `${textNode.parentElement.tagName}_${originalText}`;

      // 返回一个异步任务函数
      return () => window.concurrencyController.execute(async () => {
        try {
          console.log(`🔄 Processing text node ${index + 1}/${validTextNodes.length}: "${originalText.substring(0, 30)}..."`);

          // 调用API处理文本，设置10秒超时
          const processedText = await callTextProcessingAPI(originalText, 10000);

          if (processedText && processedText !== originalText) {
            // 直接替换文本节点的内容
            textNode.textContent = processedText;
            console.log(`✅ Replaced ${index + 1}/${validTextNodes.length}: "${originalText.substring(0, 20)}..." -> "${processedText.substring(0, 20)}..."`);

            // 标记为已处理
            window.processedTextNodes.add(nodeId);
            return { success: true, index: index + 1, originalText, processedText };
          } else {
            console.log(`⏭️ Skipped ${index + 1}/${validTextNodes.length}: "${originalText.substring(0, 30)}..." (no change)`);
            return { success: false, index: index + 1, originalText, reason: 'no change' };
          }

        } catch (error) {
          console.error(`❌ Error processing text node ${index + 1}/${validTextNodes.length}:`, error);
          return { success: false, index: index + 1, originalText, error: error.message };
        }
      });
    });

    // 并发执行所有任务
    try {
      console.log(`🚀 开始并发执行 ${processingTasks.length} 个处理任务...`);
      const startTime = Date.now();

      // 使用Promise.allSettled确保所有任务都完成（无论成功或失败）
      const results = await Promise.allSettled(processingTasks.map(task => task()));

      const endTime = Date.now();
      const duration = endTime - startTime;

      // 统计结果
      const successful = results.filter(result =>
        result.status === 'fulfilled' && result.value.success
      ).length;
      const failed = results.length - successful;

      console.log(`🎉 ${description}并发处理完成！`);
      console.log(`📊 处理统计: 成功 ${successful}/${results.length}, 失败 ${failed}/${results.length}`);
      console.log(`⏱️ 总耗时: ${duration}ms (平均 ${Math.round(duration / results.length)}ms/个)`);

      // 显示失败的任务详情
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`❌ Task ${index + 1} rejected:`, result.reason);
        } else if (result.value && !result.value.success && result.value.error) {
          console.error(`❌ Task ${index + 1} failed:`, result.value.error);
        }
      });

    } catch (error) {
      console.error('❌ 并发处理过程中发生错误:', error);
    } finally {
      window.isProcessing = false;
    }
  }

  // 首次处理：只处理视窗内的文本节点
  console.log('📝 Starting to get visible text nodes...');
  const visibleTextNodes = getVisibleTextNodes(document.body);
  console.log(`📊 Found ${visibleTextNodes.length} visible text nodes to process`);

  await processTextNodes(visibleTextNodes, '初始视窗内');

  // 设置滚动监听器，处理新出现的内容
  let scrollTimeout;
  const handleScroll = async () => {
    // 防抖处理，避免频繁触发
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(async () => {
      console.log('📜 检测到滚动，处理新出现的内容...');
      const newVisibleTextNodes = getVisibleTextNodes(document.body);
      await processTextNodes(newVisibleTextNodes, '滚动后新出现的');
    }, 500); // 500ms延迟
  };

  // 添加滚动事件监听器
  window.addEventListener('scroll', handleScroll, { passive: true });

  // 添加窗口大小变化监听器
  window.addEventListener('resize', handleScroll, { passive: true });

  console.log('🎉 初始处理完成，滚动监听器已设置！');
}

// 递归获取所有文本节点
function getTextNodes(element) {
  const textNodes = [];

  function traverse(node) {
    // 如果是文本节点
    if (node.nodeType === Node.TEXT_NODE) {
      // 跳过script、style等标签内的文本
      const parent = node.parentElement;
      if (parent && !['SCRIPT', 'STYLE', 'NOSCRIPT', 'HEAD'].includes(parent.tagName)) {
        const text = node.textContent.trim();
        if (text.length > 0) {
          textNodes.push(node);
        }
      }
    }
    // 如果是元素节点，继续遍历其子节点
    else if (node.nodeType === Node.ELEMENT_NODE) {
      // 跳过script、style等标签
      if (!['SCRIPT', 'STYLE', 'NOSCRIPT', 'HEAD'].includes(node.tagName)) {
        for (let child of node.childNodes) {
          traverse(child);
        }
      }
    }
  }

  traverse(element);
  console.log(`getTextNodes found ${textNodes.length} text nodes`);
  return textNodes;
}



// Listen for clicks on the extension icon
chrome.action.onClicked.addListener((tab) => {
  console.log('Extension icon clicked!', tab);

  // Don't try to inject into chrome:// pages
  if (tab.url.includes('chrome://') || tab.url.includes('chrome-extension://')) {
    console.log('Cannot inject into chrome:// or chrome-extension:// pages');
    return;
  }

  console.log('Attempting to inject text processing script into tab:', tab.id);

  chrome.scripting.executeScript({
    target: { tabId: tab.id },
    function: processAndReplaceText
  }).then(() => {
    console.log('Text processing script injection successful');
  }).catch((error) => {
    console.error('Script injection failed:', error);
  });
});
