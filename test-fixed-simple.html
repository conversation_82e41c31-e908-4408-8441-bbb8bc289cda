<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版简化跟踪测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info-box {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007cba;
        }
        .text-block {
            margin: 15px 0;
            padding: 10px;
            background: #fff;
            border-left: 4px solid #007cba;
            border-radius: 3px;
        }
        .test-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #005a8b;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .status-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复版简化跟踪测试</h1>
        
        <div class="success-box">
            <h3>✅ 问题已修复</h3>
            <p><strong>原问题</strong>：<code>window is not defined</code> 错误</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
                <li>将DOM操作代码移到 <code>content-simple.js</code>（Content Script）</li>
                <li>创建简单的 <code>background.js</code>（Service Worker）处理消息转发</li>
                <li>Content Script可以正常使用 <code>window</code> 对象</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>🏗️ 新架构</h3>
            <ul>
                <li><strong>background.js</strong>：Service Worker，处理popup消息并转发给content script</li>
                <li><strong>content-simple.js</strong>：Content Script，运行在页面环境中，处理DOM操作</li>
                <li><strong>manifest.json</strong>：配置了content_scripts自动注入</li>
            </ul>
        </div>

        <div class="test-controls">
            <h3>🧪 测试控制</h3>
            <button class="btn" onclick="showMemoryStatus()">显示内存状态</button>
            <button class="btn btn-success" onclick="refreshPage()">刷新页面</button>
        </div>

        <div id="memory-status" class="status-display" style="display: none;">
            <h4>🧠 内存状态</h4>
            <div id="memory-content"></div>
        </div>

        <div class="info-box">
            <h3>📋 测试步骤</h3>
            <ol>
                <li><strong>重新加载扩展</strong>：在Chrome扩展管理页面点击刷新</li>
                <li><strong>打开开发者工具</strong>：查看Console日志</li>
                <li><strong>点击扩展图标</strong>：应该看到文本被处理</li>
                <li><strong>检查日志</strong>：应该看到完整的处理流程日志</li>
                <li><strong>重复点击</strong>：验证不会重复处理</li>
                <li><strong>刷新页面</strong>：验证状态重置和重新处理</li>
            </ol>
        </div>

        <div class="text-block">
            测试文本1：这是第一个测试文本，用于验证扩展是否正常工作。
        </div>
        <div class="text-block">
            测试文本2：修复后的扩展应该可以正常处理这些文本内容。
        </div>
        <div class="text-block">
            测试文本3：纯内存跟踪确保页面刷新后可以重新处理。
        </div>
        <div class="text-block">
            测试文本4：API失败的节点下次可以重新尝试处理。
        </div>
        <div class="text-block">
            测试文本5：并发安全机制防止重复的API调用。
        </div>

        <div class="success-box">
            <h3>🎯 预期行为</h3>
            <ul>
                <li>✅ 扩展加载无错误</li>
                <li>✅ 点击图标可以处理文本</li>
                <li>✅ Console显示完整处理日志</li>
                <li>✅ 重复点击不会重复处理</li>
                <li>✅ 页面刷新后可以重新处理</li>
                <li>✅ API失败时可以重试</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>🔍 关键日志</h3>
            <p>在Console中应该看到：</p>
            <ul>
                <li><code>🚀 Background Service Worker 已启动</code></li>
                <li><code>🚀 Chrome扩展文本处理脚本已加载（简化版）</code></li>
                <li><code>📄 页面已加载，扩展已准备就绪</code></li>
                <li><code>📨 Background收到消息</code></li>
                <li><code>⏳ 标记节点为处理中</code></li>
                <li><code>✅ 标记节点为已处理</code></li>
            </ul>
        </div>
    </div>

    <script>
        // 显示内存状态
        function showMemoryStatus() {
            const statusDiv = document.getElementById('memory-status');
            const contentDiv = document.getElementById('memory-content');
            
            try {
                let html = '<h5>🧠 内存状态统计:</h5>';
                
                if (typeof window.processedTextNodes !== 'undefined') {
                    html += `<p>已处理节点 (WeakSet): 已初始化</p>`;
                } else {
                    html += `<p>已处理节点 (WeakSet): 未初始化</p>`;
                }
                
                if (typeof window.processedNodeIds !== 'undefined') {
                    html += `<p>已处理节点ID数量 (Set): ${window.processedNodeIds.size}</p>`;
                } else {
                    html += `<p>已处理节点ID (Set): 未初始化</p>`;
                }
                
                if (typeof window.processingNodes !== 'undefined') {
                    html += `<p>处理中节点数量 (Set): ${window.processingNodes.size}</p>`;
                } else {
                    html += `<p>处理中节点 (Set): 未初始化</p>`;
                }
                
                html += `<p>处理状态: ${window.isProcessing ? '进行中' : '空闲'}</p>`;
                html += `<p>当前时间: ${new Date().toLocaleString()}</p>`;
                
                contentDiv.innerHTML = html;
                statusDiv.style.display = 'block';
                
                console.log('📊 内存状态已显示');
            } catch (error) {
                contentDiv.innerHTML = '<p style="color: red;">读取内存状态失败: ' + error.message + '</p>';
                statusDiv.style.display = 'block';
            }
        }

        // 刷新页面
        function refreshPage() {
            console.log('🔄 手动刷新页面');
            window.location.reload();
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🎉 修复版测试页面加载完成！');
            console.log('🔧 请重新加载扩展，然后点击扩展图标测试');
            
            // 显示当前内存状态
            showMemoryStatus();
        });
    </script>
</body>
</html>
