<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
            margin: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 12px;
        }
        .button-container {
            text-align: center;
            margin: 20px 0;
        }
        .process-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            width: 100%;
            transition: background-color 0.3s;
        }
        .process-btn:hover {
            background: #005a8b;
        }
        .process-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
            min-height: 20px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.processing {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #e7f3ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007cba;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .info-section {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 11px;
            color: #666;
        }
        .info-section h4 {
            margin: 0 0 8px 0;
            font-size: 12px;
            color: #333;
        }
        .info-section ul {
            margin: 0;
            padding-left: 16px;
        }
        .info-section li {
            margin: 3px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🌐 文本处理器</h2>
        <p>点击按钮处理页面文本</p>
    </div>
    
    <div class="button-container">
        <button id="processBtn" class="process-btn">
            🚀 开始处理文本
        </button>
    </div>
    
    <div id="status" class="status info">
        准备就绪，点击按钮开始处理页面文本
    </div>
    
    <div class="info-section">
        <h4>📋 功能说明</h4>
        <ul>
            <li>自动识别页面文本内容</li>
            <li>调用API进行文本处理</li>
            <li>智能跳过已处理内容</li>
            <li>支持并发安全处理</li>
            <li>页面刷新后可重新处理</li>
        </ul>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
