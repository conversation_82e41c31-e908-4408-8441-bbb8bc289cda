<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版跟踪测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .text-block {
            margin: 15px 0;
            padding: 10px;
            background: #fff;
            border-left: 4px solid #007cba;
            border-radius: 3px;
        }
        .info-box {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007cba;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #005a8b;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .status-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .error-simulation {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 简化版跟踪机制测试</h1>
        
        <div class="info-box">
            <h3>✨ 新方案特点</h3>
            <ul>
                <li><strong>纯内存存储</strong>：不使用localStorage/sessionStorage</li>
                <li><strong>页面刷新重置</strong>：刷新后所有状态自动清空，可重新处理</li>
                <li><strong>API失败重试</strong>：失败的节点下次可以重新处理</li>
                <li><strong>并发安全</strong>：防止重复API调用</li>
                <li><strong>简单可靠</strong>：无复杂的存储管理</li>
            </ul>
        </div>

        <div class="warning-box">
            <h3>📋 测试步骤</h3>
            <ol>
                <li><strong>首次处理</strong>：点击扩展图标，观察文本被处理</li>
                <li><strong>重复点击</strong>：再次点击，确认不会重复处理</li>
                <li><strong>页面刷新</strong>：按F5刷新页面，观察文本恢复原文</li>
                <li><strong>重新处理</strong>：刷新后再次点击扩展，确认可以重新处理</li>
                <li><strong>API失败测试</strong>：停止API服务器，测试失败重试</li>
            </ol>
        </div>

        <div class="test-controls">
            <h3>🧪 测试控制</h3>
            <button class="btn" onclick="showMemoryStatus()">显示内存状态</button>
            <button class="btn btn-secondary" onclick="clearMemoryStatus()">清空内存状态</button>
            <button class="btn btn-success" onclick="refreshPage()">刷新页面</button>
            <button class="btn btn-danger" onclick="simulateApiError()">模拟API错误</button>
        </div>

        <div id="memory-status" class="status-display" style="display: none;">
            <h4>🧠 内存状态</h4>
            <div id="memory-content"></div>
        </div>

        <div class="test-section">
            <h2>🔄 第一组：页面刷新测试</h2>
            <div class="success-box">
                <strong>🎯 验证目标：</strong>页面刷新后可以重新处理所有文本
            </div>
            <div class="text-block">
                刷新测试文本1：这个文本处理后刷新页面应该可以重新处理。
            </div>
            <div class="text-block">
                刷新测试文本2：纯内存存储确保页面刷新后状态完全重置。
            </div>
            <div class="text-block">
                刷新测试文本3：用户体验完全符合预期的刷新行为。
            </div>
        </div>

        <div class="test-section">
            <h2>🔒 第二组：并发安全测试</h2>
            <div class="success-box">
                <strong>🎯 验证目标：</strong>快速连续点击不会产生重复API请求
            </div>
            <div class="text-block">
                并发测试文本1：快速连续点击扩展图标不会重复处理。
            </div>
            <div class="text-block">
                并发测试文本2：处理中状态确保并发安全性。
            </div>
        </div>

        <div class="test-section">
            <h2>🔄 第三组：API失败重试测试</h2>
            <div class="error-simulation">
                <strong>🧪 测试方法：</strong>
                <ol>
                    <li>停止API服务器（关闭 http://localhost:8000）</li>
                    <li>点击扩展图标，观察API调用失败</li>
                    <li>重新启动API服务器</li>
                    <li>再次点击扩展图标，确认失败的文本可以重新处理</li>
                </ol>
            </div>
            <div class="text-block">
                重试测试文本1：API失败时这个文本不会被标记为已处理。
            </div>
            <div class="text-block">
                重试测试文本2：重新启动API后可以继续处理失败的文本。
            </div>
            <div class="text-block">
                重试测试文本3：失败重试机制确保处理的完整性。
            </div>
        </div>

        <div class="test-section">
            <h2>⚡ 第四组：性能测试</h2>
            <div class="success-box">
                <strong>🎯 验证目标：</strong>大量文本的处理性能和稳定性
            </div>
            <div class="text-block">性能测试文本1：测试大量文本节点的并发处理能力。</div>
            <div class="text-block">性能测试文本2：验证内存使用效率和垃圾回收。</div>
            <div class="text-block">性能测试文本3：确保长时间运行的稳定性。</div>
            <div class="text-block">性能测试文本4：多次处理的性能表现。</div>
            <div class="text-block">性能测试文本5：并发控制的有效性验证。</div>
            <div class="text-block">性能测试文本6：错误处理的性能影响。</div>
            <div class="text-block">性能测试文本7：内存泄漏检测和预防。</div>
            <div class="text-block">性能测试文本8：大规模文本处理压力测试。</div>
        </div>

        <div class="info-box">
            <h3>🔍 关键日志标识</h3>
            <ul>
                <li><code>⏳ 标记节点为处理中</code> - 节点开始处理前的标记</li>
                <li><code>✅ 标记节点为已处理</code> - 节点处理完成的标记</li>
                <li><code>⏳ 节点正在处理中，跳过</code> - 检测到重复处理尝试</li>
                <li><code>🧹 清理节点处理中状态</code> - API失败时的状态清理</li>
                <li><code>❌ 处理失败</code> - API调用失败</li>
                <li><code>💡 失败的节点下次点击扩展时可以重新处理</code> - 重试提示</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>📈 预期行为</h3>
            <ul>
                <li><strong>首次处理</strong>：所有文本正常处理和替换</li>
                <li><strong>重复点击</strong>：显示"没有新的文本节点需要处理"</li>
                <li><strong>页面刷新</strong>：文本恢复原文，内存状态清空</li>
                <li><strong>刷新后处理</strong>：可以重新处理所有文本</li>
                <li><strong>API失败</strong>：失败的节点不会被标记为已处理</li>
                <li><strong>失败重试</strong>：API恢复后可以重新处理失败的节点</li>
            </ul>
        </div>

        <div class="success-box">
            <h3>✅ 成功标准</h3>
            <ol>
                <li>首次点击：文本被正常处理</li>
                <li>重复点击：不会重复处理</li>
                <li>页面刷新：文本恢复原文，状态清空</li>
                <li>刷新后处理：可以重新处理</li>
                <li>API失败：失败节点可以重试</li>
                <li>并发安全：无重复API请求</li>
            </ol>
        </div>
    </div>

    <script>
        // 显示内存状态
        function showMemoryStatus() {
            const statusDiv = document.getElementById('memory-status');
            const contentDiv = document.getElementById('memory-content');
            
            try {
                let html = '<h5>🧠 内存状态统计:</h5>';
                
                // 检查全局变量是否存在
                if (typeof window.processedTextNodes !== 'undefined') {
                    html += `<p>已处理节点数量 (WeakSet): 无法直接计数（WeakSet特性）</p>`;
                } else {
                    html += `<p>已处理节点 (WeakSet): 未初始化</p>`;
                }
                
                if (typeof window.processedNodeIds !== 'undefined') {
                    html += `<p>已处理节点ID数量 (Set): ${window.processedNodeIds.size}</p>`;
                    if (window.processedNodeIds.size > 0) {
                        html += '<h6>已处理节点ID列表:</h6>';
                        html += '<pre>';
                        Array.from(window.processedNodeIds).forEach((id, index) => {
                            html += `${index + 1}. ${id.substring(0, 80)}...\n`;
                        });
                        html += '</pre>';
                    }
                } else {
                    html += `<p>已处理节点ID (Set): 未初始化</p>`;
                }
                
                if (typeof window.processingNodes !== 'undefined') {
                    html += `<p>处理中节点数量 (Set): ${window.processingNodes.size}</p>`;
                    if (window.processingNodes.size > 0) {
                        html += '<h6>处理中节点ID列表:</h6>';
                        html += '<pre>';
                        Array.from(window.processingNodes).forEach((id, index) => {
                            html += `${index + 1}. ${id.substring(0, 80)}...\n`;
                        });
                        html += '</pre>';
                    }
                } else {
                    html += `<p>处理中节点 (Set): 未初始化</p>`;
                }
                
                html += `<p>处理状态: ${window.isProcessing ? '进行中' : '空闲'}</p>`;
                html += `<p>当前时间: ${new Date().toLocaleString()}</p>`;
                
                contentDiv.innerHTML = html;
                statusDiv.style.display = 'block';
                
                console.log('📊 内存状态已显示');
            } catch (error) {
                contentDiv.innerHTML = '<p style="color: red;">读取内存状态失败: ' + error.message + '</p>';
                statusDiv.style.display = 'block';
            }
        }

        // 清空内存状态
        function clearMemoryStatus() {
            try {
                if (typeof window.processedTextNodes !== 'undefined') {
                    window.processedTextNodes = new WeakSet();
                }
                if (typeof window.processedNodeIds !== 'undefined') {
                    window.processedNodeIds = new Set();
                }
                if (typeof window.processingNodes !== 'undefined') {
                    window.processingNodes = new Set();
                }
                window.isProcessing = false;
                
                console.log('🧹 已清空内存状态');
                alert('已清空内存状态');
                showMemoryStatus();
            } catch (error) {
                console.error('清空内存状态失败:', error);
                alert('清空内存状态失败: ' + error.message);
            }
        }

        // 刷新页面
        function refreshPage() {
            console.log('🔄 手动刷新页面');
            window.location.reload();
        }

        // 模拟API错误
        function simulateApiError() {
            alert('请手动停止API服务器来测试错误处理:\n\n1. 停止 http://localhost:8000 服务\n2. 点击扩展图标\n3. 观察错误处理\n4. 重启服务器\n5. 再次点击扩展测试重试');
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🎉 简化版跟踪测试页面加载完成！');
            console.log('📋 页面包含 ' + document.querySelectorAll('.text-block').length + ' 个测试文本块');
            console.log('🔧 请按照测试步骤进行验证：');
            console.log('   1. 点击扩展图标处理文本');
            console.log('   2. 再次点击验证不重复处理');
            console.log('   3. 刷新页面观察状态重置');
            console.log('   4. 再次点击验证可重新处理');
            console.log('   5. 测试API失败重试机制');
            console.log('💡 使用页面上的按钮查看和管理内存状态');
            
            // 显示当前内存状态
            showMemoryStatus();
        });

        // 监听页面刷新前事件
        window.addEventListener('beforeunload', function(e) {
            console.log('🔄 页面即将刷新，内存状态将被清空');
        });
    </script>
</body>
</html>
