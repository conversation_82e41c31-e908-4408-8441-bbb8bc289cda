<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话级跟踪测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .text-block {
            margin: 15px 0;
            padding: 10px;
            background: #fff;
            border-left: 4px solid #007cba;
            border-radius: 3px;
        }
        .info-box {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007cba;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #005a8b;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .status-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .step-indicator {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #6c757d;
        }
        .step-active {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .step-completed {
            background: #d4edda;
            border-left-color: #28a745;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 会话级跟踪测试页面</h1>
        
        <div class="info-box">
            <h3>🎯 测试目标</h3>
            <p>验证新的会话级跟踪机制能够：</p>
            <ul>
                <li><strong>页面刷新重置</strong>：刷新后可以重新处理所有文本</li>
                <li><strong>会话内防重复</strong>：同一会话内不会重复处理</li>
                <li><strong>状态自动清理</strong>：页面刷新时自动清理所有状态</li>
                <li><strong>用户体验优化</strong>：符合用户对刷新行为的预期</li>
            </ul>
        </div>

        <div class="warning-box">
            <h3>📋 完整测试流程</h3>
            <div class="step-indicator" id="step1">
                <strong>步骤1：</strong>点击Chrome扩展图标，处理页面文本
            </div>
            <div class="step-indicator" id="step2">
                <strong>步骤2：</strong>再次点击扩展图标，验证不会重复处理
            </div>
            <div class="step-indicator" id="step3">
                <strong>步骤3：</strong>刷新页面（F5或Ctrl+R）
            </div>
            <div class="step-indicator" id="step4">
                <strong>步骤4：</strong>观察文本恢复原文
            </div>
            <div class="step-indicator" id="step5">
                <strong>步骤5：</strong>再次点击扩展图标，验证可以重新处理
            </div>
        </div>

        <div class="test-controls">
            <h3>🧪 测试控制面板</h3>
            <button class="btn" onclick="showSessionStatus()">显示会话状态</button>
            <button class="btn btn-secondary" onclick="clearSessionStorage()">清空会话存储</button>
            <button class="btn btn-success" onclick="refreshPage()">刷新页面</button>
            <button class="btn btn-secondary" onclick="simulateNewSession()">模拟新会话</button>
            <button class="btn btn-danger" onclick="clearAllStorage()">清空所有存储</button>
        </div>

        <div id="session-status" class="status-display" style="display: none;">
            <h4>📊 会话状态</h4>
            <div id="session-content"></div>
        </div>

        <div class="test-section">
            <h2>🔄 第一组：页面刷新测试</h2>
            <div class="success-box">
                <strong>🎯 核心验证：</strong>页面刷新后，文本恢复原文，且可以重新处理
            </div>
            <div class="text-block" id="refresh-1">
                刷新测试文本1：这个文本处理后刷新页面应该恢复原文，并可重新处理。
            </div>
            <div class="text-block" id="refresh-2">
                刷新测试文本2：验证sessionStorage在页面刷新时的正确行为。
            </div>
            <div class="text-block" id="refresh-3">
                刷新测试文本3：确保用户体验符合预期的刷新行为。
            </div>
        </div>

        <div class="test-section">
            <h2>🔒 第二组：会话内防重复测试</h2>
            <div class="success-box">
                <strong>🎯 核心验证：</strong>同一会话内，已处理的文本不会重复处理
            </div>
            <div class="text-block" id="session-1">
                会话测试文本1：在同一会话内，这个文本只应该被处理一次。
            </div>
            <div class="text-block" id="session-2">
                会话测试文本2：重复点击扩展图标不应该重新处理这个文本。
            </div>
            <div class="text-block" id="session-3">
                会话测试文本3：sessionStorage确保会话内的状态一致性。
            </div>
        </div>

        <div class="test-section">
            <h2>⚡ 第三组：并发安全测试</h2>
            <div class="success-box">
                <strong>🎯 核心验证：</strong>快速连续点击仍然不会产生重复API请求
            </div>
            <div class="text-block" id="concurrent-1">
                并发测试文本1：即使使用sessionStorage，并发安全性仍然保持。
            </div>
            <div class="text-block" id="concurrent-2">
                并发测试文本2：快速连续点击不会产生重复的API调用。
            </div>
        </div>

        <div class="test-section">
            <h2>🔄 第四组：状态转换测试</h2>
            <div class="success-box">
                <strong>🎯 核心验证：</strong>不同状态之间的正确转换
            </div>
            <div class="text-block" id="state-1">
                状态测试文本1：未处理 → 处理中 → 已完成 → 页面刷新 → 未处理。
            </div>
            <div class="text-block" id="state-2">
                状态测试文本2：验证状态在sessionStorage中的正确管理。
            </div>
        </div>

        <div class="info-box">
            <h3>🔍 关键日志标识</h3>
            <ul>
                <li><code>🔄 检测到页面刷新，清理所有处理状态</code> - 页面刷新检测</li>
                <li><code>✨ 页面已刷新，所有节点可重新处理</code> - 状态重置确认</li>
                <li><code>⏳ 标记节点为处理中</code> - 节点开始处理</li>
                <li><code>✅ 标记节点为已处理</code> - 节点处理完成</li>
                <li><code>⏳ 节点正在处理中</code> - 检测到重复处理尝试</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>📈 预期行为</h3>
            <ul>
                <li><strong>首次处理</strong>：所有文本正常处理和替换</li>
                <li><strong>重复点击</strong>：显示"没有新的文本节点需要处理"</li>
                <li><strong>页面刷新</strong>：文本恢复原文，状态完全清理</li>
                <li><strong>刷新后处理</strong>：可以重新处理所有文本</li>
                <li><strong>并发安全</strong>：快速点击不会产生重复API请求</li>
            </ul>
        </div>

        <div class="warning-box">
            <h3>🚨 故障排除</h3>
            <p>如果发现问题：</p>
            <ul>
                <li>检查控制台是否有"页面刷新检测"的日志</li>
                <li>使用"显示会话状态"查看sessionStorage内容</li>
                <li>确认页面刷新后sessionStorage被清空</li>
                <li>验证API服务器正常运行</li>
            </ul>
        </div>

        <div class="success-box">
            <h3>✅ 成功标准</h3>
            <ol>
                <li>首次点击扩展：文本被正常处理</li>
                <li>再次点击扩展：不会重复处理</li>
                <li>刷新页面：文本恢复原文</li>
                <li>刷新后点击扩展：可以重新处理文本</li>
                <li>整个过程中：Network面板无重复API请求</li>
            </ol>
        </div>
    </div>

    <script>
        // 显示会话状态
        function showSessionStatus() {
            const statusDiv = document.getElementById('session-status');
            const contentDiv = document.getElementById('session-content');
            
            try {
                const processedData = JSON.parse(sessionStorage.getItem('chrome_extension_processed_nodes') || '{}');
                const processingData = JSON.parse(sessionStorage.getItem('chrome_extension_processing_nodes') || '{}');
                const pageSession = sessionStorage.getItem('chrome_extension_page_session');
                
                let html = '<h5>📦 已处理节点 (sessionStorage):</h5>';
                html += '<pre>' + JSON.stringify(processedData, null, 2) + '</pre>';
                
                html += '<h5>⏳ 处理中节点 (sessionStorage):</h5>';
                html += '<pre>' + JSON.stringify(processingData, null, 2) + '</pre>';
                
                html += '<h5>🔑 页面会话ID:</h5>';
                html += '<pre>' + (pageSession || '未设置') + '</pre>';
                
                html += '<h5>📊 统计信息:</h5>';
                html += `<p>已处理节点数量: ${Object.keys(processedData).length}</p>`;
                html += `<p>处理中节点数量: ${Object.keys(processingData).length}</p>`;
                html += `<p>当前时间: ${new Date().toLocaleString()}</p>`;
                html += `<p>页面加载时间: ${new Date(window.pageLoadTime).toLocaleString()}</p>`;
                
                contentDiv.innerHTML = html;
                statusDiv.style.display = 'block';
                
                console.log('📊 会话状态已显示');
            } catch (error) {
                contentDiv.innerHTML = '<p style="color: red;">读取会话状态失败: ' + error.message + '</p>';
                statusDiv.style.display = 'block';
            }
        }

        // 清空会话存储
        function clearSessionStorage() {
            sessionStorage.removeItem('chrome_extension_processed_nodes');
            sessionStorage.removeItem('chrome_extension_processing_nodes');
            sessionStorage.removeItem('chrome_extension_page_session');
            console.log('🧹 已清空会话存储');
            alert('已清空会话存储');
            showSessionStatus();
        }

        // 刷新页面
        function refreshPage() {
            console.log('🔄 手动刷新页面');
            window.location.reload();
        }

        // 模拟新会话
        function simulateNewSession() {
            // 清空所有sessionStorage
            sessionStorage.clear();
            console.log('🆕 已模拟新会话（清空所有sessionStorage）');
            alert('已模拟新会话，所有状态已清空');
            showSessionStatus();
        }

        // 清空所有存储
        function clearAllStorage() {
            sessionStorage.clear();
            localStorage.clear();
            console.log('🧹 已清空所有存储');
            alert('已清空所有存储');
            showSessionStatus();
        }

        // 记录页面加载时间
        window.pageLoadTime = Date.now();

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🎉 会话级跟踪测试页面加载完成！');
            console.log('📋 页面包含 ' + document.querySelectorAll('.text-block').length + ' 个测试文本块');
            console.log('🔧 请按照完整测试流程进行验证：');
            console.log('   1. 点击扩展图标处理文本');
            console.log('   2. 再次点击验证不重复处理');
            console.log('   3. 刷新页面');
            console.log('   4. 观察文本恢复原文');
            console.log('   5. 再次点击验证可重新处理');
            console.log('💡 使用页面上的按钮查看和管理会话状态');
            
            // 显示当前会话信息
            showSessionStatus();
        });

        // 监听存储变化
        window.addEventListener('storage', function(e) {
            console.log('📦 检测到存储变化:', e.key, e.newValue);
        });

        // 监听页面刷新前事件
        window.addEventListener('beforeunload', function(e) {
            console.log('🔄 页面即将刷新/关闭');
        });

        // 检测页面可见性变化
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                console.log('👁️ 页面变为可见');
            } else {
                console.log('👁️ 页面变为隐藏');
            }
        });
    </script>
</body>
</html>
