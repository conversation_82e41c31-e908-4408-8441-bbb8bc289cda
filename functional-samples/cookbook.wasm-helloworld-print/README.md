# Using WASM as a module in Manifest V3

This recipe shows how to use WASM in Manifest V3.

To load WASM in Manifest V3, we need to use the `wasm-unsafe-eval` CSP directive ([Content Security Policy][0]).

## Overview

### Running this extension

1. Clone this repository.
2. Load this directory in Chrome as an [unpacked extension](https://developer.chrome.com/docs/extensions/mv3/getstarted/development-basics/#load-unpacked).
3. Find the extension named "WASM Load Example - Helloworld" and [inspect the service worker](https://developer.chrome.com/docs/extensions/mv3/tut_debugging/#debug-bg).

You will see the following output:

```
[from wasm] Inited.
[from wasm] Hello World!
[from wasm] Hello John
```

### Build WASM locally

We have already built the WASM file for you. If you want to build it yourself, follow the steps below.

1. Install [Rust](https://www.rust-lang.org/install.html).

2. Install [wasm-pack](https://rustwasm.github.io/wasm-pack/installer/).

   ```bash
   cargo install wasm-pack
   ```

3. Build WASM.

   ```bash
   cd wasm
   wasm-pack build --target web
   ```

## Implementation Notes

- To import the script generated by `wasm-pack`, we need to [declare the service worker as an ES Module][1].

```diff
 // manifest.json
 ...
 "background": {
     "service_worker": "background.js",
+    "type": "module"
 },
 ...
```

[0]: https://developer.chrome.com/docs/extensions/mv3/manifest/content_security_policy/
[1]: https://developer.chrome.com/docs/extensions/mv3/service_workers/basics/#import-scripts
