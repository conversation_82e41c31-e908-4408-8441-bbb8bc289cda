// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

const originalCreateObjectURL = URL.createObjectURL;

// Signal the creation of object URLs to the side panel
// Note: you should be only doing this for specific websites
// and not for all (as we do in this demo)
URL.createObjectURL = (object) => {
  const objectUrl = originalCreateObjectURL.call(URL, object);
  window.postMessage({ type: 'audio-scribe', objectUrl });
  return objectUrl;
};
