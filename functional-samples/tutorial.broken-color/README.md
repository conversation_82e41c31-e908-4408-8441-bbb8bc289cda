## Context

This is a simple sample extension used in the [Debugging extensions][1] tutorial. The purpose of this extension is to demonstrate how to debug different extension components. This extension changes the background color of active tab.

## Running this extension

1. Clone this repository.
2. Load this directory in Chrome as an [unpacked extension][2].
3. Go to https://developer.chrome.com/docs/extensions.
4. Open the extension popup and click on the color.
5. The background color will change.

[1]: https://developer.chrome.com/docs/extensions/mv3/tut_debugging/
[2]: https://developer.chrome.com/docs/extensions/mv3/getstarted/development-basics/#load-unpacked
