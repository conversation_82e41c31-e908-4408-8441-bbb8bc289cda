<!-- Copyright 2017 The Chromium Authors. All rights reserved.
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file. -->
<!doctype html>
<html>
  <head>
    <title>Water Popup</title>
    <style>
      body {
        text-align: center;
      }

      #hydrateImage {
        width: 100px;
        margin: 5px;
      }

      button {
        margin: 5px;
        outline: none;
      }

      button:hover {
        outline: #80deea dotted thick;
      }
    </style>
    <!--
      - JavaScript and HTML must be in separate files
     -->
  </head>
  <body>
    <img src="./stay_hydrated.png" id="hydrateImage" />
    <!-- An Alarm delay of less than the minimum 1 minute will fire
      in approximately 1 minute increments if released -->
    <button id="sampleMinute" value="1">Sample minute</button>
    <button id="min15" value="15">15 Minutes</button>
    <button id="min30" value="30">30 Minutes</button>
    <button id="cancelAlarm">Cancel Alarm</button>
    <script src="popup.js"></script>
  </body>
</html>
