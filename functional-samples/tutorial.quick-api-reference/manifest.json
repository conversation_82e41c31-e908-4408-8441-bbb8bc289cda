{"manifest_version": 3, "name": "Quick API Reference", "description": "Quick API can speed up the building of Chrome extensions.", "version": "1.0.0", "icons": {"16": "images/icon-16.png", "128": "images/icon-128.png"}, "background": {"service_worker": "service-worker.js", "type": "module"}, "minimum_chrome_version": "102", "omnibox": {"keyword": "api"}, "permissions": ["alarms", "storage"], "host_permissions": ["https://extension-tips.glitch.me/*"], "content_scripts": [{"matches": ["https://developer.chrome.com/docs/extensions/reference/*"], "js": ["content.js"]}]}