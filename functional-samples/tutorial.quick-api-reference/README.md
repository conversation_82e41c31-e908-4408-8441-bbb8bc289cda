# Tutorial: Handling events

This sample demonstrates how to handle events with service workers.

## Overview

The extension built allows users to quickly navigate to Chrome API reference pages using the omnibox.

The events from the following APIs have been handled: `chrome.runtime`, `chrome.omnibox` and `chrome.alarms`.

The complete tutorial is available [here](https://developer.chrome.com/docs/extensions/get-started/tutorial/service-worker-events).

## Running this extension

1. Clone this repository.
2. Load this directory in Chrome as an [unpacked extension](https://developer.chrome.com/docs/extensions/mv3/getstarted/development-basics/#load-unpacked).
3. Type "api" in the omnibox followed by tab or space and select a suggestion.
4. Click on "Tip" button in the navigation bar.
