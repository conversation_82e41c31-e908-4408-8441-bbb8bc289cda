<!doctype html>
<html>
  <head>
    <link rel="stylesheet" type="text/css" href="index.css" />
  </head>
  <body>
    <div class="card">
      <fieldset>
        <legend>Settings</legend>
        <div>
          <label for="type">Summary Type:</label>
          <select id="type">
            <option value="key-points" selected>Key Points</option>
            <option value="tldr">TL;DR</option>
            <option value="teaser">Teaser</option>
            <option value="headline">Headline</option>
          </select>
        </div>
        <div>
          <label for="length">Length:</label>
          <select id="length">
            <option value="short" selected>Short</option>
            <option value="medium">Medium</option>
            <option value="long">Long</option>
          </select>
        </div>
        <div>
          <label for="format">Format:</label>
          <select id="format">
            <option value="markdown" selected>Markdown</option>
            <option value="plain-text">Plain text</option>
          </select>
        </div>
      </fieldset>
    </div>
    <div class="warning card" hidden id="warning"></div>
    <div class="card" id="summary">Nothing to show...</div>
    <script src="index.js" type="module"></script>
  </body>
</html>
