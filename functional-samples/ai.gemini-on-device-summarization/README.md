# On-device Summarization with Gemini Nano

This sample demonstrates how to use the experimental Summarization API in Chrome. To learn more about the API and how to sign-up for the preview, head over to the [summarizer guide on developer.chrome.com](https://developer.chrome.com/docs/ai/summarizer-api).

## Overview

The extension summarizes the content of the currently open tab. It uses Mozilla's [readability](https://github.com/mozilla/readability) library to extract the content of the currently active tab and displays a summary of the page generated by [Chrome's experimental summarization API](https://developer.chrome.com/blog/august2024-summarization-ai) in a side panel.

## Running this extension

1. Clone this repository
1. Run `npm install` in this folder to install all dependencies.
1. Run `npm run build` to build the extension.
1. Load the newly created `dist` directory in Chrome as an [unpacked extension](https://developer.chrome.com/docs/extensions/get-started/tutorial/hello-world#load-unpacked).
1. Click the extension icon to open the summary side panel.
1. Open any web page, the page's content summary will automatically be displayed in the side panel.

## Creating your own extension

If you use this sample as the foundation for your own extension, be sure to update the `"trial_tokens"` field [with your own origin trial token](https://developer.chrome.com/docs/web-platform/origin-trials#extensions) and to remove the `"key"` field in `manifest.json`.
