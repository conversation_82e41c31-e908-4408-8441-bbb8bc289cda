# Tab Manager For Chrome Dev Docs

This Chrome extension helps you manage and group your tabs related to Google Chrome Developer documentation. It organizes tabs into a group titled "DOCS" for easier navigation and enhanced productivity.

Purpose of this extension is to demonstrate [chrome.tabGroups](https://developer.chrome.com/docs/extensions/reference/api/tabGroups) API to interact with the browser's tab grouping system.

 ## Running This Extension

1. Clone this repository.
2. Load this directory in Chrome as an [unpacked extension](https://developer.chrome.com/docs/extensions/mv3/getstarted/development-basics/#load-unpacked).
3. Open these two URLs in separate tabs:  
   - [https://developer.chrome.com/docs/extensions](https://developer.chrome.com/docs/extensions)  
   - [https://developer.chrome.com/docs/extensions/reference/api](https://developer.chrome.com/docs/extensions/reference/api)
4. Click the extension icon in the Chrome toolbar, then select the "Tab Manager for Chrome Dev Docs" extension. A popup will appear having title "Google Dev Docs" and button named "Group Tabs". Click on the button to group the two tabs.
