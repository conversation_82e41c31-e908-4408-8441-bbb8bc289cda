# Oliver Focus Mode

Extension used in the [Debugging Chrome extensions](https://www.youtube.com/watch?v=Ta-YTDhiBIQ) DevTools Tips video. This extension simplifies the styling of the extensions and Chrome Web Store documentation pages so that they are easier to read when the action icon is clicked.

This extension is based on the [Focus Mode](/functional-samples/tutorial.focus-mode/) extension.

**Note:** `background.js` and `focus-mode.js` are intentionally broken. Try to fix them, and compare your fixes with the code in the `fixed` folder.

## Running this extension

1. Clone this repository.
2. Load this directory in Chrome as an [unpacked extension](https://developer.chrome.com/docs/extensions/mv3/getstarted/development-basics/#load-unpacked).
3. Visit a page in the Extensions or Chrome Web Store sections on developer.chrome.com, e.g https://developer.chrome.com/docs/extensions/mv3/getstarted/tut-focus-mode/.
4. Click the extension icon to toggle focus mode.
