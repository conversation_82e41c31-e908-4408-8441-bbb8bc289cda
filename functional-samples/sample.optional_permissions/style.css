/* Copyright 2022 Google LLC

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License. */

h1 {
  font-family: Verdana, Geneva, Tahoma, sans-serif;
}

#display_top {
  margin: auto;
  margin-bottom: 40px;
  width: 600px;
}

#todo_div {
  margin-top: 0px;
  height: 100px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  animation-name: color-extravaganza;
  animation-duration: 6s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes color-extravaganza {
  0% {
    background-color: #4285f4;
  }
  10% {
    background-color: #4285f4;
  }
  25% {
    background-color: #ea4335;
  }
  50% {
    background-color: #fbbc04;
  }
  100% {
    background-color: #34a853;
  }
}

.colorFun {
  position: relative;
  height: 70px;
  width: 100px;
  padding: 10px;
  display: inline-block;
  margin-top: 40px;
}

.colorFun .tooltip {
  width: 100px;
  text-align: center;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  bottom: 0%;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.imageContainer {
  background-color: #cdcdcd;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  justify-self: center;
}

.imageContainer img {
  width: 28px;
  height: 28px;
}

footer {
  position: absolute;
  bottom: 20px;
  left: 20px;
}

input#todo_value {
  width: 300px;
  height: 40px;
  font-size: 18px;
  padding: 12px 20px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  margin-right: 4px;
}

input#todo_value ~ input[type="submit"] {
  background-color: #4285F4;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

input#todo_value ~ input[type="submit"]:hover {
  background-color: #45a049;
}
