<!doctype html>
<html>
  <head>
    <title>Alt Text Generator</title>
    <style>
      @import 'https://unpkg.com/open-props';
      @import 'https://unpkg.com/open-props/normalize.min.css';
      @import 'https://unpkg.com/open-props/buttons.min.css';
      @import 'https://unpkg.com/open-props/theme.light.switch.min.css';
      @import 'https://unpkg.com/open-props/theme.dark.switch.min.css';

      :root {
        --font-size-00: 0.6rem;
      }
      body {
        margin: auto;
        padding: var(--size-2);
        width: 500px;
        padding: 10px;
      }
      h4 {
        margin-bottom: var(--size-2);
      }
      textarea {
        width: 100%;
        height: 100px;
      }
      button {
        margin-right: 5px;
      }
      #loading,
      textarea {
        margin: 16px 0;
        height: 200px;
      }
    </style>
  </head>
  <body>
    <h4>Alt Texter</h4>
    <label
      >Target language:
      <select id="lang">
        <option value="bn">Bengali</option>
        <option value="en" selected>English</option>
        <option value="de">German</option>
        <option value="fr">French</option>
        <option value="hi">Hindi</option>
        <option value="ja">Japanese</option>
        <option value="zh">Mandarin Chinese (Simplified)</option>
        <option value="pt">Portuguese</option>
        <option value="ru">Russian</option>
        <option value="es">Spanish</option>
        <option value="zh-Hant">Taiwanese Mandarin (Traditional)</option>
        <option value="tr">Turkish</option>
        <option value="vi">Vietnamese</option>
      </select>
    </label>
    <textarea id="loading">Generating alt text ...</textarea>
    <textarea id="altText" hidden></textarea>
    <button type="button" id="copyClose">Copy and close</button>
    <button type="reset" id="discard">Discard</button>
    <script src="popup.js"></script>
  </body>
</html>
