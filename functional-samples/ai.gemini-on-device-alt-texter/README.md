# Alt-texter: On-device multimodal AI with Gemini Nano - image understanding

This sample demonstrates how to use the image understanding capabilities of the multi-modal Gemini Nano API preview together with [Chrome's translation API](https://developer.chrome.com/docs/ai/translator-api). To learn more about the API and how to sign-up for the origin trial, head over to [Built-in AI on developer.chrome.com](https://developer.chrome.com/docs/extensions/ai/prompt-api).

## Overview

This extension adds a context menu entry for images on the web to generate an alt text description that is displayed in a popup window.

## Running this extension

1. Clone this repository.
1. Load this directory in Chrome as an [unpacked extension](https://developer.chrome.com/docs/extensions/get-started/tutorial/hello-world#load-unpacked).
1. Right click an image on a webpage and select "Generate alt text"
