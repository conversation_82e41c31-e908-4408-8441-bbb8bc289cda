# Testing service worker termination

## Overview

**Note:** The test extension is intentionally broken as part of a tutorial in
our documentation. See [Test service worker termination with P<PERSON>pet<PERSON>](https://developer.chrome.com/docs/extensions/how-to/test/test-serviceworker-termination-with-puppeteer).

Sample code to show how to terminate a service worker in Puppeteer or Selenium.

## Running the tests

1. Install [Node.JS](https://nodejs.org/).
2. Change to the `puppeteer` or `selenium` directory.
3. Run `npm install`.
4. Run `npm start`.
