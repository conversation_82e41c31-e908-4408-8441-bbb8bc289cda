export default [
  {
    content: 'commands',
    description:
      'Use the <match>Commands API</match> to add a keyboard shortcuts.'
  },
  {
    content: 'contextmenus',
    description:
      "Use the <match>ContextMenus API</match> to add a custom item to Chrome's context menu."
  },
  {
    content: 'declarativeNetRequest',
    description:
      'Use the <match>DeclarativeNetRequest API</match> to block or modify network requests.'
  },
  {
    content: 'downloads',
    description:
      'Use the <match>Downloads API</match> to programmatically manipulate downloads.'
  },
  {
    content: 'i18n',
    description: 'Use the <match>i18n API</match> to localize your extension'
  },
  {
    content: 'identity',
    description:
      'Use the <match>Identity API</match> to get OAuth2 access tokens.'
  },
  {
    content: 'notifications',
    description:
      'Use the <match>Notifications API</match> show notifications to users in the system tray.'
  },
  {
    content: 'offscreen',
    description:
      'Use the <match>Offscreen API</match> to create and manage offscreen documents.'
  },
  {
    content: 'omnibox',
    description:
      "Use the <match>Omnibox API</match> to register a keyword with Chrome's address bar."
  },
  {
    content: 'permissions',
    description:
      'Use the <match>Permissions API</match> to request optional permissions at run time.'
  },
  {
    content: 'runtime',
    description:
      'Use the <match>Runtime API</match> pass messages, manage extension lifecycle, and access other helper utils.'
  },
  {
    content: 'scripting',
    description:
      'Use the <match>Scripting API</match> to execute scripts in different contexts.'
  },
  {
    content: 'storage',
    description:
      'Use the <match>Storage API</match> to store, retrieve, and track changes to user data.'
  },
  {
    content: 'tabs',
    description:
      'Use the <match>Tabs API</match> to create, update and manipulate tabs.'
  },
  {
    content: 'topSites',
    description:
      'Use the <match>TopSites API</match> to access the most visited sites that are displayed on the new tab page.'
  },
  {
    content: 'webNavigation',
    description:
      'Use the <match>WebNavigation API</match> to receive notifications about the status of navigation requests in-flight.'
  }
];
