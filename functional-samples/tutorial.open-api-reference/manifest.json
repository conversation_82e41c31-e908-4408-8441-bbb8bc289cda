{"manifest_version": 3, "name": "Open extension API reference", "version": "1.0.0", "icons": {"16": "icon-16.png", "128": "icon-128.png"}, "background": {"service_worker": "service-worker.js", "type": "module"}, "minimum_chrome_version": "102", "omnibox": {"keyword": "api"}, "permissions": ["alarms", "storage"], "content_scripts": [{"matches": ["https://developer.chrome.com/docs/extensions/reference/*"], "js": ["content.js"]}], "host_permissions": ["https://extension-tips.glitch.me/*"]}