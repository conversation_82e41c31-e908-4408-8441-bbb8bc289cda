// 简单的文本节点测试脚本
// 可以在浏览器控制台直接运行

function simpleTextNodeTest() {
  console.log('=== 简单文本节点测试 ===');
  
  // 方法1：使用TreeWalker
  console.log('方法1：使用TreeWalker');
  const walker = document.createTreeWalker(
    document.body,
    NodeFilter.SHOW_TEXT,
    null,
    false
  );
  
  const nodes1 = [];
  let node;
  while (node = walker.nextNode()) {
    if (node.textContent.trim().length > 0) {
      nodes1.push(node);
    }
  }
  console.log(`TreeWalker找到 ${nodes1.length} 个文本节点`);
  
  // 方法2：递归遍历
  console.log('方法2：递归遍历');
  function getAllTextNodes(element) {
    const textNodes = [];
    
    function walk(node) {
      if (node.nodeType === 3) { // TEXT_NODE
        if (node.textContent.trim().length > 0) {
          textNodes.push(node);
        }
      } else {
        for (let child of node.childNodes) {
          walk(child);
        }
      }
    }
    
    walk(element);
    return textNodes;
  }
  
  const nodes2 = getAllTextNodes(document.body);
  console.log(`递归遍历找到 ${nodes2.length} 个文本节点`);
  
  // 显示前几个节点的内容
  console.log('前5个文本节点内容：');
  nodes2.slice(0, 5).forEach((node, index) => {
    console.log(`${index + 1}: "${node.textContent.trim().substring(0, 50)}..."`);
    console.log(`   父元素: ${node.parentElement.tagName}`);
  });
  
  return nodes2;
}

// 运行测试
simpleTextNodeTest();
