/* Copyright (c) 2012 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file. */

body.uber-frame {
  color: rgb(48, 57, 66);
  margin-inline-start: 155px;
}

html[dir='rtl'] body.uber-frame {
  /* Enable vertical scrollbar at all times in RTL to avoid visual glitches when
   * showing sub-pages that vertically overflow. */
  overflow-y: scroll;
}

/* TODO(dbeam): Remove .page class from overlays in settings so the junk below
 * isn't necessary. */
body.uber-frame #extension-settings.page,
body.uber-frame #mainview-content .page,
body.uber-frame .subpage-sheet-container .page,
body.uber-frame > .page {
  margin-inline-end: 24px;
  min-width: 576px;
  padding-bottom: 20px;
  padding-top: 55px;
}

body.uber-frame header {
  background-image: -webkit-linear-gradient(white,
                                            white 40%,
                                            rgba(255, 255, 255, 0.92));
  left: 155px;
  /* <section>s in options currently amount to 638px total, broken up into
   * 600px max-width + 18px padding-inline-start + 20px margin-inline-end
   * so we mirror this value here so the headers match width and horizontal
   * alignment when scrolling sideways. */
  max-width: 738px;
  min-width: 600px;
  position: fixed;
  right: 0;
  top: 0;
  /* list.css sets a z-index of up to 2, this is set to 3 to ensure that the
   * header is in front of the selected list item. */
  z-index: 3;
}

html[dir='rtl'] body.uber-frame header {
  left: 0;
  right: 155px;
}

body.uber-frame header > .search-field-container,
body.uber-frame header > .header-extras,
body.uber-frame header > button {
  position: absolute;
  right: 20px;
  top: 21px;
}

html[dir='rtl'] body.uber-frame header > .search-field-container,
html[dir='rtl'] body.uber-frame header > .header-extras,
html[dir='rtl'] body.uber-frame header > button {
  left: 20px;
  right: auto;
}

body.uber-frame header input[type='search'],
body.uber-frame header input[type='text'],
body.uber-frame header button {
  margin: 0;
}

body.uber-frame header > h1 {
  margin: 0;
  padding: 21px 0 13px;
}

/* Create a border under the h1 (but before anything that gets appended
 * to the end of the header). */
body.uber-frame header > h1::after {
  background-color: #eee;
  content: ' ';
  display: block;
  height: 1px;
  margin-inline-end: 20px;
  position: relative;
  top: 13px;
}

body.uber-frame footer {
  border-top: 1px solid #eee;
  margin-top: 16px;
  /* min-width and max-width should match the header */
  max-width: 638px;
  min-width: 600px;
  padding: 8px 0;
}

/* Sections are used in options pages, help page and history page. This defines
 * the section metrics to match the header metrics above. */
body.uber-frame section {
  margin-bottom: 24px;
  margin-top: 8px;
  max-width: 600px;
  padding-inline-start: 18px;
}

body.uber-frame section:last-of-type {
  margin-bottom: 0;
}

body.uber-frame section > h3 {
  margin-inline-start: -18px;
}

body.uber-frame section > div:only-of-type {
  -webkit-box-flex: 1;
}
