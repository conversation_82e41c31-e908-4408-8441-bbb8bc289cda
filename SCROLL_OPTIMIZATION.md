# 滚动优化功能说明

## 🚀 新功能特性

### 1. 视窗优化处理
- **初始加载**：只处理当前视窗内可见的文本内容
- **滚动触发**：当用户滚动页面时，自动处理新出现的内容
- **性能优化**：避免一次性处理整个页面，提高响应速度

### 2. 智能重复检测
- **已处理标记**：记录已处理的文本节点，避免重复处理
- **内容识别**：通过文本内容和父元素标签组合生成唯一ID
- **状态持久化**：在页面会话期间保持处理状态

### 3. 滚动监听机制
- **防抖处理**：500ms延迟，避免频繁触发API调用
- **视窗检测**：提前100px开始处理，延后100px停止处理
- **事件优化**：使用passive事件监听器，不阻塞滚动性能

## 🔧 技术实现

### 视窗检测算法
```javascript
function isElementInViewport(element) {
  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;
  
  return (
    rect.top >= -100 && // 提前100px开始处理
    rect.left >= -100 &&
    rect.bottom <= windowHeight + 100 && // 延后100px停止处理
    rect.right <= windowWidth + 100
  );
}
```

### 重复检测机制
```javascript
// 使用WeakSet直接存储节点对象引用
window.processedTextNodes = new WeakSet();
// 使用Set存储节点路径ID作为备用
window.processedNodeIds = new Set();

function isNodeProcessed(textNode) {
  return window.processedTextNodes.has(textNode) ||
         window.processedNodeIds.has(generateNodeId(textNode));
}

function markNodeAsProcessed(textNode) {
  window.processedTextNodes.add(textNode);
  window.processedNodeIds.add(generateNodeId(textNode));
}
```

### 滚动事件处理
```javascript
let scrollTimeout;
const handleScroll = async () => {
  clearTimeout(scrollTimeout);
  scrollTimeout = setTimeout(async () => {
    const newVisibleTextNodes = getVisibleTextNodes(document.body);
    await processTextNodes(newVisibleTextNodes, '滚动后新出现的');
  }, 500);
};
```

## 📊 性能优势

### 处理速度对比
- **原版本**：一次性处理所有文本节点（可能数百个）
- **优化版本**：只处理视窗内文本节点（通常10-30个）

### 内存使用
- **状态管理**：使用Set存储已处理节点ID，内存占用小
- **事件监听**：passive监听器，不影响滚动性能
- **API调用**：按需调用，减少不必要的网络请求

### 用户体验
- **即时响应**：页面加载后立即处理可见内容
- **流畅滚动**：滚动过程不卡顿
- **渐进处理**：内容按需处理，用户感知更好

## 🧪 测试方法

### 1. 基础功能测试
1. 打开 `test.html` 文件
2. 点击扩展图标
3. 观察只有视窗内的内容被处理

### 2. 滚动功能测试
1. 向下滚动页面
2. 观察控制台输出 "检测到滚动，处理新出现的内容"
3. 新出现的文本应该被自动处理

### 3. 重复处理测试
1. 滚动到已处理的区域
2. 观察控制台输出 "没有新的文本节点需要处理"
3. 确认不会重复处理相同内容

## 🔍 调试信息

### 控制台输出说明
- `📝 Starting to get visible text nodes...` - 开始获取可见文本节点
- `📊 Found X visible text nodes to process` - 找到X个可见文本节点
- `✅ 初始视窗内处理 X 个新的文本节点` - 处理初始可见内容
- `📜 检测到滚动，处理新出现的内容...` - 滚动事件触发
- `⏭️ 滚动后新出现的没有新的文本节点需要处理` - 没有新内容需要处理
- `⏸️ 已有处理任务在进行中，跳过` - 防止并发处理

## ⚙️ 配置选项

### 视窗边距调整
在 `isElementInViewport` 函数中修改边距值：
```javascript
rect.top >= -100 && // 提前处理距离
rect.bottom <= windowHeight + 100 // 延后处理距离
```

### 滚动延迟调整
在 `handleScroll` 函数中修改延迟时间：
```javascript
setTimeout(async () => {
  // 处理逻辑
}, 500); // 修改这个值（毫秒）
```

### API调用间隔
在 `processTextNodes` 函数中修改延迟：
```javascript
await new Promise(resolve => setTimeout(resolve, 100)); // 修改这个值
```
