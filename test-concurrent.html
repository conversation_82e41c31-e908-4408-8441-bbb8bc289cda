<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>并发处理测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .text-block {
            margin: 15px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #007cba;
        }
        .performance-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007cba;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Chrome扩展并发处理测试页面</h1>
        
        <div class="performance-info">
            <h3>📊 性能优化说明</h3>
            <p>本页面用于测试新的异步并发处理功能。相比之前的串行处理，新版本具有以下优势：</p>
            <ul>
                <li><strong>并发处理</strong>：同时处理多个文本节点，大幅提升处理速度</li>
                <li><strong>超时控制</strong>：每个API请求设置10秒超时，避免单个请求卡死</li>
                <li><strong>并发限制</strong>：最多同时3个请求，避免服务器过载</li>
                <li><strong>错误隔离</strong>：单个请求失败不影响其他请求</li>
                <li><strong>实时统计</strong>：显示处理进度和成功率</li>
            </ul>
        </div>

        <div class="warning">
            <strong>⚠️ 测试说明：</strong>
            <ol>
                <li>确保本地API服务器运行在 <code>http://localhost:8000/translate</code></li>
                <li>点击Chrome扩展图标开始处理</li>
                <li>打开浏览器控制台查看详细日志</li>
                <li>观察页面文本的实时变化</li>
            </ol>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">50+</div>
                <div>测试文本节点</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div>最大并发数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10s</div>
                <div>请求超时时间</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">500ms</div>
                <div>滚动防抖延迟</div>
            </div>
        </div>

        <div class="section">
            <h2>📝 第一部分：基础文本测试</h2>
            <div class="text-block">这是第一个测试文本块。它包含了一些基本的中文内容，用于测试API的基本翻译功能。</div>
            <div class="text-block">这里是另一个文本块，内容稍有不同。我们希望看到每个文本块都能被正确处理。</div>
            <div class="text-block">第三个文本块包含了更多的内容，用于测试较长文本的处理能力和API的响应时间。</div>
            <div class="text-block">这是一个包含特殊字符的文本：Hello World! 你好世界！123456</div>
            <div class="text-block">测试文本的多样性很重要，这样可以确保我们的处理逻辑在各种情况下都能正常工作。</div>
        </div>

        <div class="section">
            <h2>🔄 第二部分：并发处理测试</h2>
            <div class="text-block">并发处理的第一个测试文本。这个文本应该与其他文本同时被处理。</div>
            <div class="text-block">并发处理的第二个测试文本。观察控制台可以看到多个请求同时发出。</div>
            <div class="text-block">并发处理的第三个测试文本。新的实现应该比串行处理快很多。</div>
            <div class="text-block">并发处理的第四个测试文本。每个请求都有独立的超时控制。</div>
            <div class="text-block">并发处理的第五个测试文本。即使某个请求失败，其他请求也会继续。</div>
            <div class="text-block">并发处理的第六个测试文本。最终会显示详细的处理统计信息。</div>
        </div>

        <div class="section">
            <h2>⏱️ 第三部分：性能测试</h2>
            <div class="text-block">性能测试文本1：这个部分包含更多的文本节点，用于测试大量并发请求的处理能力。</div>
            <div class="text-block">性能测试文本2：观察处理时间和成功率，新版本应该显著提升用户体验。</div>
            <div class="text-block">性能测试文本3：即使API响应较慢，页面也不应该出现卡死现象。</div>
            <div class="text-block">性能测试文本4：并发控制确保不会对服务器造成过大压力。</div>
            <div class="text-block">性能测试文本5：超时机制保证单个慢请求不会影响整体处理。</div>
            <div class="text-block">性能测试文本6：错误处理机制确保系统的稳定性和可靠性。</div>
            <div class="text-block">性能测试文本7：实时统计帮助开发者了解处理效果和性能表现。</div>
            <div class="text-block">性能测试文本8：这是最后一个测试文本，完成后会显示完整的处理报告。</div>
        </div>

        <div class="section">
            <h2>📜 第四部分：滚动测试区域</h2>
            <p>向下滚动页面，观察新出现的内容是否会被自动处理：</p>
            <div style="height: 800px; background: linear-gradient(to bottom, #f0f0f0, #e0e0e0); display: flex; align-items: center; justify-content: center; font-size: 24px; color: #666;">
                滚动区域 - 继续向下滚动
            </div>
            <div class="text-block">滚动后出现的文本1：这个文本只有在滚动到这里时才会被处理。</div>
            <div class="text-block">滚动后出现的文本2：测试滚动监听器的工作效果。</div>
            <div class="text-block">滚动后出现的文本3：新的并发处理机制在滚动场景下也应该工作良好。</div>
            <div style="height: 500px; background: linear-gradient(to bottom, #e0e0e0, #d0d0d0); display: flex; align-items: center; justify-content: center; font-size: 18px; color: #666;">
                更多滚动区域
            </div>
            <div class="text-block">最底部的测试文本1：这些文本位于页面底部，测试完整的滚动处理流程。</div>
            <div class="text-block">最底部的测试文本2：确保所有可见文本都能被正确识别和处理。</div>
            <div class="text-block">最底部的测试文本3：这是最后的测试文本，完成整个测试流程。</div>
        </div>

        <div class="performance-info">
            <h3>🎯 预期结果</h3>
            <ul>
                <li>页面加载后，只有可见区域的文本被立即处理</li>
                <li>多个文本节点同时处理，而不是逐个等待</li>
                <li>控制台显示并发处理的详细日志</li>
                <li>滚动时新出现的文本自动处理</li>
                <li>即使API响应慢，页面也不会卡死</li>
                <li>显示处理统计：成功数量、失败数量、总耗时等</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🎉 测试页面加载完成！');
            console.log('📋 页面包含约 ' + document.querySelectorAll('.text-block').length + ' 个测试文本块');
            console.log('🔧 请点击Chrome扩展图标开始测试并发处理功能');
        });
    </script>
</body>
</html>
