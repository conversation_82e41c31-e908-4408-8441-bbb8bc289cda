<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点跟踪测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .text-block {
            margin: 15px 0;
            padding: 10px;
            background: #fff;
            border-left: 4px solid #007cba;
            border-radius: 3px;
        }
        .duplicate-test {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .info-box {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007cba;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #005a8b;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 节点跟踪机制测试页面</h1>
        
        <div class="info-box">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试新的节点跟踪机制，验证以下功能：</p>
            <ul>
                <li><strong>WeakSet跟踪</strong>：直接使用节点对象引用进行跟踪</li>
                <li><strong>路径ID备用</strong>：基于DOM路径生成唯一ID作为备用方案</li>
                <li><strong>重复检测</strong>：确保相同节点不会被重复处理</li>
                <li><strong>动态内容</strong>：测试动态添加的内容是否正确跟踪</li>
            </ul>
        </div>

        <div class="warning-box">
            <strong>⚠️ 测试步骤：</strong>
            <ol>
                <li>点击Chrome扩展图标进行第一次处理</li>
                <li>观察控制台输出的节点跟踪日志</li>
                <li>再次点击扩展图标，验证不会重复处理</li>
                <li>使用下方按钮动态添加内容进行测试</li>
                <li>检查控制台确认新内容被正确处理</li>
            </ol>
        </div>

        <div class="test-controls">
            <h3>🧪 测试控制</h3>
            <button class="btn" onclick="addDynamicContent()">添加动态内容</button>
            <button class="btn btn-secondary" onclick="duplicateExistingContent()">复制现有内容</button>
            <button class="btn btn-secondary" onclick="clearConsole()">清空控制台</button>
            <button class="btn btn-secondary" onclick="showTrackingStats()">显示跟踪统计</button>
        </div>

        <div class="test-section">
            <h2>📝 第一组：基础文本测试</h2>
            <div class="text-block" id="text-1">
                这是第一个测试文本。它有一个唯一的ID，应该被正确跟踪。
            </div>
            <div class="text-block" class="test-class">
                这是第二个测试文本。它有CSS类名，路径生成应该包含类信息。
            </div>
            <div class="text-block">
                这是第三个测试文本。它没有特殊属性，使用基本的路径跟踪。
            </div>
        </div>

        <div class="test-section">
            <h2>🔄 第二组：重复内容测试</h2>
            <div class="text-block duplicate-test">
                重复测试文本：这个文本会在页面中出现多次，测试路径区分能力。
            </div>
            <div class="text-block duplicate-test">
                重复测试文本：这个文本会在页面中出现多次，测试路径区分能力。
            </div>
            <div class="text-block duplicate-test">
                不同的文本内容，但在相似的DOM结构中，应该被区分开来。
            </div>
        </div>

        <div class="test-section">
            <h2>🏗️ 第三组：复杂结构测试</h2>
            <div class="complex-container">
                <div class="nested-level-1">
                    <div class="nested-level-2">
                        <span class="text-block">深层嵌套的文本节点，测试复杂路径生成。</span>
                    </div>
                </div>
                <div class="nested-level-1">
                    <div class="nested-level-2">
                        <span class="text-block">另一个深层嵌套的文本节点，路径应该不同。</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 第四组：边界情况测试</h2>
            <div class="text-block">
                <strong>包含HTML标签的文本</strong>，测试混合内容处理。
            </div>
            <div class="text-block" title="有title属性">
                带有属性的元素中的文本，路径生成应该稳定。
            </div>
            <div class="text-block">
                很长的文本内容，超过50个字符的部分应该被截断用于生成节点ID，但不影响实际的文本处理功能。这是一个测试长文本处理的例子。
            </div>
        </div>

        <div id="dynamic-content-area" class="test-section">
            <h2>⚡ 第五组：动态内容区域</h2>
            <p>这里会动态添加内容，测试新节点的跟踪机制。</p>
        </div>

        <div class="info-box">
            <h3>🔍 预期行为</h3>
            <ul>
                <li><strong>首次处理</strong>：所有文本节点都会被处理，控制台显示跟踪日志</li>
                <li><strong>重复点击</strong>：显示"没有新的文本节点需要处理"</li>
                <li><strong>动态内容</strong>：新添加的内容会被处理，已存在的内容不会重复处理</li>
                <li><strong>路径区分</strong>：相同文本在不同位置会被正确区分</li>
                <li><strong>内存效率</strong>：WeakSet自动清理不再引用的节点</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>📈 关键日志标识</h3>
            <ul>
                <li><code>🏷️ 标记节点为已处理</code> - 节点被成功标记</li>
                <li><code>⏭️ Skipped ... (already processed)</code> - 检测到重复处理</li>
                <li><code>🔄 Processing text node</code> - 开始处理新节点</li>
                <li><code>✅ Replaced</code> - 节点处理成功</li>
            </ul>
        </div>
    </div>

    <script>
        let dynamicCounter = 1;

        function addDynamicContent() {
            const area = document.getElementById('dynamic-content-area');
            const newDiv = document.createElement('div');
            newDiv.className = 'text-block';
            newDiv.innerHTML = `动态添加的文本 ${dynamicCounter}：这是通过JavaScript动态创建的内容，应该被新的跟踪机制正确处理。`;
            area.appendChild(newDiv);
            dynamicCounter++;
            console.log('🆕 添加了新的动态内容');
        }

        function duplicateExistingContent() {
            const area = document.getElementById('dynamic-content-area');
            const existingText = document.querySelector('.text-block').cloneNode(true);
            existingText.innerHTML = '复制的现有内容：' + existingText.innerHTML;
            area.appendChild(existingText);
            console.log('📋 复制了现有内容');
        }

        function clearConsole() {
            console.clear();
            console.log('🧹 控制台已清空，准备新的测试');
        }

        function showTrackingStats() {
            if (window.processedTextNodes && window.processedNodeIds) {
                console.log('📊 跟踪统计信息:');
                console.log(`  - WeakSet中的节点数量: ${window.processedTextNodes.size || '无法直接获取WeakSet大小'}`);
                console.log(`  - 节点ID Set大小: ${window.processedNodeIds.size}`);
                console.log(`  - 已处理的节点ID列表:`, Array.from(window.processedNodeIds));
            } else {
                console.log('⚠️ 跟踪数据尚未初始化，请先运行扩展');
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🎉 节点跟踪测试页面加载完成！');
            console.log('📋 页面包含 ' + document.querySelectorAll('.text-block').length + ' 个测试文本块');
            console.log('🔧 请点击Chrome扩展图标开始测试节点跟踪功能');
            console.log('💡 使用页面上的按钮进行动态内容测试');
        });

        // 监听DOM变化，用于测试
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    console.log('👀 检测到DOM变化，新增了节点');
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
