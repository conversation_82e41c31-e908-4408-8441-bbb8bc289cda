// Generated by dart2js, the Dart to JavaScript compiler version: 1.1.1.
// The code supports the following hooks:
// dartPrint(message)   - if this function is defined it is called
//                        instead of the Dart [print] method.
// dartMainRunner(main) - if this function is defined, the Dart [main]
//                        method will not be invoked directly.
//                        Instead, a closure that will invoke [main] is
//                        passed to [dartMainRunner].
(function($) {
function dart() {}var A = new dart;
delete A.x;
var B = new dart;
delete B.x;
var C = new dart;
delete C.x;
var D = new dart;
delete D.x;
var E = new dart;
delete E.x;
var F = new dart;
delete F.x;
var G = new dart;
delete G.x;
var H = new dart;
delete H.x;
var J = new dart;
delete J.x;
var K = new dart;
delete K.x;
var L = new dart;
delete L.x;
var M = new dart;
delete M.x;
var N = new dart;
delete N.x;
var O = new dart;
delete O.x;
var P = new dart;
delete P.x;
var Q = new dart;
delete Q.x;
var R = new dart;
delete R.x;
var S = new dart;
delete S.x;
var T = new dart;
delete T.x;
var U = new dart;
delete U.x;
var V = new dart;
delete V.x;
var W = new dart;
delete W.x;
var X = new dart;
delete X.x;
var Y = new dart;
delete Y.x;
var Z = new dart;
delete Z.x;
function Isolate() {}
init();

$ = Isolate.$isolateProperties;
var $$ = {};

// Native classes
(function (reflectionData) {
  "use strict";
  function map(x){x={x:x};delete x.x;return x}
    function processStatics(descriptor) {
      for (var property in descriptor) {
        if (!hasOwnProperty.call(descriptor, property)) continue;
        if (property === "") continue;
        var element = descriptor[property];
        var firstChar = property.substring(0, 1);
        var previousProperty;
        if (firstChar === "+") {
          mangledGlobalNames[previousProperty] = property.substring(1);
          if (descriptor[property] == 1) descriptor[previousProperty].$reflectable = 1;
          if (element && element.length) init.typeInformation[previousProperty] = element;
        } else if (firstChar === "@") {
          property = property.substring(1);
          $[property]["@"] = element;
        } else if (firstChar === "*") {
          globalObject[previousProperty].$defaultValues = element;
          var optionalMethods = descriptor.$methodsWithOptionalArguments;
          if (!optionalMethods) {
            descriptor.$methodsWithOptionalArguments = optionalMethods = {}
          }
          optionalMethods[property] = previousProperty;
        } else if (typeof element === "function") {
          globalObject[previousProperty = property] = element;
          functions.push(property);
          init.globalFunctions[property] = element;
        } else if (element.constructor === Array) {
          addStubs(globalObject, element, property, true, descriptor, functions);
        } else {
          previousProperty = property;
          var newDesc = {};
          var previousProp;
          for (var prop in element) {
            if (!hasOwnProperty.call(element, prop)) continue;
            firstChar = prop.substring(0, 1);
            if (prop === "static") {
              processStatics(init.statics[property] = element[prop]);
            } else if (firstChar === "+") {
              mangledNames[previousProp] = prop.substring(1);
              if (element[prop] == 1) element[previousProp].$reflectable = 1;
            } else if (firstChar === "@" && prop !== "@") {
              newDesc[prop.substring(1)]["@"] = element[prop];
            } else if (firstChar === "*") {
              newDesc[previousProp].$defaultValues = element[prop];
              var optionalMethods = newDesc.$methodsWithOptionalArguments;
              if (!optionalMethods) {
                newDesc.$methodsWithOptionalArguments = optionalMethods={}
              }
              optionalMethods[prop] = previousProp;
            } else {
              var elem = element[prop];
              if (prop && elem != null && elem.constructor === Array && prop !== "<>") {
                addStubs(newDesc, elem, prop, false, element, []);
              } else {
                newDesc[previousProp = prop] = elem;
              }
            }
          }
          $$[property] = [globalObject, newDesc];
          classes.push(property);
        }
      }
    }
  function addStubs(descriptor, array, name, isStatic, originalDescriptor, functions) {
    var f, funcs = [originalDescriptor[name] = descriptor[name] = f = (function() {
  var result = array[0];
  if (result != null && typeof result != "function") {
    throw new Error(
        name + ": expected value of type 'function' at index " + (0) +
        " but got " + (typeof result));
  }
  return result;
})()];
    f.$stubName = name;
    functions.push(name);
    for (var index = 0; index < array.length; index += 2) {
      f = array[index + 1];
      if (typeof f != "function") break;
      f.$stubName = (function() {
  var result = array[index + 2];
  if (result != null && typeof result != "string") {
    throw new Error(
        name + ": expected value of type 'string' at index " + (index + 2) +
        " but got " + (typeof result));
  }
  return result;
})();
      funcs.push(f);
      if (f.$stubName) {
        originalDescriptor[f.$stubName] = descriptor[f.$stubName] = f;
        functions.push(f.$stubName);
      }
    }
    for (var i = 0; i < funcs.length; index++, i++) {
      funcs[i].$callName = (function() {
  var result = array[index + 1];
  if (result != null && typeof result != "string") {
    throw new Error(
        name + ": expected value of type 'string' at index " + (index + 1) +
        " but got " + (typeof result));
  }
  return result;
})();
    }
    var getterStubName = (function() {
  var result = array[++index];
  if (result != null && typeof result != "string") {
    throw new Error(
        name + ": expected value of type 'string' at index " + (++index) +
        " but got " + (typeof result));
  }
  return result;
})();
    array = array.slice(++index);
    var requiredParameterInfo = (function() {
  var result = array[0];
  if (result != null && (typeof result != "number" || (result|0) !== result)) {
    throw new Error(
        name + ": expected value of type 'int' at index " + (0) +
        " but got " + (typeof result));
  }
  return result;
})();
    var requiredParameterCount = requiredParameterInfo >> 1;
    var isAccessor = (requiredParameterInfo & 1) === 1;
    var isSetter = requiredParameterInfo === 3;
    var isGetter = requiredParameterInfo === 1;
    var optionalParameterInfo = (function() {
  var result = array[1];
  if (result != null && (typeof result != "number" || (result|0) !== result)) {
    throw new Error(
        name + ": expected value of type 'int' at index " + (1) +
        " but got " + (typeof result));
  }
  return result;
})();
    var optionalParameterCount = optionalParameterInfo >> 1;
    var optionalParametersAreNamed = (optionalParameterInfo & 1) === 1;
    var isIntercepted = requiredParameterCount + optionalParameterCount != funcs[0].length;
    var functionTypeIndex = (function() {
  var result = array[2];
  if (result != null && (typeof result != "number" || (result|0) !== result) && typeof result != "function") {
    throw new Error(
        name + ": expected value of type 'function or int' at index " + (2) +
        " but got " + (typeof result));
  }
  return result;
})();
    var isReflectable = array.length > requiredParameterCount + optionalParameterCount + 3;
    if (getterStubName) {
      f = tearOff(funcs, array, isStatic, name, isIntercepted);
      if (isStatic) init.globalFunctions[name] = f;
      originalDescriptor[getterStubName] = descriptor[getterStubName] = f;
      funcs.push(f);
      if (getterStubName) functions.push(getterStubName);
      f.$stubName = getterStubName;
      f.$callName = null;
    }
    if (isReflectable) {
      for (var i = 0; i < funcs.length; i++) {
        funcs[i].$reflectable = 1;
        funcs[i].$reflectionInfo = array;
      }
    }
    if (isReflectable) {
      var unmangledNameIndex = optionalParameterCount * 2 + requiredParameterCount + 3;
      var unmangledName = (function() {
  var result = array[unmangledNameIndex];
  if (result != null && typeof result != "string") {
    throw new Error(
        name + ": expected value of type 'string' at index " + (unmangledNameIndex) +
        " but got " + (typeof result));
  }
  return result;
})();
      var reflectionName = unmangledName + ":" + requiredParameterCount + ":" + optionalParameterCount;
      if (isGetter) {
        reflectionName = unmangledName;
      } else if (isSetter) {
        reflectionName = unmangledName + "=";
      }
      if (isStatic) {
        init.mangledGlobalNames[name] = reflectionName;
      } else {
        init.mangledNames[name] = reflectionName;
      }
      funcs[0].$reflectionName = reflectionName;
      funcs[0].$metadataIndex = unmangledNameIndex + 1;
      if (optionalParameterCount) descriptor[unmangledName + "*"] = funcs[0];
    }
  }
  function tearOffGetterNoCsp(funcs, reflectionInfo, name, isIntercepted) {
    return isIntercepted
        ? new Function("funcs", "reflectionInfo", "name", "H", "c",
            "return function tearOff_" + name + (functionCounter++)+ "(x) {" +
              "if (c === null) c = H.closureFromTearOff(" +
                  "this, funcs, reflectionInfo, false, [x], name);" +
              "return new c(this, funcs[0], x, name);" +
            "}")(funcs, reflectionInfo, name, H, null)
        : new Function("funcs", "reflectionInfo", "name", "H", "c",
            "return function tearOff_" + name + (functionCounter++)+ "() {" +
              "if (c === null) c = H.closureFromTearOff(" +
                  "this, funcs, reflectionInfo, false, [], name);" +
              "return new c(this, funcs[0], null, name);" +
            "}")(funcs, reflectionInfo, name, H, null)
  }
  function tearOffGetterCsp(funcs, reflectionInfo, name, isIntercepted) {
    var cache = null;
    return isIntercepted
        ? function(x) {
            if (cache === null) cache = H.closureFromTearOff(this, funcs, reflectionInfo, false, [x], name);
            return new cache(this, funcs[0], x, name)
          }
        : function() {
            if (cache === null) cache = H.closureFromTearOff(this, funcs, reflectionInfo, false, [], name);
            return new cache(this, funcs[0], null, name)
          }
  }
  function tearOff(funcs, reflectionInfo, isStatic, name, isIntercepted) {
    var cache;
    return isStatic
        ? function() {
            if (cache === void 0) cache = H.closureFromTearOff(this, funcs, reflectionInfo, true, [], name).prototype;
            return cache;
          }
        : tearOffGetter(funcs, reflectionInfo, name, isIntercepted);
  }
  var functionCounter = 0;
  var tearOffGetter = (typeof dart_precompiled == "function")
      ? tearOffGetterCsp : tearOffGetterNoCsp;
  if (!init.libraries) init.libraries = [];
  if (!init.mangledNames) init.mangledNames = map();
  if (!init.mangledGlobalNames) init.mangledGlobalNames = map();
  if (!init.statics) init.statics = map();
  if (!init.typeInformation) init.typeInformation = map();
  if (!init.globalFunctions) init.globalFunctions = map();
  var libraries = init.libraries;
  var mangledNames = init.mangledNames;
  var mangledGlobalNames = init.mangledGlobalNames;
  var hasOwnProperty = Object.prototype.hasOwnProperty;
  var length = reflectionData.length;
  for (var i = 0; i < length; i++) {
    var data = reflectionData[i];
    var name = data[0];
    var uri = data[1];
    var metadata = data[2];
    var globalObject = data[3];
    var descriptor = data[4];
    var isRoot = !!data[5];
    var fields = descriptor && descriptor[""];
    var classes = [];
    var functions = [];
    processStatics(descriptor);
    libraries.push([name, uri, classes, functions, metadata, fields, isRoot,
                    globalObject]);
  }
})
([
["_foreign_helper", "dart:_foreign_helper", , H, {
  "": "",
  JS_CONST: {
    "": "Object;code"
  }
}],
["_interceptors", "dart:_interceptors", , J, {
  "": "",
  getInterceptor: function(object) {
    return void 0;
  },
  makeDispatchRecord: function(interceptor, proto, extension, indexability) {
    return {i: interceptor, p: proto, e: extension, x: indexability};
  },
  getNativeInterceptor: function(object) {
    var record, proto, objectProto, interceptor;
    record = object[init.dispatchPropertyName];
    if (record == null)
      if ($.initNativeDispatchFlag == null) {
        H.initNativeDispatch();
        record = object[init.dispatchPropertyName];
      }
    if (record != null) {
      proto = record.p;
      if (false === proto)
        return record.i;
      if (true === proto)
        return object;
      objectProto = Object.getPrototypeOf(object);
      if (proto === objectProto)
        return record.i;
      if (record.e === objectProto)
        throw H.wrapException(P.UnimplementedError$("Return interceptor for " + H.S(proto(object, record))));
    }
    interceptor = H.lookupAndCacheInterceptor(object);
    if (interceptor == null)
      return C.UnknownJavaScriptObject_methods;
    return interceptor;
  },
  Interceptor: {
    "": "Object;",
    $eq: function(receiver, other) {
      return receiver === other;
    },
    get$hashCode: function(receiver) {
      return H.Primitives_objectHashCode(receiver);
    },
    toString$0: function(receiver) {
      return H.Primitives_objectToString(receiver);
    },
    "%": "DOMError|FileError|MediaError|MediaKeyError|Navigator|NavigatorUserMediaError|PositionError|SQLError|SVGAnimatedLength|SVGAnimatedLengthList|SVGAnimatedNumber|SVGAnimatedNumberList"
  },
  JSBool: {
    "": "bool/Interceptor;",
    toString$0: function(receiver) {
      return String(receiver);
    },
    get$hashCode: function(receiver) {
      return receiver ? 519018 : 218159;
    },
    $isbool: true
  },
  JSNull: {
    "": "Interceptor;",
    $eq: function(receiver, other) {
      return null == other;
    },
    toString$0: function(receiver) {
      return "null";
    },
    get$hashCode: function(receiver) {
      return 0;
    }
  },
  JavaScriptObject: {
    "": "Interceptor;",
    get$hashCode: function(_) {
      return 0;
    }
  },
  PlainJavaScriptObject: {
    "": "JavaScriptObject;"
  },
  UnknownJavaScriptObject: {
    "": "JavaScriptObject;"
  },
  JSArray: {
    "": "List/Interceptor;",
    forEach$1: function(receiver, f) {
      return H.IterableMixinWorkaround_forEach(receiver, f);
    },
    elementAt$1: function(receiver, index) {
      if (index < 0 || index >= receiver.length)
        return H.ioore(receiver, index);
      return receiver[index];
    },
    toString$0: function(receiver) {
      return H.IterableMixinWorkaround_toStringIterable(receiver, "[", "]");
    },
    get$iterator: function(receiver) {
      return new H.ListIterator(receiver, receiver.length, 0, null);
    },
    get$hashCode: function(receiver) {
      return H.Primitives_objectHashCode(receiver);
    },
    get$length: function(receiver) {
      return receiver.length;
    },
    set$length: function(receiver, newLength) {
      if (newLength < 0)
        throw H.wrapException(P.RangeError$value(newLength));
      if (!!receiver.fixed$length)
        H.throwExpression(P.UnsupportedError$("set length"));
      receiver.length = newLength;
    },
    $index: function(receiver, index) {
      if (typeof index !== "number" || Math.floor(index) !== index)
        throw H.wrapException(new P.ArgumentError(index));
      if (index >= receiver.length || index < 0)
        throw H.wrapException(P.RangeError$value(index));
      return receiver[index];
    },
    $indexSet: function(receiver, index, value) {
      if (!!receiver.immutable$list)
        H.throwExpression(P.UnsupportedError$("indexed set"));
      if (typeof index !== "number" || Math.floor(index) !== index)
        throw H.wrapException(new P.ArgumentError(index));
      if (index >= receiver.length || index < 0)
        throw H.wrapException(P.RangeError$value(index));
      receiver[index] = value;
    },
    $isList: true,
    $asList: null,
    $isList: true,
    static: {JSArray_JSArray$fixed: function($length, $E) {
        var t1;
        if (typeof $length !== "number" || Math.floor($length) !== $length || $length < 0)
          throw H.wrapException(P.ArgumentError$("Length must be a non-negative integer: " + H.S($length)));
        t1 = H.setRuntimeTypeInfo(new Array($length), [$E]);
        t1.fixed$length = init;
        return t1;
      }}
  },
  JSNumber: {
    "": "num/Interceptor;",
    remainder$1: function(receiver, b) {
      return receiver % b;
    },
    toInt$0: function(receiver) {
      var t1;
      if (receiver >= -2147483648 && receiver <= 2147483647)
        return receiver | 0;
      if (isFinite(receiver)) {
        t1 = receiver < 0 ? Math.ceil(receiver) : Math.floor(receiver);
        return t1 + 0;
      }
      throw H.wrapException(P.UnsupportedError$('' + receiver));
    },
    roundToDouble$0: function(receiver) {
      if (receiver < 0)
        return -Math.round(-receiver);
      else
        return Math.round(receiver);
    },
    toString$0: function(receiver) {
      if (receiver === 0 && 1 / receiver < 0)
        return "-0.0";
      else
        return "" + receiver;
    },
    get$hashCode: function(receiver) {
      return receiver & 0x1FFFFFFF;
    },
    $add: function(receiver, other) {
      return receiver + other;
    },
    $sub: function(receiver, other) {
      if (typeof other !== "number")
        throw H.wrapException(new P.ArgumentError(other));
      return receiver - other;
    },
    _tdivFast$1: function(receiver, other) {
      return (receiver | 0) === receiver ? receiver / other | 0 : this.toInt$0(receiver / other);
    },
    _shrOtherPositive$1: function(receiver, other) {
      var t1;
      if (receiver > 0)
        t1 = other > 31 ? 0 : receiver >>> other;
      else {
        t1 = other > 31 ? 31 : other;
        t1 = receiver >> t1 >>> 0;
      }
      return t1;
    },
    $lt: function(receiver, other) {
      if (typeof other !== "number")
        throw H.wrapException(P.ArgumentError$(other));
      return receiver < other;
    },
    $ge: function(receiver, other) {
      if (typeof other !== "number")
        throw H.wrapException(P.ArgumentError$(other));
      return receiver >= other;
    },
    $isnum: true,
    static: {"": "JSNumber__MIN_INT32,JSNumber__MAX_INT32"}
  },
  JSInt: {
    "": "int/JSNumber;",
    $isdouble: true,
    $isnum: true,
    $isint: true
  },
  JSDouble: {
    "": "double/JSNumber;",
    $isdouble: true,
    $isnum: true
  },
  JSString: {
    "": "String/Interceptor;",
    codeUnitAt$1: function(receiver, index) {
      if (index < 0)
        throw H.wrapException(P.RangeError$value(index));
      if (index >= receiver.length)
        throw H.wrapException(P.RangeError$value(index));
      return receiver.charCodeAt(index);
    },
    $add: function(receiver, other) {
      if (typeof other !== "string")
        throw H.wrapException(new P.ArgumentError(other));
      return receiver + other;
    },
    substring$2: function(receiver, startIndex, endIndex) {
      if (endIndex == null)
        endIndex = receiver.length;
      if (typeof endIndex !== "number" || Math.floor(endIndex) !== endIndex)
        H.throwExpression(P.ArgumentError$(endIndex));
      if (startIndex < 0)
        throw H.wrapException(P.RangeError$value(startIndex));
      if (typeof endIndex !== "number")
        return H.iae(endIndex);
      if (startIndex > endIndex)
        throw H.wrapException(P.RangeError$value(startIndex));
      if (endIndex > receiver.length)
        throw H.wrapException(P.RangeError$value(endIndex));
      return receiver.substring(startIndex, endIndex);
    },
    substring$1: function($receiver, startIndex) {
      return this.substring$2($receiver, startIndex, null);
    },
    get$isEmpty: function(receiver) {
      return receiver.length === 0;
    },
    toString$0: function(receiver) {
      return receiver;
    },
    get$hashCode: function(receiver) {
      var t1, hash, i;
      for (t1 = receiver.length, hash = 0, i = 0; i < t1; ++i) {
        hash = 536870911 & hash + receiver.charCodeAt(i);
        hash = 536870911 & hash + ((524287 & hash) << 10 >>> 0);
        hash ^= hash >> 6;
      }
      hash = 536870911 & hash + ((67108863 & hash) << 3 >>> 0);
      hash ^= hash >> 11;
      return 536870911 & hash + ((16383 & hash) << 15 >>> 0);
    },
    get$length: function(receiver) {
      return receiver.length;
    },
    $index: function(receiver, index) {
      if (typeof index !== "number" || Math.floor(index) !== index)
        throw H.wrapException(new P.ArgumentError(index));
      if (index >= receiver.length || index < 0)
        throw H.wrapException(P.RangeError$value(index));
      return receiver[index];
    },
    $isString: true
  }
}],
["_isolate_helper", "dart:_isolate_helper", , H, {
  "": "",
  _callInIsolate: function(isolate, $function) {
    var result = isolate.eval$1($function);
    init.globalState.topEventLoop.run$0();
    return result;
  },
  startRootIsolate: function(entry) {
    var t1, t2, rootContext;
    t1 = new H._Manager(0, 0, 1, null, null, null, null, null, null, null, null, null, entry);
    t1._Manager$1(entry);
    init.globalState = t1;
    if (init.globalState.isWorker === true)
      return;
    t1 = init.globalState;
    t2 = t1.nextIsolateId;
    t1.nextIsolateId = t2 + 1;
    rootContext = new H._IsolateContext(t2, P.LinkedHashMap_LinkedHashMap(null, null, null, J.JSInt, H.RawReceivePortImpl), P.LinkedHashSet_LinkedHashSet(null, null, null, J.JSInt), new Isolate());
    init.globalState.rootContext = rootContext;
    init.globalState.currentContext = rootContext;
    t1 = H.getDynamicRuntimeType();
    t2 = H.buildFunctionType(t1, [t1])._isTest$1(entry);
    if (t2)
      rootContext.eval$1(new H.startRootIsolate_closure(entry));
    else {
      t1 = H.buildFunctionType(t1, [t1, t1])._isTest$1(entry);
      if (t1)
        rootContext.eval$1(new H.startRootIsolate_closure0(entry));
      else
        rootContext.eval$1(entry);
    }
    init.globalState.topEventLoop.run$0();
  },
  IsolateNatives_computeThisScript: function() {
    var currentScript = init.currentScript;
    if (currentScript != null)
      return String(currentScript.src);
    if (typeof version == "function" && typeof os == "object" && "system" in os)
      return H.IsolateNatives_computeThisScriptD8();
    if (typeof version == "function" && typeof system == "function")
      return thisFilename();
    return;
  },
  IsolateNatives_computeThisScriptD8: function() {
    var stack, matches;
    stack = new Error().stack;
    if (stack == null) {
      stack = (function() {try { throw new Error() } catch(e) { return e.stack }})();
      if (stack == null)
        throw H.wrapException(P.UnsupportedError$("No stack trace"));
    }
    matches = stack.match(new RegExp("^ *at [^(]*\\((.*):[0-9]*:[0-9]*\\)$", "m"));
    if (matches != null)
      return matches[1];
    matches = stack.match(new RegExp("^[^@]*@(.*):[0-9]*$", "m"));
    if (matches != null)
      return matches[1];
    throw H.wrapException(P.UnsupportedError$("Cannot extract URI from \"" + stack + "\""));
  },
  IsolateNatives__processWorkerMessage: function(sender, e) {
    var msg, t1, functionName, entryPoint, args, message, isSpawnUri, replyTo, t2, context, uri, t3, t4, t5, worker, t6, workerId;
    msg = H._deserializeMessage(e.data);
    t1 = J.getInterceptor$asx(msg);
    switch (t1.$index(msg, "command")) {
      case "start":
        init.globalState.currentManagerId = t1.$index(msg, "id");
        functionName = t1.$index(msg, "functionName");
        entryPoint = functionName == null ? init.globalState.entry : init.globalFunctions[functionName]();
        args = t1.$index(msg, "args");
        message = H._deserializeMessage(t1.$index(msg, "msg"));
        isSpawnUri = t1.$index(msg, "isSpawnUri");
        replyTo = H._deserializeMessage(t1.$index(msg, "replyTo"));
        t1 = init.globalState;
        t2 = t1.nextIsolateId;
        t1.nextIsolateId = t2 + 1;
        context = new H._IsolateContext(t2, P.LinkedHashMap_LinkedHashMap(null, null, null, J.JSInt, H.RawReceivePortImpl), P.LinkedHashSet_LinkedHashSet(null, null, null, J.JSInt), new Isolate());
        init.globalState.topEventLoop.events._add$1(new H._IsolateEvent(context, new H.IsolateNatives__processWorkerMessage_closure(entryPoint, args, message, isSpawnUri, replyTo), "worker-start"));
        init.globalState.currentContext = context;
        init.globalState.topEventLoop.run$0();
        break;
      case "spawn-worker":
        t2 = t1.$index(msg, "functionName");
        uri = t1.$index(msg, "uri");
        t3 = t1.$index(msg, "args");
        t4 = t1.$index(msg, "msg");
        t5 = t1.$index(msg, "isSpawnUri");
        t1 = t1.$index(msg, "replyPort");
        if (uri == null)
          uri = $.get$IsolateNatives_thisScript();
        worker = new Worker(uri);
        worker.onmessage = function(e) { H.IsolateNatives__processWorkerMessage(worker, e); };
        t6 = init.globalState;
        workerId = t6.nextManagerId;
        t6.nextManagerId = workerId + 1;
        t6 = $.get$IsolateNatives_workerIds();
        t6.$indexSet(t6, worker, workerId);
        t6 = init.globalState.managers;
        t6.$indexSet(t6, workerId, worker);
        worker.postMessage(H._serializeMessage(H.fillLiteralMap(["command", "start", "id", workerId, "replyTo", H._serializeMessage(t1), "args", t3, "msg", H._serializeMessage(t4), "isSpawnUri", t5, "functionName", t2], P.LinkedHashMap_LinkedHashMap(null, null, null, null, null))));
        break;
      case "message":
        if (t1.$index(msg, "port") != null)
          t1.$index(msg, "port").send$1(t1.$index(msg, "msg"));
        init.globalState.topEventLoop.run$0();
        break;
      case "close":
        t1 = init.globalState.managers;
        t2 = $.get$IsolateNatives_workerIds();
        t1.remove$1(t1, t2.$index(t2, sender));
        sender.terminate();
        init.globalState.topEventLoop.run$0();
        break;
      case "log":
        H.IsolateNatives__log(t1.$index(msg, "msg"));
        break;
      case "print":
        if (init.globalState.isWorker === true) {
          t1 = init.globalState.mainManager;
          t2 = H._serializeMessage(H.fillLiteralMap(["command", "print", "msg", msg], P.LinkedHashMap_LinkedHashMap(null, null, null, null, null)));
          t1.toString;
          self.postMessage(t2);
        } else
          P.print(t1.$index(msg, "msg"));
        break;
      case "error":
        throw H.wrapException(t1.$index(msg, "msg"));
      default:
    }
  },
  IsolateNatives__log: function(msg) {
    var trace, t1, t2, exception;
    if (init.globalState.isWorker === true) {
      t1 = init.globalState.mainManager;
      t2 = H._serializeMessage(H.fillLiteralMap(["command", "log", "msg", msg], P.LinkedHashMap_LinkedHashMap(null, null, null, null, null)));
      t1.toString;
      self.postMessage(t2);
    } else
      try {
        $.get$globalThis().console.log(msg);
      } catch (exception) {
        H.unwrapException(exception);
        trace = new H._StackTrace(exception, null);
        throw H.wrapException(P.Exception_Exception(trace));
      }

  },
  _serializeMessage: function(message) {
    var t1;
    if (init.globalState.supportsWorkers === true) {
      t1 = new H._JsSerializer(0, new H._MessageTraverserVisitedMap());
      t1._visited = new H._JsVisitedMap(null);
      return t1.traverse$1(message);
    } else {
      t1 = new H._JsCopier(new H._MessageTraverserVisitedMap());
      t1._visited = new H._JsVisitedMap(null);
      return t1.traverse$1(message);
    }
  },
  _deserializeMessage: function(message) {
    if (init.globalState.supportsWorkers === true)
      return new H._JsDeserializer(null).deserialize$1(message);
    else
      return message;
  },
  _MessageTraverser_isPrimitive: function(x) {
    return x == null || typeof x === "string" || typeof x === "number" || typeof x === "boolean";
  },
  _Deserializer_isPrimitive: function(x) {
    return x == null || typeof x === "string" || typeof x === "number" || typeof x === "boolean";
  },
  startRootIsolate_closure: {
    "": "Closure:8;entry_0",
    call$0: function() {
      this.entry_0.call$1([]);
    }
  },
  startRootIsolate_closure0: {
    "": "Closure:8;entry_1",
    call$0: function() {
      this.entry_1.call$2([], null);
    }
  },
  _Manager: {
    "": "Object;nextIsolateId,currentManagerId,nextManagerId,currentContext,rootContext,topEventLoop,fromCommandLine,isWorker,supportsWorkers,isolates,mainManager,managers,entry",
    _Manager$1: function(entry) {
      var t1, t2, t3, $function;
      t1 = $.get$globalWindow() == null;
      t2 = $.get$globalWorker();
      t3 = t1 && $.get$globalPostMessageDefined() === true;
      this.isWorker = t3;
      if (!t3)
        t2 = t2 != null && $.get$IsolateNatives_thisScript() != null;
      else
        t2 = true;
      this.supportsWorkers = t2;
      this.fromCommandLine = t1 && !t3;
      t2 = H._IsolateEvent;
      t3 = H.setRuntimeTypeInfo(new P.ListQueue(null, 0, 0, 0), [t2]);
      t3.ListQueue$1(null, t2);
      this.topEventLoop = new H._EventLoop(t3, 0);
      this.isolates = P.LinkedHashMap_LinkedHashMap(null, null, null, J.JSInt, H._IsolateContext);
      this.managers = P.LinkedHashMap_LinkedHashMap(null, null, null, J.JSInt, null);
      if (this.isWorker === true) {
        t1 = new H._MainManagerStub();
        this.mainManager = t1;
        $function = function (e) { H.IsolateNatives__processWorkerMessage(t1, e); };
        $.get$globalThis().onmessage = $function;
        $.get$globalThis().dartPrint = function (object) {};
      }
    }
  },
  _IsolateContext: {
    "": "Object;id,ports,weakPorts,isolateStatics<",
    eval$1: function(code) {
      var old, result;
      old = init.globalState.currentContext;
      init.globalState.currentContext = this;
      $ = this.isolateStatics;
      result = null;
      try {
        result = code.call$0();
      } finally {
        init.globalState.currentContext = old;
        if (old != null)
          $ = old.get$isolateStatics();
      }
      return result;
    },
    lookup$1: function(portId) {
      var t1 = this.ports;
      return t1.$index(t1, portId);
    },
    register$2: function(_, portId, port) {
      var t1 = this.ports;
      if (t1.containsKey$1(portId))
        throw H.wrapException(P.Exception_Exception("Registry: ports must be registered only once."));
      t1.$indexSet(t1, portId, port);
      this._updateGlobalState$0();
    },
    _updateGlobalState$0: function() {
      var t1, t2;
      t1 = this.id;
      if (this.ports._collection$_length - this.weakPorts._collection$_length > 0) {
        t2 = init.globalState.isolates;
        t2.$indexSet(t2, t1, this);
      } else {
        t2 = init.globalState.isolates;
        t2.remove$1(t2, t1);
      }
    }
  },
  _EventLoop: {
    "": "Object;events,activeTimerCount",
    dequeue$0: function() {
      var t1 = this.events;
      if (t1._head === t1._tail)
        return;
      return t1.removeFirst$0();
    },
    runIteration$0: function() {
      var $event, t1, t2;
      $event = this.dequeue$0();
      if ($event == null) {
        if (init.globalState.rootContext != null && init.globalState.isolates.containsKey$1(init.globalState.rootContext.id) && init.globalState.fromCommandLine === true && init.globalState.rootContext.ports._collection$_length === 0)
          H.throwExpression(P.Exception_Exception("Program exited with open ReceivePorts."));
        t1 = init.globalState;
        if (t1.isWorker === true && t1.isolates._collection$_length === 0 && t1.topEventLoop.activeTimerCount === 0) {
          t1 = t1.mainManager;
          t2 = H._serializeMessage(H.fillLiteralMap(["command", "close"], P.LinkedHashMap_LinkedHashMap(null, null, null, null, null)));
          t1.toString;
          self.postMessage(t2);
        }
        return false;
      }
      $event.process$0();
      return true;
    },
    _runHelper$0: function() {
      if ($.get$globalWindow() != null)
        new H._EventLoop__runHelper_next(this).call$0();
      else
        for (; this.runIteration$0();)
          ;
    },
    run$0: function() {
      var e, trace, exception, t1, t2;
      if (init.globalState.isWorker !== true)
        this._runHelper$0();
      else
        try {
          this._runHelper$0();
        } catch (exception) {
          t1 = H.unwrapException(exception);
          e = t1;
          trace = new H._StackTrace(exception, null);
          t1 = init.globalState.mainManager;
          t2 = H._serializeMessage(H.fillLiteralMap(["command", "error", "msg", H.S(e) + "\n" + H.S(trace)], P.LinkedHashMap_LinkedHashMap(null, null, null, null, null)));
          t1.toString;
          self.postMessage(t2);
        }

    }
  },
  _EventLoop__runHelper_next: {
    "": "Closure:0;this_0",
    call$0: function() {
      if (!this.this_0.runIteration$0())
        return;
      P.Timer_Timer(C.Duration_0, this);
    }
  },
  _IsolateEvent: {
    "": "Object;isolate,fn,message",
    process$0: function() {
      this.isolate.eval$1(this.fn);
    }
  },
  _MainManagerStub: {
    "": "Object;"
  },
  IsolateNatives__processWorkerMessage_closure: {
    "": "Closure:8;entryPoint_0,args_1,message_2,isSpawnUri_3,replyTo_4",
    call$0: function() {
      var t1, t2, t3, t4, t5, t6, t7;
      t1 = this.entryPoint_0;
      t2 = this.args_1;
      t3 = this.message_2;
      t4 = init.globalState.currentContext.id;
      $.Primitives_mirrorFunctionCacheName = $.Primitives_mirrorFunctionCacheName + ("_" + t4);
      $.Primitives_mirrorInvokeCacheName = $.Primitives_mirrorInvokeCacheName + ("_" + t4);
      t4 = $.RawReceivePortImpl__nextFreeId;
      $.RawReceivePortImpl__nextFreeId = t4 + 1;
      t5 = new H.RawReceivePortImpl(t4, null, false);
      t6 = init.globalState.currentContext;
      t7 = t6.weakPorts;
      t7.add$1(t7, t4);
      t6.register$2(t6, t4, t5);
      t4 = new H.ReceivePortImpl(t5, null);
      t4.ReceivePortImpl$fromRawReceivePort$1(t5);
      $.controlPort = t4;
      this.replyTo_4.send$1(["spawned", new H._NativeJsSendPort(t5, init.globalState.currentContext.id)]);
      if (this.isSpawnUri_3 !== true)
        t1.call$1(t3);
      else {
        t4 = H.getDynamicRuntimeType();
        t5 = H.buildFunctionType(t4, [t4, t4])._isTest$1(t1);
        if (t5)
          t1.call$2(t2, t3);
        else {
          t3 = H.buildFunctionType(t4, [t4])._isTest$1(t1);
          if (t3)
            t1.call$1(t2);
          else
            t1.call$0();
        }
      }
    }
  },
  _BaseSendPort: {
    "": "Object;",
    $isSendPort: true
  },
  _NativeJsSendPort: {
    "": "_BaseSendPort;_receivePort,_isolateId",
    send$1: function(message) {
      var t1, t2, t3, isolate, shouldSerialize;
      t1 = {};
      t2 = init.globalState.isolates;
      t3 = this._isolateId;
      isolate = t2.$index(t2, t3);
      if (isolate == null)
        return;
      if (this._receivePort.get$_isClosed())
        return;
      shouldSerialize = init.globalState.currentContext != null && init.globalState.currentContext.id !== t3;
      t1.msg_0 = message;
      if (shouldSerialize)
        t1.msg_0 = H._serializeMessage(message);
      t2 = init.globalState.topEventLoop;
      t3 = "receive " + H.S(message);
      t2.events._add$1(new H._IsolateEvent(isolate, new H._NativeJsSendPort_send_closure(t1, this, shouldSerialize), t3));
    },
    $eq: function(_, other) {
      var t1;
      if (other == null)
        return false;
      t1 = J.getInterceptor(other);
      return typeof other === "object" && other !== null && !!t1.$is_NativeJsSendPort && J.$eq(this._receivePort, other._receivePort);
    },
    get$hashCode: function(_) {
      return this._receivePort.get$_id();
    },
    $is_NativeJsSendPort: true,
    $isSendPort: true
  },
  _NativeJsSendPort_send_closure: {
    "": "Closure:8;box_0,this_1,shouldSerialize_2",
    call$0: function() {
      var t1, t2;
      t1 = this.this_1._receivePort;
      if (!t1.get$_isClosed()) {
        if (this.shouldSerialize_2) {
          t2 = this.box_0;
          t2.msg_0 = H._deserializeMessage(t2.msg_0);
        }
        t1.__isolate_helper$_add$1(this.box_0.msg_0);
      }
    }
  },
  _WorkerSendPort: {
    "": "_BaseSendPort;_workerId,_receivePortId,_isolateId",
    send$1: function(message) {
      var workerMessage, t1, manager;
      workerMessage = H._serializeMessage(H.fillLiteralMap(["command", "message", "port", this, "msg", message], P.LinkedHashMap_LinkedHashMap(null, null, null, null, null)));
      if (init.globalState.isWorker === true) {
        init.globalState.mainManager.toString;
        self.postMessage(workerMessage);
      } else {
        t1 = init.globalState.managers;
        manager = t1.$index(t1, this._workerId);
        if (manager != null)
          manager.postMessage(workerMessage);
      }
    },
    $eq: function(_, other) {
      var t1;
      if (other == null)
        return false;
      t1 = J.getInterceptor(other);
      return typeof other === "object" && other !== null && !!t1.$is_WorkerSendPort && J.$eq(this._workerId, other._workerId) && J.$eq(this._isolateId, other._isolateId) && J.$eq(this._receivePortId, other._receivePortId);
    },
    get$hashCode: function(_) {
      var t1, t2, t3;
      t1 = this._workerId;
      if (typeof t1 !== "number")
        return t1.$shl();
      t2 = this._isolateId;
      if (typeof t2 !== "number")
        return t2.$shl();
      t3 = this._receivePortId;
      if (typeof t3 !== "number")
        return H.iae(t3);
      return (t1 << 16 ^ t2 << 8 ^ t3) >>> 0;
    },
    $is_WorkerSendPort: true,
    $isSendPort: true
  },
  RawReceivePortImpl: {
    "": "Object;_id<,_handler,_isClosed<",
    _handler$1: function(arg0) {
      return this._handler.call$1(arg0);
    },
    close$0: function(_) {
      var t1, t2;
      if (this._isClosed)
        return;
      this._isClosed = true;
      this._handler = null;
      t1 = init.globalState.currentContext;
      t2 = t1.ports;
      t2.remove$1(t2, this._id);
      t1._updateGlobalState$0();
    },
    __isolate_helper$_add$1: function(dataEvent) {
      if (this._isClosed)
        return;
      this._handler$1(dataEvent);
    },
    static: {"": "RawReceivePortImpl__nextFreeId"}
  },
  ReceivePortImpl: {
    "": "Stream;_rawPort,_controller",
    listen$4$cancelOnError$onDone$onError: function(onData, cancelOnError, onDone, onError) {
      var t1 = this._controller;
      t1.toString;
      return H.setRuntimeTypeInfo(new P._ControllerStream(t1), [null]).listen$4$cancelOnError$onDone$onError(onData, cancelOnError, onDone, onError);
    },
    close$0: [function(_) {
      var t1 = this._rawPort;
      t1.close$0(t1);
      t1 = this._controller;
      t1.close$0(t1);
    }, "call$0", "get$close", 0, 0, 0],
    ReceivePortImpl$fromRawReceivePort$1: function(_rawPort) {
      var t1 = P.StreamController_StreamController(this.get$close(this), null, null, null, true, null);
      this._controller = t1;
      this._rawPort._handler = t1.get$add(t1);
    }
  },
  _JsSerializer: {
    "": "_Serializer;_nextFreeRefId,_visited",
    visitSendPort$1: function(x) {
      if (!!x.$is_NativeJsSendPort)
        return ["sendport", init.globalState.currentManagerId, x._isolateId, x._receivePort.get$_id()];
      if (!!x.$is_WorkerSendPort)
        return ["sendport", x._workerId, x._isolateId, x._receivePortId];
      throw H.wrapException("Illegal underlying port " + H.S(x));
    }
  },
  _JsCopier: {
    "": "_Copier;_visited",
    visitSendPort$1: function(x) {
      if (!!x.$is_NativeJsSendPort)
        return new H._NativeJsSendPort(x._receivePort, x._isolateId);
      if (!!x.$is_WorkerSendPort)
        return new H._WorkerSendPort(x._workerId, x._receivePortId, x._isolateId);
      throw H.wrapException("Illegal underlying port " + H.S(x));
    }
  },
  _JsDeserializer: {
    "": "_Deserializer;_deserialized",
    deserializeSendPort$1: function(list) {
      var t1, managerId, isolateId, receivePortId, isolate, receivePort;
      t1 = J.getInterceptor$asx(list);
      managerId = t1.$index(list, 1);
      isolateId = t1.$index(list, 2);
      receivePortId = t1.$index(list, 3);
      if (J.$eq(managerId, init.globalState.currentManagerId)) {
        t1 = init.globalState.isolates;
        isolate = t1.$index(t1, isolateId);
        if (isolate == null)
          return;
        receivePort = isolate.lookup$1(receivePortId);
        if (receivePort == null)
          return;
        return new H._NativeJsSendPort(receivePort, isolateId);
      } else
        return new H._WorkerSendPort(managerId, receivePortId, isolateId);
    }
  },
  _JsVisitedMap: {
    "": "Object;tagged",
    $index: function(_, object) {
      return object.__MessageTraverser__attached_info__;
    },
    $indexSet: function(_, object, info) {
      this.tagged.push(object);
      object.__MessageTraverser__attached_info__ = info;
    },
    reset$0: function(_) {
      this.tagged = [];
    },
    cleanup$0: function() {
      var $length, i, t1;
      for ($length = this.tagged.length, i = 0; i < $length; ++i) {
        t1 = this.tagged;
        if (i >= t1.length)
          return H.ioore(t1, i);
        t1[i].__MessageTraverser__attached_info__ = null;
      }
      this.tagged = null;
    }
  },
  _MessageTraverserVisitedMap: {
    "": "Object;",
    $index: function(_, object) {
      return;
    },
    $indexSet: function(_, object, info) {
    },
    reset$0: function(_) {
    },
    cleanup$0: function() {
      return;
    }
  },
  _MessageTraverser: {
    "": "Object;",
    traverse$1: function(x) {
      var result, t1;
      if (H._MessageTraverser_isPrimitive(x))
        return this.visitPrimitive$1(x);
      t1 = this._visited;
      t1.reset$0(t1);
      result = null;
      try {
        result = this._dispatch$1(x);
      } finally {
        this._visited.cleanup$0();
      }
      return result;
    },
    _dispatch$1: function(x) {
      var t1;
      if (x == null || typeof x === "string" || typeof x === "number" || typeof x === "boolean")
        return this.visitPrimitive$1(x);
      t1 = J.getInterceptor(x);
      if (typeof x === "object" && x !== null && (x.constructor === Array || !!t1.$isList))
        return this.visitList$1(x);
      if (typeof x === "object" && x !== null && !!t1.$isMap)
        return this.visitMap$1(x);
      if (typeof x === "object" && x !== null && !!t1.$isSendPort)
        return this.visitSendPort$1(x);
      return this.visitObject$1(x);
    },
    visitObject$1: function(x) {
      throw H.wrapException("Message serialization: Illegal value " + H.S(x) + " passed");
    }
  },
  _Copier: {
    "": "_MessageTraverser;",
    visitPrimitive$1: function(x) {
      return x;
    },
    visitList$1: function(list) {
      var t1, copy, len, t2, i;
      t1 = this._visited;
      copy = t1.$index(t1, list);
      if (copy != null)
        return copy;
      t1 = J.getInterceptor$asx(list);
      len = t1.get$length(list);
      copy = Array(len);
      copy.fixed$length = init;
      t2 = this._visited;
      t2.$indexSet(t2, list, copy);
      for (i = 0; i < len; ++i)
        copy[i] = this._dispatch$1(t1.$index(list, i));
      return copy;
    },
    visitMap$1: function(map) {
      var t1, t2, copy;
      t1 = {};
      t2 = this._visited;
      copy = t2.$index(t2, map);
      t1.copy_0 = copy;
      if (copy != null)
        return copy;
      copy = P.LinkedHashMap_LinkedHashMap(null, null, null, null, null);
      t1.copy_0 = copy;
      t2 = this._visited;
      t2.$indexSet(t2, map, copy);
      map.forEach$1(map, new H._Copier_visitMap_closure(t1, this));
      return t1.copy_0;
    },
    visitSendPort$1: function(x) {
      return H.throwExpression(P.UnimplementedError$(null));
    }
  },
  _Copier_visitMap_closure: {
    "": "Closure:9;box_0,this_1",
    call$2: function(key, val) {
      var t1 = this.this_1;
      J.$indexSet$ax(this.box_0.copy_0, t1._dispatch$1(key), t1._dispatch$1(val));
    }
  },
  _Serializer: {
    "": "_MessageTraverser;",
    visitPrimitive$1: function(x) {
      return x;
    },
    visitList$1: function(list) {
      var t1, copyId, id;
      t1 = this._visited;
      copyId = t1.$index(t1, list);
      if (copyId != null)
        return ["ref", copyId];
      id = this._nextFreeRefId;
      this._nextFreeRefId = id + 1;
      t1 = this._visited;
      t1.$indexSet(t1, list, id);
      return ["list", id, this._serializeList$1(list)];
    },
    visitMap$1: function(map) {
      var t1, copyId, id, keys;
      t1 = this._visited;
      copyId = t1.$index(t1, map);
      if (copyId != null)
        return ["ref", copyId];
      id = this._nextFreeRefId;
      this._nextFreeRefId = id + 1;
      t1 = this._visited;
      t1.$indexSet(t1, map, id);
      t1 = map.get$keys();
      keys = this._serializeList$1(P.List_List$from(t1, true, H.getRuntimeTypeArgument(t1, "IterableBase", 0)));
      t1 = map.get$values(map);
      return ["map", id, keys, this._serializeList$1(P.List_List$from(t1, true, H.getRuntimeTypeArgument(t1, "IterableBase", 0)))];
    },
    _serializeList$1: function(list) {
      var t1, len, result, i, t2;
      t1 = J.getInterceptor$asx(list);
      len = t1.get$length(list);
      result = [];
      C.JSArray_methods.set$length(result, len);
      for (i = 0; i < len; ++i) {
        t2 = this._dispatch$1(t1.$index(list, i));
        if (i >= result.length)
          return H.ioore(result, i);
        result[i] = t2;
      }
      return result;
    },
    visitSendPort$1: function(x) {
      return H.throwExpression(P.UnimplementedError$(null));
    }
  },
  _Deserializer: {
    "": "Object;",
    deserialize$1: function(x) {
      if (H._Deserializer_isPrimitive(x))
        return x;
      this._deserialized = P.HashMap_HashMap(null, null, null, null, null);
      return this._deserializeHelper$1(x);
    },
    _deserializeHelper$1: function(x) {
      var t1, id;
      if (x == null || typeof x === "string" || typeof x === "number" || typeof x === "boolean")
        return x;
      t1 = J.getInterceptor$asx(x);
      switch (t1.$index(x, 0)) {
        case "ref":
          id = t1.$index(x, 1);
          t1 = this._deserialized;
          return t1.$index(t1, id);
        case "list":
          return this._deserializeList$1(x);
        case "map":
          return this._deserializeMap$1(x);
        case "sendport":
          return this.deserializeSendPort$1(x);
        default:
          return this.deserializeObject$1(x);
      }
    },
    _deserializeList$1: function(x) {
      var t1, id, dartList, len, i;
      t1 = J.getInterceptor$asx(x);
      id = t1.$index(x, 1);
      dartList = t1.$index(x, 2);
      t1 = this._deserialized;
      t1.$indexSet(t1, id, dartList);
      t1 = J.getInterceptor$asx(dartList);
      len = t1.get$length(dartList);
      if (typeof len !== "number")
        return H.iae(len);
      i = 0;
      for (; i < len; ++i)
        t1.$indexSet(dartList, i, this._deserializeHelper$1(t1.$index(dartList, i)));
      return dartList;
    },
    _deserializeMap$1: function(x) {
      var result, t1, id, t2, keys, values, len, i;
      result = P.LinkedHashMap_LinkedHashMap(null, null, null, null, null);
      t1 = J.getInterceptor$asx(x);
      id = t1.$index(x, 1);
      t2 = this._deserialized;
      t2.$indexSet(t2, id, result);
      keys = t1.$index(x, 2);
      values = t1.$index(x, 3);
      t1 = J.getInterceptor$asx(keys);
      len = t1.get$length(keys);
      if (typeof len !== "number")
        return H.iae(len);
      t2 = J.getInterceptor$asx(values);
      i = 0;
      for (; i < len; ++i)
        result.$indexSet(result, this._deserializeHelper$1(t1.$index(keys, i)), this._deserializeHelper$1(t2.$index(values, i)));
      return result;
    },
    deserializeObject$1: function(x) {
      throw H.wrapException("Unexpected serialized object");
    }
  },
  TimerImpl: {
    "": "Object;_once,_inEventLoop,_handle",
    TimerImpl$2: function(milliseconds, callback) {
      var t1, t2;
      if (milliseconds === 0)
        t1 = $.get$globalThis().setTimeout == null || init.globalState.isWorker === true;
      else
        t1 = false;
      if (t1) {
        this._handle = 1;
        t1 = init.globalState.topEventLoop;
        t2 = init.globalState.currentContext;
        t1.events._add$1(new H._IsolateEvent(t2, new H.TimerImpl_internalCallback(this, callback), "timer"));
        this._inEventLoop = true;
      } else {
        t1 = $.get$globalThis();
        if (t1.setTimeout != null) {
          t2 = init.globalState.topEventLoop;
          t2.activeTimerCount = t2.activeTimerCount + 1;
          this._handle = t1.setTimeout(H.convertDartClosureToJS(new H.TimerImpl_internalCallback0(this, callback), 0), milliseconds);
        } else
          throw H.wrapException(P.UnsupportedError$("Timer greater than 0."));
      }
    },
    static: {TimerImpl$: function(milliseconds, callback) {
        var t1 = new H.TimerImpl(true, false, null);
        t1.TimerImpl$2(milliseconds, callback);
        return t1;
      }}
  },
  TimerImpl_internalCallback: {
    "": "Closure:0;this_0,callback_1",
    call$0: function() {
      this.this_0._handle = null;
      this.callback_1.call$0();
    }
  },
  TimerImpl_internalCallback0: {
    "": "Closure:0;this_2,callback_3",
    call$0: function() {
      this.this_2._handle = null;
      var t1 = init.globalState.topEventLoop;
      t1.activeTimerCount = t1.activeTimerCount - 1;
      this.callback_3.call$0();
    }
  }
}],
["_js_helper", "dart:_js_helper", , H, {
  "": "",
  isJsIndexable: function(object, record) {
    var result, t1;
    if (record != null) {
      result = record.x;
      if (result != null)
        return result;
    }
    t1 = J.getInterceptor(object);
    return typeof object === "object" && object !== null && !!t1.$isJavaScriptIndexingBehavior;
  },
  S: function(value) {
    var res;
    if (typeof value === "string")
      return value;
    if (typeof value === "number") {
      if (value !== 0)
        return "" + value;
    } else if (true === value)
      return "true";
    else if (false === value)
      return "false";
    else if (value == null)
      return "null";
    res = J.toString$0(value);
    if (typeof res !== "string")
      throw H.wrapException(P.ArgumentError$(value));
    return res;
  },
  Primitives_objectHashCode: function(object) {
    var hash = object.$identityHash;
    if (hash == null) {
      hash = Math.random() * 0x3fffffff | 0;
      object.$identityHash = hash;
    }
    return hash;
  },
  Primitives_objectTypeName: function(object) {
    var $name, decompiled;
    $name = C.JS_CONST_IX5(J.getInterceptor(object));
    if ($name === "Object") {
      decompiled = String(object.constructor).match(/^\s*function\s*(\S*)\s*\(/)[1];
      if (typeof decompiled === "string")
        $name = decompiled;
    }
    if (J.getInterceptor$s($name).codeUnitAt$1($name, 0) === 36)
      $name = C.JSString_methods.substring$1($name, 1);
    return $name + H.joinArguments(H.getRuntimeTypeInfo(object), 0, null);
  },
  Primitives_objectToString: function(object) {
    return "Instance of '" + H.Primitives_objectTypeName(object) + "'";
  },
  Primitives__fromCharCodeApply: function(array) {
    var end, t1, result, i, subarray, t2;
    end = array.length;
    for (t1 = end <= 500, result = "", i = 0; i < end; i += 500) {
      if (t1)
        subarray = array;
      else {
        t2 = i + 500;
        t2 = t2 < end ? t2 : end;
        subarray = array.slice(i, t2);
      }
      result += String.fromCharCode.apply(null, subarray);
    }
    return result;
  },
  Primitives_stringFromCodePoints: function(codePoints) {
    var a, t1, i;
    a = [];
    a.$builtinTypeInfo = [J.JSInt];
    for (t1 = new H.ListIterator(codePoints, codePoints.length, 0, null); t1.moveNext$0();) {
      i = t1._dev$_current;
      if (typeof i !== "number" || Math.floor(i) !== i)
        throw H.wrapException(P.ArgumentError$(i));
      if (i <= 65535)
        a.push(i);
      else if (i <= 1114111) {
        a.push(55296 + (C.JSInt_methods._shrOtherPositive$1(i - 65536, 10) & 1023));
        a.push(56320 + (i & 1023));
      } else
        throw H.wrapException(P.ArgumentError$(i));
    }
    return H.Primitives__fromCharCodeApply(a);
  },
  Primitives_stringFromCharCodes: function(charCodes) {
    var t1, i;
    for (t1 = new H.ListIterator(charCodes, charCodes.length, 0, null); t1.moveNext$0();) {
      i = t1._dev$_current;
      if (typeof i !== "number" || Math.floor(i) !== i)
        throw H.wrapException(P.ArgumentError$(i));
      if (i < 0)
        throw H.wrapException(P.ArgumentError$(i));
      if (i > 65535)
        return H.Primitives_stringFromCodePoints(charCodes);
    }
    return H.Primitives__fromCharCodeApply(charCodes);
  },
  Primitives_lazyAsJsDate: function(receiver) {
    if (receiver.date === void 0)
      receiver.date = new Date(receiver.millisecondsSinceEpoch);
    return receiver.date;
  },
  Primitives_getHours: function(receiver) {
    return receiver.isUtc ? H.Primitives_lazyAsJsDate(receiver).getUTCHours() + 0 : H.Primitives_lazyAsJsDate(receiver).getHours() + 0;
  },
  Primitives_getMinutes: function(receiver) {
    return receiver.isUtc ? H.Primitives_lazyAsJsDate(receiver).getUTCMinutes() + 0 : H.Primitives_lazyAsJsDate(receiver).getMinutes() + 0;
  },
  Primitives_getSeconds: function(receiver) {
    return receiver.isUtc ? H.Primitives_lazyAsJsDate(receiver).getUTCSeconds() + 0 : H.Primitives_lazyAsJsDate(receiver).getSeconds() + 0;
  },
  Primitives_getProperty: function(object, key) {
    if (object == null || typeof object === "boolean" || typeof object === "number" || typeof object === "string")
      throw H.wrapException(new P.ArgumentError(object));
    return object[key];
  },
  Primitives_setProperty: function(object, key, value) {
    if (object == null || typeof object === "boolean" || typeof object === "number" || typeof object === "string")
      throw H.wrapException(new P.ArgumentError(object));
    object[key] = value;
  },
  iae: function(argument) {
    throw H.wrapException(P.ArgumentError$(argument));
  },
  ioore: function(receiver, index) {
    if (receiver == null)
      J.get$length$asx(receiver);
    if (typeof index !== "number" || Math.floor(index) !== index)
      H.iae(index);
    throw H.wrapException(P.RangeError$value(index));
  },
  wrapException: function(ex) {
    var wrapper;
    if (ex == null)
      ex = new P.NullThrownError();
    wrapper = new Error();
    wrapper.dartException = ex;
    if ("defineProperty" in Object) {
      Object.defineProperty(wrapper, "message", { get: H.toStringWrapper });
      wrapper.name = "";
    } else
      wrapper.toString = H.toStringWrapper;
    return wrapper;
  },
  toStringWrapper: function() {
    return J.toString$0(this.dartException);
  },
  throwExpression: function(ex) {
    var wrapper;
    if (ex == null)
      ex = new P.NullThrownError();
    wrapper = new Error();
    wrapper.dartException = ex;
    if ("defineProperty" in Object) {
      Object.defineProperty(wrapper, "message", { get: H.toStringWrapper });
      wrapper.name = "";
    } else
      wrapper.toString = H.toStringWrapper;
    throw wrapper;
  },
  unwrapException: function(ex) {
    var t1, message, number, ieErrorCode, t2, t3, t4, nullLiteralCall, t5, t6, t7, t8, t9, match;
    t1 = new H.unwrapException_saveStackTrace(ex);
    if (ex == null)
      return;
    if (typeof ex !== "object")
      return ex;
    if ("dartException" in ex)
      return t1.call$1(ex.dartException);
    else if (!("message" in ex))
      return ex;
    message = ex.message;
    if ("number" in ex && typeof ex.number == "number") {
      number = ex.number;
      ieErrorCode = number & 65535;
      if ((C.JSInt_methods._shrOtherPositive$1(number, 16) & 8191) === 10)
        switch (ieErrorCode) {
          case 438:
            return t1.call$1(H.JsNoSuchMethodError$(H.S(message) + " (Error " + ieErrorCode + ")", null));
          case 445:
          case 5007:
            t2 = H.S(message) + " (Error " + ieErrorCode + ")";
            return t1.call$1(new H.NullError(t2, null));
          default:
        }
    }
    if (ex instanceof TypeError) {
      t2 = $.get$TypeErrorDecoder_noSuchMethodPattern();
      t3 = $.get$TypeErrorDecoder_notClosurePattern();
      t4 = $.get$TypeErrorDecoder_nullCallPattern();
      nullLiteralCall = $.get$TypeErrorDecoder_nullLiteralCallPattern();
      t5 = $.get$TypeErrorDecoder_undefinedCallPattern();
      t6 = $.get$TypeErrorDecoder_undefinedLiteralCallPattern();
      t7 = $.get$TypeErrorDecoder_nullPropertyPattern();
      $.get$TypeErrorDecoder_nullLiteralPropertyPattern();
      t8 = $.get$TypeErrorDecoder_undefinedPropertyPattern();
      t9 = $.get$TypeErrorDecoder_undefinedLiteralPropertyPattern();
      match = t2.matchTypeError$1(message);
      if (match != null)
        return t1.call$1(H.JsNoSuchMethodError$(message, match));
      else {
        match = t3.matchTypeError$1(message);
        if (match != null) {
          match.method = "call";
          return t1.call$1(H.JsNoSuchMethodError$(message, match));
        } else {
          match = t4.matchTypeError$1(message);
          if (match == null) {
            match = nullLiteralCall.matchTypeError$1(message);
            if (match == null) {
              match = t5.matchTypeError$1(message);
              if (match == null) {
                match = t6.matchTypeError$1(message);
                if (match == null) {
                  match = t7.matchTypeError$1(message);
                  if (match == null) {
                    match = nullLiteralCall.matchTypeError$1(message);
                    if (match == null) {
                      match = t8.matchTypeError$1(message);
                      if (match == null) {
                        match = t9.matchTypeError$1(message);
                        t2 = match != null;
                      } else
                        t2 = true;
                    } else
                      t2 = true;
                  } else
                    t2 = true;
                } else
                  t2 = true;
              } else
                t2 = true;
            } else
              t2 = true;
          } else
            t2 = true;
          if (t2) {
            t2 = match == null ? null : match.method;
            return t1.call$1(new H.NullError(message, t2));
          }
        }
      }
      t2 = typeof message === "string" ? message : "";
      return t1.call$1(new H.UnknownJsTypeError(t2));
    }
    if (ex instanceof RangeError) {
      if (typeof message === "string" && message.indexOf("call stack") !== -1)
        return new P.StackOverflowError();
      return t1.call$1(new P.ArgumentError(null));
    }
    if (typeof InternalError == "function" && ex instanceof InternalError)
      if (typeof message === "string" && message === "too much recursion")
        return new P.StackOverflowError();
    return ex;
  },
  objectHashCode: function(object) {
    if (object == null || typeof object != 'object')
      return J.get$hashCode$(object);
    else
      return H.Primitives_objectHashCode(object);
  },
  fillLiteralMap: function(keyValuePairs, result) {
    var $length, index, index0, index1;
    $length = keyValuePairs.length;
    for (index = 0; index < $length; index = index1) {
      index0 = index + 1;
      index1 = index0 + 1;
      result.$indexSet(result, keyValuePairs[index], keyValuePairs[index0]);
    }
    return result;
  },
  invokeClosure: function(closure, isolate, numberOfArguments, arg1, arg2, arg3, arg4) {
    var t1 = J.getInterceptor(numberOfArguments);
    if (t1.$eq(numberOfArguments, 0))
      return H._callInIsolate(isolate, new H.invokeClosure_closure(closure));
    else if (t1.$eq(numberOfArguments, 1))
      return H._callInIsolate(isolate, new H.invokeClosure_closure0(closure, arg1));
    else if (t1.$eq(numberOfArguments, 2))
      return H._callInIsolate(isolate, new H.invokeClosure_closure1(closure, arg1, arg2));
    else if (t1.$eq(numberOfArguments, 3))
      return H._callInIsolate(isolate, new H.invokeClosure_closure2(closure, arg1, arg2, arg3));
    else if (t1.$eq(numberOfArguments, 4))
      return H._callInIsolate(isolate, new H.invokeClosure_closure3(closure, arg1, arg2, arg3, arg4));
    else
      throw H.wrapException(P.Exception_Exception("Unsupported number of arguments for wrapped closure"));
  },
  convertDartClosureToJS: function(closure, arity) {
    var $function;
    if (closure == null)
      return;
    $function = closure.$identity;
    if (!!$function)
      return $function;
    $function = (function(closure, arity, context, invoke) {  return function(a1, a2, a3, a4) {     return invoke(closure, context, arity, a1, a2, a3, a4);  };})(closure,arity,init.globalState.currentContext,H.invokeClosure);
    closure.$identity = $function;
    return $function;
  },
  Closure_fromTearOff: function(receiver, functions, reflectionInfo, isStatic, jsArguments, propertyName) {
    var $function, callName, functionType, $prototype, $constructor, t1, isIntercepted, trampoline, signatureFunction, getReceiver, i, stub, stubCallName, t2;
    $function = functions[0];
    $function.$stubName;
    callName = $function.$callName;
    $function.$reflectionInfo = reflectionInfo;
    functionType = H.ReflectionInfo_ReflectionInfo($function).functionType;
    $prototype = isStatic ? Object.create(new H.TearOffClosure().constructor.prototype) : Object.create(new H.BoundClosure(null, null, null, null).constructor.prototype);
    $prototype.$initialize = $prototype.constructor;
    if (isStatic)
      $constructor = function(){this.$initialize()};
    else if (typeof dart_precompiled == "function") {
      t1 = function(a,b,c,d) {this.$initialize(a,b,c,d)};
      $constructor = t1;
    } else {
      t1 = $.Closure_functionCounter;
      $.Closure_functionCounter = J.$add$ns(t1, 1);
      t1 = new Function("a", "b", "c", "d", "this.$initialize(a,b,c,d);" + t1);
      $constructor = t1;
    }
    $prototype.constructor = $constructor;
    $constructor.prototype = $prototype;
    t1 = !isStatic;
    if (t1) {
      isIntercepted = jsArguments.length == 1 && true;
      trampoline = H.Closure_forwardCallTo($function, isIntercepted);
    } else {
      $prototype.$name = propertyName;
      trampoline = $function;
      isIntercepted = false;
    }
    if (typeof functionType == "number")
      signatureFunction = (function(s){return function(){return init.metadata[s]}})(functionType);
    else if (t1 && typeof functionType == "function") {
      getReceiver = isIntercepted ? H.BoundClosure_receiverOf : H.BoundClosure_selfOf;
      signatureFunction = function(f,r){return function(){return f.apply({$receiver:r(this)},arguments)}}(functionType,getReceiver);
    } else
      throw H.wrapException("Error in reflectionInfo.");
    $prototype.$signature = signatureFunction;
    $prototype[callName] = trampoline;
    for (t1 = functions.length, i = 1; i < t1; ++i) {
      stub = functions[i];
      stubCallName = stub.$callName;
      if (stubCallName != null) {
        t2 = isStatic ? stub : H.Closure_forwardCallTo(stub, isIntercepted);
        $prototype[stubCallName] = t2;
      }
    }
    $prototype["call*"] = $function;
    return $constructor;
  },
  Closure_cspForwardCall: function(arity, $function) {
    var getSelf = H.BoundClosure_selfOf;
    switch (arity) {
      case 0:
        return function(F,S){return function(){return F.call(S(this))}}($function,getSelf);
      case 1:
        return function(F,S){return function(a){return F.call(S(this),a)}}($function,getSelf);
      case 2:
        return function(F,S){return function(a,b){return F.call(S(this),a,b)}}($function,getSelf);
      case 3:
        return function(F,S){return function(a,b,c){return F.call(S(this),a,b,c)}}($function,getSelf);
      case 4:
        return function(F,S){return function(a,b,c,d){return F.call(S(this),a,b,c,d)}}($function,getSelf);
      case 5:
        return function(F,S){return function(a,b,c,d,e){return F.call(S(this),a,b,c,d,e)}}($function,getSelf);
      default:
        return function(f,s){return function(){return f.apply(s(this),arguments)}}($function,getSelf);
    }
  },
  Closure_forwardCallTo: function($function, isIntercepted) {
    var arity, t1, t2, $arguments;
    if (isIntercepted)
      return H.Closure_forwardInterceptedCallTo($function);
    arity = $function.length;
    if (typeof dart_precompiled == "function")
      return H.Closure_cspForwardCall(arity, $function);
    else if (arity === 0) {
      t1 = $.BoundClosure_selfFieldNameCache;
      if (t1 == null) {
        t1 = H.BoundClosure_computeFieldNamed("self");
        $.BoundClosure_selfFieldNameCache = t1;
      }
      t1 = "return function(){return F.call(this." + H.S(t1) + ");";
      t2 = $.Closure_functionCounter;
      $.Closure_functionCounter = J.$add$ns(t2, 1);
      return new Function("F", t1 + H.S(t2) + "}")($function);
    } else if (1 <= arity && arity < 27) {
      $arguments = "abcdefghijklmnopqrstuvwxyz".split("").splice(0, arity).join(",");
      t1 = "return function(" + $arguments + "){return F.call(this.";
      t2 = $.BoundClosure_selfFieldNameCache;
      if (t2 == null) {
        t2 = H.BoundClosure_computeFieldNamed("self");
        $.BoundClosure_selfFieldNameCache = t2;
      }
      t2 = t1 + H.S(t2) + "," + $arguments + ");";
      t1 = $.Closure_functionCounter;
      $.Closure_functionCounter = J.$add$ns(t1, 1);
      return new Function("F", t2 + H.S(t1) + "}")($function);
    } else
      return H.Closure_cspForwardCall(arity, $function);
  },
  Closure_cspForwardInterceptedCall: function(arity, $name, $function) {
    var getSelf, getReceiver;
    getSelf = H.BoundClosure_selfOf;
    getReceiver = H.BoundClosure_receiverOf;
    switch (arity) {
      case 0:
        throw H.wrapException(H.RuntimeError$("Intercepted function with no arguments."));
      case 1:
        return function(n,s,r){return function(){return s(this)[n](r(this))}}($name,getSelf,getReceiver);
      case 2:
        return function(n,s,r){return function(a){return s(this)[n](r(this),a)}}($name,getSelf,getReceiver);
      case 3:
        return function(n,s,r){return function(a,b){return s(this)[n](r(this),a,b)}}($name,getSelf,getReceiver);
      case 4:
        return function(n,s,r){return function(a,b,c){return s(this)[n](r(this),a,b,c)}}($name,getSelf,getReceiver);
      case 5:
        return function(n,s,r){return function(a,b,c,d){return s(this)[n](r(this),a,b,c,d)}}($name,getSelf,getReceiver);
      case 6:
        return function(n,s,r){return function(a,b,c,d,e){return s(this)[n](r(this),a,b,c,d,e)}}($name,getSelf,getReceiver);
      default:
        return function(f,s,r,a){return function(){a=[r(this)];Array.prototype.push.apply(a,arguments);return f.apply(s(this),a)}}($function,getSelf,getReceiver);
    }
  },
  Closure_forwardInterceptedCallTo: function($function) {
    var stubName, arity, t1, t2, $arguments;
    stubName = $function.$stubName;
    arity = $function.length;
    if (typeof dart_precompiled == "function")
      return H.Closure_cspForwardInterceptedCall(arity, stubName, $function);
    else if (arity === 1) {
      t1 = "return this." + H.S(H.BoundClosure_selfFieldName()) + "." + stubName + "(this." + H.S(H.BoundClosure_receiverFieldName()) + ");";
      t2 = $.Closure_functionCounter;
      $.Closure_functionCounter = J.$add$ns(t2, 1);
      return new Function(t1 + H.S(t2));
    } else if (1 < arity && arity < 28) {
      $arguments = "abcdefghijklmnopqrstuvwxyz".split("").splice(0, arity - 1).join(",");
      t1 = "return function(" + $arguments + "){return this." + H.S(H.BoundClosure_selfFieldName()) + "." + stubName + "(this." + H.S(H.BoundClosure_receiverFieldName()) + "," + $arguments + ");";
      t2 = $.Closure_functionCounter;
      $.Closure_functionCounter = J.$add$ns(t2, 1);
      return new Function(t1 + H.S(t2) + "}")();
    } else
      return H.Closure_cspForwardInterceptedCall(arity, stubName, $function);
  },
  closureFromTearOff: function(receiver, functions, reflectionInfo, isStatic, jsArguments, $name) {
    functions.fixed$length = init;
    reflectionInfo.fixed$length = init;
    return H.Closure_fromTearOff(receiver, functions, reflectionInfo, !!isStatic, jsArguments, $name);
  },
  throwCyclicInit: function(staticName) {
    throw H.wrapException(P.CyclicInitializationError$("Cyclic initialization for static " + H.S(staticName)));
  },
  buildFunctionType: function(returnType, parameterTypes, optionalParameterTypes) {
    return new H.RuntimeFunctionType(returnType, parameterTypes, optionalParameterTypes, null);
  },
  getDynamicRuntimeType: function() {
    return C.C_DynamicRuntimeType;
  },
  createRuntimeType: function($name) {
    return new H.TypeImpl($name, null);
  },
  setRuntimeTypeInfo: function(target, typeInfo) {
    if (target != null)
      target.$builtinTypeInfo = typeInfo;
    return target;
  },
  getRuntimeTypeInfo: function(target) {
    if (target == null)
      return;
    return target.$builtinTypeInfo;
  },
  getRuntimeTypeArguments: function(target, substitutionName) {
    return H.substitute(target["$as" + H.S(substitutionName)], H.getRuntimeTypeInfo(target));
  },
  getRuntimeTypeArgument: function(target, substitutionName, index) {
    var $arguments = H.getRuntimeTypeArguments(target, substitutionName);
    return $arguments == null ? null : $arguments[index];
  },
  getTypeArgumentByIndex: function(target, index) {
    var rti = H.getRuntimeTypeInfo(target);
    return rti == null ? null : rti[index];
  },
  runtimeTypeToString: function(type, onTypeVariable) {
    if (type == null)
      return "dynamic";
    else if (typeof type === "object" && type !== null && type.constructor === Array)
      return type[0].builtin$cls + H.joinArguments(type, 1, onTypeVariable);
    else if (typeof type == "function")
      return type.builtin$cls;
    else if (typeof type === "number" && Math.floor(type) === type)
      return C.JSInt_methods.toString$0(type);
    else
      return;
  },
  joinArguments: function(types, startIndex, onTypeVariable) {
    var buffer, index, firstArgument, allDynamic, argument, str;
    if (types == null)
      return "";
    buffer = P.StringBuffer$("");
    for (index = startIndex, firstArgument = true, allDynamic = true; index < types.length; ++index) {
      if (firstArgument)
        firstArgument = false;
      else
        buffer._contents = buffer._contents + ", ";
      argument = types[index];
      if (argument != null)
        allDynamic = false;
      str = H.runtimeTypeToString(argument, onTypeVariable);
      str = typeof str === "string" ? str : H.S(str);
      buffer._contents = buffer._contents + str;
    }
    return allDynamic ? "" : "<" + H.S(buffer) + ">";
  },
  substitute: function(substitution, $arguments) {
    if (typeof substitution === "object" && substitution !== null && substitution.constructor === Array)
      $arguments = substitution;
    else if (typeof substitution == "function") {
      substitution = H.invokeOn(substitution, null, $arguments);
      if (typeof substitution === "object" && substitution !== null && substitution.constructor === Array)
        $arguments = substitution;
      else if (typeof substitution == "function")
        $arguments = H.invokeOn(substitution, null, $arguments);
    }
    return $arguments;
  },
  areSubtypes: function(s, t) {
    var len, i;
    if (s == null || t == null)
      return true;
    len = s.length;
    for (i = 0; i < len; ++i)
      if (!H.isSubtype(s[i], t[i]))
        return false;
    return true;
  },
  computeSignature: function(signature, context, contextName) {
    return H.invokeOn(signature, context, H.getRuntimeTypeArguments(context, contextName));
  },
  isSubtype: function(s, t) {
    var targetSignatureFunction, t1, typeOfS, t2, typeOfT, $name, substitution;
    if (s === t)
      return true;
    if (s == null || t == null)
      return true;
    if ("func" in t) {
      if (!("func" in s)) {
        if ("$is_" + H.S(t.func) in s)
          return true;
        targetSignatureFunction = s.$signature;
        if (targetSignatureFunction == null)
          return false;
        s = targetSignatureFunction.apply(s, null);
      }
      return H.isFunctionSubtype(s, t);
    }
    if (t.builtin$cls === "Function" && "func" in s)
      return true;
    t1 = typeof s === "object" && s !== null && s.constructor === Array;
    typeOfS = t1 ? s[0] : s;
    t2 = typeof t === "object" && t !== null && t.constructor === Array;
    typeOfT = t2 ? t[0] : t;
    $name = H.runtimeTypeToString(typeOfT, null);
    if (typeOfT !== typeOfS) {
      if (!("$is" + H.S($name) in typeOfS))
        return false;
      substitution = typeOfS["$as" + H.S(H.runtimeTypeToString(typeOfT, null))];
    } else
      substitution = null;
    if (!t1 && substitution == null || !t2)
      return true;
    t1 = t1 ? s.slice(1) : null;
    t2 = t2 ? t.slice(1) : null;
    return H.areSubtypes(H.substitute(substitution, t1), t2);
  },
  areAssignable: function(s, t, allowShorter) {
    var sLength, tLength, i, t1, t2;
    if (t == null && s == null)
      return true;
    if (t == null)
      return allowShorter;
    if (s == null)
      return false;
    sLength = s.length;
    tLength = t.length;
    if (allowShorter) {
      if (sLength < tLength)
        return false;
    } else if (sLength !== tLength)
      return false;
    for (i = 0; i < tLength; ++i) {
      t1 = s[i];
      t2 = t[i];
      if (!(H.isSubtype(t1, t2) || H.isSubtype(t2, t1)))
        return false;
    }
    return true;
  },
  areAssignableMaps: function(s, t) {
    var t1, names, i, $name, tType, sType;
    if (t == null)
      return true;
    if (s == null)
      return false;
    t1 = Object.getOwnPropertyNames(t);
    t1.fixed$length = init;
    names = t1;
    for (t1 = names.length, i = 0; i < t1; ++i) {
      $name = names[i];
      if (!Object.hasOwnProperty.call(s, $name))
        return false;
      tType = t[$name];
      sType = s[$name];
      if (!(H.isSubtype(tType, sType) || H.isSubtype(sType, tType)))
        return false;
    }
    return true;
  },
  isFunctionSubtype: function(s, t) {
    var sReturnType, tReturnType, sParameterTypes, tParameterTypes, sOptionalParameterTypes, tOptionalParameterTypes, sParametersLen, tParametersLen, sOptionalParametersLen, tOptionalParametersLen, pos, t1, t2, tPos, sPos;
    if (!("func" in s))
      return false;
    if ("void" in s) {
      if (!("void" in t) && "ret" in t)
        return false;
    } else if (!("void" in t)) {
      sReturnType = s.ret;
      tReturnType = t.ret;
      if (!(H.isSubtype(sReturnType, tReturnType) || H.isSubtype(tReturnType, sReturnType)))
        return false;
    }
    sParameterTypes = s.args;
    tParameterTypes = t.args;
    sOptionalParameterTypes = s.opt;
    tOptionalParameterTypes = t.opt;
    sParametersLen = sParameterTypes != null ? sParameterTypes.length : 0;
    tParametersLen = tParameterTypes != null ? tParameterTypes.length : 0;
    sOptionalParametersLen = sOptionalParameterTypes != null ? sOptionalParameterTypes.length : 0;
    tOptionalParametersLen = tOptionalParameterTypes != null ? tOptionalParameterTypes.length : 0;
    if (sParametersLen > tParametersLen)
      return false;
    if (sParametersLen + sOptionalParametersLen < tParametersLen + tOptionalParametersLen)
      return false;
    if (sParametersLen === tParametersLen) {
      if (!H.areAssignable(sParameterTypes, tParameterTypes, false))
        return false;
      if (!H.areAssignable(sOptionalParameterTypes, tOptionalParameterTypes, true))
        return false;
    } else {
      for (pos = 0; pos < sParametersLen; ++pos) {
        t1 = sParameterTypes[pos];
        t2 = tParameterTypes[pos];
        if (!(H.isSubtype(t1, t2) || H.isSubtype(t2, t1)))
          return false;
      }
      for (tPos = pos, sPos = 0; tPos < tParametersLen; ++sPos, ++tPos) {
        t1 = sOptionalParameterTypes[sPos];
        t2 = tParameterTypes[tPos];
        if (!(H.isSubtype(t1, t2) || H.isSubtype(t2, t1)))
          return false;
      }
      for (tPos = 0; tPos < tOptionalParametersLen; ++sPos, ++tPos) {
        t1 = sOptionalParameterTypes[sPos];
        t2 = tOptionalParameterTypes[tPos];
        if (!(H.isSubtype(t1, t2) || H.isSubtype(t2, t1)))
          return false;
      }
    }
    return H.areAssignableMaps(s.named, t.named);
  },
  invokeOn: function($function, receiver, $arguments) {
    return $function.apply(receiver, $arguments);
  },
  toStringForNativeObject: function(obj) {
    var t1 = $.getTagFunction;
    return "Instance of " + (t1 == null ? "<Unknown>" : t1.call$1(obj));
  },
  hashCodeForNativeObject: function(object) {
    return H.Primitives_objectHashCode(object);
  },
  defineProperty: function(obj, property, value) {
    Object.defineProperty(obj, property, {value: value, enumerable: false, writable: true, configurable: true});
  },
  lookupAndCacheInterceptor: function(obj) {
    var tag, record, interceptor, interceptorClass, mark, t1;
    tag = $.getTagFunction.call$1(obj);
    record = $.dispatchRecordsForInstanceTags[tag];
    if (record != null) {
      Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
      return record.i;
    }
    interceptor = $.interceptorsForUncacheableTags[tag];
    if (interceptor != null)
      return interceptor;
    interceptorClass = init.interceptorsByTag[tag];
    if (interceptorClass == null) {
      tag = $.alternateTagFunction.call$2(obj, tag);
      if (tag != null) {
        record = $.dispatchRecordsForInstanceTags[tag];
        if (record != null) {
          Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
          return record.i;
        }
        interceptor = $.interceptorsForUncacheableTags[tag];
        if (interceptor != null)
          return interceptor;
        interceptorClass = init.interceptorsByTag[tag];
      }
    }
    if (interceptorClass == null)
      return;
    interceptor = interceptorClass.prototype;
    mark = tag[0];
    if (mark === "!") {
      record = H.makeLeafDispatchRecord(interceptor);
      $.dispatchRecordsForInstanceTags[tag] = record;
      Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
      return record.i;
    }
    if (mark === "~") {
      $.interceptorsForUncacheableTags[tag] = interceptor;
      return interceptor;
    }
    if (mark === "-") {
      t1 = H.makeLeafDispatchRecord(interceptor);
      Object.defineProperty(Object.getPrototypeOf(obj), init.dispatchPropertyName, {value: t1, enumerable: false, writable: true, configurable: true});
      return t1.i;
    }
    if (mark === "+")
      return H.patchInteriorProto(obj, interceptor);
    if (mark === "*")
      throw H.wrapException(P.UnimplementedError$(tag));
    if (init.leafTags[tag] === true) {
      t1 = H.makeLeafDispatchRecord(interceptor);
      Object.defineProperty(Object.getPrototypeOf(obj), init.dispatchPropertyName, {value: t1, enumerable: false, writable: true, configurable: true});
      return t1.i;
    } else
      return H.patchInteriorProto(obj, interceptor);
  },
  patchInteriorProto: function(obj, interceptor) {
    var proto, record;
    proto = Object.getPrototypeOf(obj);
    record = J.makeDispatchRecord(interceptor, proto, null, null);
    Object.defineProperty(proto, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
    return interceptor;
  },
  makeLeafDispatchRecord: function(interceptor) {
    return J.makeDispatchRecord(interceptor, false, null, !!interceptor.$isJavaScriptIndexingBehavior);
  },
  makeDefaultDispatchRecord: function(tag, interceptorClass, proto) {
    var interceptor = interceptorClass.prototype;
    if (init.leafTags[tag] === true)
      return J.makeDispatchRecord(interceptor, false, null, !!interceptor.$isJavaScriptIndexingBehavior);
    else
      return J.makeDispatchRecord(interceptor, proto, null, null);
  },
  initNativeDispatch: function() {
    if (true === $.initNativeDispatchFlag)
      return;
    $.initNativeDispatchFlag = true;
    H.initNativeDispatchContinue();
  },
  initNativeDispatchContinue: function() {
    var map, tags, i, tag, proto, record, interceptorClass;
    $.dispatchRecordsForInstanceTags = Object.create(null);
    $.interceptorsForUncacheableTags = Object.create(null);
    H.initHooks();
    map = init.interceptorsByTag;
    tags = Object.getOwnPropertyNames(map);
    if (typeof window != "undefined") {
      window;
      for (i = 0; i < tags.length; ++i) {
        tag = tags[i];
        proto = $.prototypeForTagFunction.call$1(tag);
        if (proto != null) {
          record = H.makeDefaultDispatchRecord(tag, map[tag], proto);
          if (record != null)
            Object.defineProperty(proto, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
        }
      }
    }
    for (i = 0; i < tags.length; ++i) {
      tag = tags[i];
      if (/^[A-Za-z_]/.test(tag)) {
        interceptorClass = map[tag];
        map["!" + tag] = interceptorClass;
        map["~" + tag] = interceptorClass;
        map["-" + tag] = interceptorClass;
        map["+" + tag] = interceptorClass;
        map["*" + tag] = interceptorClass;
      }
    }
  },
  initHooks: function() {
    var hooks, transformers, i, transformer, getTag, getUnknownTag, prototypeForTag;
    hooks = C.JS_CONST_aQP();
    hooks = H.applyHooksTransformer(C.JS_CONST_0, H.applyHooksTransformer(C.JS_CONST_rr7, H.applyHooksTransformer(C.JS_CONST_Fs4, H.applyHooksTransformer(C.JS_CONST_Fs4, H.applyHooksTransformer(C.JS_CONST_gkc, H.applyHooksTransformer(C.JS_CONST_U4w, H.applyHooksTransformer(C.JS_CONST_QJm(C.JS_CONST_IX5), hooks)))))));
    if (typeof dartNativeDispatchHooksTransformer != "undefined") {
      transformers = dartNativeDispatchHooksTransformer;
      if (typeof transformers == "function")
        transformers = [transformers];
      if (transformers.constructor == Array)
        for (i = 0; i < transformers.length; ++i) {
          transformer = transformers[i];
          if (typeof transformer == "function")
            hooks = transformer(hooks) || hooks;
        }
    }
    getTag = hooks.getTag;
    getUnknownTag = hooks.getUnknownTag;
    prototypeForTag = hooks.prototypeForTag;
    $.getTagFunction = new H.initHooks_closure(getTag);
    $.alternateTagFunction = new H.initHooks_closure0(getUnknownTag);
    $.prototypeForTagFunction = new H.initHooks_closure1(prototypeForTag);
  },
  applyHooksTransformer: function(transformer, hooks) {
    return transformer(hooks) || hooks;
  },
  ReflectionInfo: {
    "": "Object;jsFunction,data,isAccessor,requiredParameterCount,optionalParameterCount,areOptionalParametersNamed,functionType",
    static: {"": "ReflectionInfo_REQUIRED_PARAMETERS_INFO,ReflectionInfo_OPTIONAL_PARAMETERS_INFO,ReflectionInfo_FUNCTION_TYPE_INDEX,ReflectionInfo_FIRST_DEFAULT_ARGUMENT", ReflectionInfo_ReflectionInfo: function(jsFunction) {
        var data, requiredParametersInfo, requiredParameterCount, optionalParametersInfo;
        data = jsFunction.$reflectionInfo;
        if (data == null)
          return;
        data.fixed$length = init;
        data = data;
        requiredParametersInfo = data[0];
        requiredParameterCount = requiredParametersInfo >> 1;
        optionalParametersInfo = data[1];
        return new H.ReflectionInfo(jsFunction, data, (requiredParametersInfo & 1) === 1, requiredParameterCount, optionalParametersInfo >> 1, (optionalParametersInfo & 1) === 1, data[2]);
      }}
  },
  TypeErrorDecoder: {
    "": "Object;_pattern,_arguments,_argumentsExpr,_expr,_method,_receiver<",
    matchTypeError$1: function(message) {
      var match, result, t1;
      match = new RegExp(this._pattern).exec(message);
      if (match == null)
        return;
      result = {};
      t1 = this._arguments;
      if (t1 !== -1)
        result.arguments = match[t1 + 1];
      t1 = this._argumentsExpr;
      if (t1 !== -1)
        result.argumentsExpr = match[t1 + 1];
      t1 = this._expr;
      if (t1 !== -1)
        result.expr = match[t1 + 1];
      t1 = this._method;
      if (t1 !== -1)
        result.method = match[t1 + 1];
      t1 = this._receiver;
      if (t1 !== -1)
        result.receiver = match[t1 + 1];
      return result;
    },
    static: {"": "TypeErrorDecoder_noSuchMethodPattern,TypeErrorDecoder_notClosurePattern,TypeErrorDecoder_nullCallPattern,TypeErrorDecoder_nullLiteralCallPattern,TypeErrorDecoder_undefinedCallPattern,TypeErrorDecoder_undefinedLiteralCallPattern,TypeErrorDecoder_nullPropertyPattern,TypeErrorDecoder_nullLiteralPropertyPattern,TypeErrorDecoder_undefinedPropertyPattern,TypeErrorDecoder_undefinedLiteralPropertyPattern", TypeErrorDecoder_extractPattern: function(message) {
        var match, $arguments, argumentsExpr, expr, method, receiver;
        message = message.replace(String({}), '$receiver$').replace(new RegExp("[[\\]{}()*+?.\\\\^$|]", 'g'), '\\$&');
        match = message.match(/\\\$[a-zA-Z]+\\\$/g);
        if (match == null)
          match = [];
        $arguments = match.indexOf("\\$arguments\\$");
        argumentsExpr = match.indexOf("\\$argumentsExpr\\$");
        expr = match.indexOf("\\$expr\\$");
        method = match.indexOf("\\$method\\$");
        receiver = match.indexOf("\\$receiver\\$");
        return new H.TypeErrorDecoder(message.replace('\\$arguments\\$', '((?:x|[^x])*)').replace('\\$argumentsExpr\\$', '((?:x|[^x])*)').replace('\\$expr\\$', '((?:x|[^x])*)').replace('\\$method\\$', '((?:x|[^x])*)').replace('\\$receiver\\$', '((?:x|[^x])*)'), $arguments, argumentsExpr, expr, method, receiver);
      }, TypeErrorDecoder_provokeCallErrorOn: function(expression) {
        return function($expr$) {
  var $argumentsExpr$ = '$arguments$'
  try {
    $expr$.$method$($argumentsExpr$);
  } catch (e) {
    return e.message;
  }
}(expression);
      }, TypeErrorDecoder_provokePropertyErrorOn: function(expression) {
        return function($expr$) {
  try {
    $expr$.$method$;
  } catch (e) {
    return e.message;
  }
}(expression);
      }}
  },
  NullError: {
    "": "Error;_message,_method",
    toString$0: function(_) {
      var t1 = this._method;
      if (t1 == null)
        return "NullError: " + H.S(this._message);
      return "NullError: Cannot call \"" + H.S(t1) + "\" on null";
    },
    $isError: true
  },
  JsNoSuchMethodError: {
    "": "Error;_message,_method,_receiver<",
    toString$0: function(_) {
      var t1, t2;
      t1 = this._method;
      if (t1 == null)
        return "NoSuchMethodError: " + H.S(this._message);
      t2 = this._receiver;
      if (t2 == null)
        return "NoSuchMethodError: Cannot call \"" + t1 + "\" (" + H.S(this._message) + ")";
      return "NoSuchMethodError: Cannot call \"" + t1 + "\" on \"" + t2 + "\" (" + H.S(this._message) + ")";
    },
    $isError: true,
    static: {JsNoSuchMethodError$: function(_message, match) {
        var t1, t2;
        t1 = match == null;
        t2 = t1 ? null : match.method;
        t1 = t1 ? null : match.receiver;
        return new H.JsNoSuchMethodError(_message, t2, t1);
      }}
  },
  UnknownJsTypeError: {
    "": "Error;_message",
    toString$0: function(_) {
      var t1 = this._message;
      return C.JSString_methods.get$isEmpty(t1) ? "Error" : "Error: " + t1;
    }
  },
  unwrapException_saveStackTrace: {
    "": "Closure:10;ex_0",
    call$1: function(error) {
      var t1 = J.getInterceptor(error);
      if (typeof error === "object" && error !== null && !!t1.$isError)
        if (error.$thrownJsError == null)
          error.$thrownJsError = this.ex_0;
      return error;
    }
  },
  _StackTrace: {
    "": "Object;_exception,_trace",
    toString$0: function(_) {
      var t1, trace;
      t1 = this._trace;
      if (t1 != null)
        return t1;
      t1 = this._exception;
      trace = typeof t1 === "object" ? t1.stack : null;
      t1 = trace == null ? "" : trace;
      this._trace = t1;
      return t1;
    }
  },
  invokeClosure_closure: {
    "": "Closure:8;closure_0",
    call$0: function() {
      return this.closure_0.call$0();
    }
  },
  invokeClosure_closure0: {
    "": "Closure:8;closure_1,arg1_2",
    call$0: function() {
      return this.closure_1.call$1(this.arg1_2);
    }
  },
  invokeClosure_closure1: {
    "": "Closure:8;closure_3,arg1_4,arg2_5",
    call$0: function() {
      return this.closure_3.call$2(this.arg1_4, this.arg2_5);
    }
  },
  invokeClosure_closure2: {
    "": "Closure:8;closure_6,arg1_7,arg2_8,arg3_9",
    call$0: function() {
      return this.closure_6.call$3(this.arg1_7, this.arg2_8, this.arg3_9);
    }
  },
  invokeClosure_closure3: {
    "": "Closure:8;closure_10,arg1_11,arg2_12,arg3_13,arg4_14",
    call$0: function() {
      return this.closure_10.call$4(this.arg1_11, this.arg2_12, this.arg3_13, this.arg4_14);
    }
  },
  Closure: {
    "": "Object;",
    toString$0: function(_) {
      return "Closure";
    }
  },
  TearOffClosure: {
    "": "Closure;"
  },
  BoundClosure: {
    "": "TearOffClosure;_self<,_target,_receiver<,__js_helper$_name",
    $eq: function(_, other) {
      var t1;
      if (other == null)
        return false;
      if (this === other)
        return true;
      t1 = J.getInterceptor(other);
      if (typeof other !== "object" || other === null || !t1.$isBoundClosure)
        return false;
      return this._self === other._self && this._target === other._target && this._receiver === other._receiver;
    },
    get$hashCode: function(_) {
      var t1, receiverHashCode;
      t1 = this._receiver;
      if (t1 == null)
        receiverHashCode = H.Primitives_objectHashCode(this._self);
      else
        receiverHashCode = typeof t1 !== "object" ? J.get$hashCode$(t1) : H.Primitives_objectHashCode(t1);
      return (receiverHashCode ^ H.Primitives_objectHashCode(this._target)) >>> 0;
    },
    $isBoundClosure: true,
    static: {"": "BoundClosure_selfFieldNameCache,BoundClosure_receiverFieldNameCache", BoundClosure_selfOf: function(closure) {
        return closure.get$_self();
      }, BoundClosure_receiverOf: function(closure) {
        return closure.get$_receiver();
      }, BoundClosure_selfFieldName: function() {
        var t1 = $.BoundClosure_selfFieldNameCache;
        if (t1 == null) {
          t1 = H.BoundClosure_computeFieldNamed("self");
          $.BoundClosure_selfFieldNameCache = t1;
        }
        return t1;
      }, BoundClosure_receiverFieldName: function() {
        var t1 = $.BoundClosure_receiverFieldNameCache;
        if (t1 == null) {
          t1 = H.BoundClosure_computeFieldNamed("receiver");
          $.BoundClosure_receiverFieldNameCache = t1;
        }
        return t1;
      }, BoundClosure_computeFieldNamed: function(fieldName) {
        var template, t1, names, i, $name;
        template = new H.BoundClosure("self", "target", "receiver", "name");
        t1 = Object.getOwnPropertyNames(template);
        t1.fixed$length = init;
        names = t1;
        for (t1 = names.length, i = 0; i < t1; ++i) {
          $name = names[i];
          if (template[$name] === fieldName)
            return $name;
        }
      }}
  },
  RuntimeError: {
    "": "Error;message",
    toString$0: function(_) {
      return "RuntimeError: " + H.S(this.message);
    },
    static: {RuntimeError$: function(message) {
        return new H.RuntimeError(message);
      }}
  },
  RuntimeType: {
    "": "Object;"
  },
  RuntimeFunctionType: {
    "": "RuntimeType;returnType,parameterTypes,optionalParameterTypes,namedParameters",
    _isTest$1: function(expression) {
      var functionTypeObject = this._extractFunctionTypeObjectFrom$1(expression);
      return functionTypeObject == null ? false : H.isFunctionSubtype(functionTypeObject, this.toRti$0());
    },
    _extractFunctionTypeObjectFrom$1: function(o) {
      var interceptor = J.getInterceptor(o);
      return "$signature" in interceptor ? interceptor.$signature() : null;
    },
    toRti$0: function() {
      var result, t1, t2, namedRti, keys, i, $name;
      result = { "func": "dynafunc" };
      t1 = this.returnType;
      t2 = J.getInterceptor(t1);
      if (typeof t1 === "object" && t1 !== null && !!t2.$isVoidRuntimeType)
        result.void = true;
      else if (typeof t1 !== "object" || t1 === null || !t2.$isDynamicRuntimeType)
        result.ret = t1.toRti$0();
      t1 = this.parameterTypes;
      if (t1 != null && t1.length !== 0)
        result.args = H.RuntimeFunctionType_listToRti(t1);
      t1 = this.optionalParameterTypes;
      if (t1 != null && t1.length !== 0)
        result.opt = H.RuntimeFunctionType_listToRti(t1);
      t1 = this.namedParameters;
      if (t1 != null) {
        namedRti = {};
        keys = H.extractKeys(t1);
        for (t2 = keys.length, i = 0; i < t2; ++i) {
          $name = keys[i];
          namedRti[$name] = t1[$name].toRti$0();
        }
        result.named = namedRti;
      }
      return result;
    },
    toString$0: function(_) {
      var t1, t2, result, needsComma, i, type, keys, $name;
      t1 = this.parameterTypes;
      if (t1 != null)
        for (t2 = t1.length, result = "(", needsComma = false, i = 0; i < t2; ++i, needsComma = true) {
          type = t1[i];
          if (needsComma)
            result += ", ";
          result += H.S(type);
        }
      else {
        result = "(";
        needsComma = false;
      }
      t1 = this.optionalParameterTypes;
      if (t1 != null && t1.length !== 0) {
        result = (needsComma ? result + ", " : result) + "[";
        for (t2 = t1.length, needsComma = false, i = 0; i < t2; ++i, needsComma = true) {
          type = t1[i];
          if (needsComma)
            result += ", ";
          result += H.S(type);
        }
        result += "]";
      } else {
        t1 = this.namedParameters;
        if (t1 != null) {
          result = (needsComma ? result + ", " : result) + "{";
          keys = H.extractKeys(t1);
          for (t2 = keys.length, needsComma = false, i = 0; i < t2; ++i, needsComma = true) {
            $name = keys[i];
            if (needsComma)
              result += ", ";
            result += H.S(t1[$name].toRti$0()) + " " + $name;
          }
          result += "}";
        }
      }
      return result + (") -> " + H.S(this.returnType));
    },
    static: {"": "RuntimeFunctionType_inAssert", RuntimeFunctionType_listToRti: function(list) {
        var result, t1, i;
        list = list;
        result = [];
        for (t1 = list.length, i = 0; i < t1; ++i)
          result.push(list[i].toRti$0());
        return result;
      }}
  },
  DynamicRuntimeType: {
    "": "RuntimeType;",
    toString$0: function(_) {
      return "dynamic";
    },
    toRti$0: function() {
      return;
    },
    $isDynamicRuntimeType: true
  },
  TypeImpl: {
    "": "Object;_typeName,_unmangledName",
    toString$0: function(_) {
      var t1, unmangledName, unmangledName0;
      t1 = this._unmangledName;
      if (t1 != null)
        return t1;
      unmangledName = this._typeName;
      unmangledName0 = init.mangledGlobalNames[unmangledName];
      unmangledName = unmangledName0 == null ? unmangledName : unmangledName0;
      this._unmangledName = unmangledName;
      return unmangledName;
    },
    get$hashCode: function(_) {
      return J.get$hashCode$(this._typeName);
    },
    $eq: function(_, other) {
      var t1;
      if (other == null)
        return false;
      t1 = J.getInterceptor(other);
      return typeof other === "object" && other !== null && !!t1.$isTypeImpl && J.$eq(this._typeName, other._typeName);
    },
    $isTypeImpl: true
  },
  initHooks_closure: {
    "": "Closure:10;getTag_0",
    call$1: function(o) {
      return this.getTag_0(o);
    }
  },
  initHooks_closure0: {
    "": "Closure:11;getUnknownTag_1",
    call$2: function(o, tag) {
      return this.getUnknownTag_1(o, tag);
    }
  },
  initHooks_closure1: {
    "": "Closure:12;prototypeForTag_2",
    call$1: function(tag) {
      return this.prototypeForTag_2(tag);
    }
  }
}],
["clock", "clock.dart", , Q, {
  "": "",
  main: [function() {
    var t1, t2, t3, t4;
    t1 = H.setRuntimeTypeInfo(Array(2), [Q.ClockNumber]);
    t2 = H.setRuntimeTypeInfo(Array(2), [Q.ClockNumber]);
    t3 = H.setRuntimeTypeInfo(Array(2), [Q.ClockNumber]);
    t4 = new Q.Balls(null, P.DateTime$_now().millisecondsSinceEpoch, H.setRuntimeTypeInfo([], [Q.Ball]));
    t4.Balls$0();
    new Q.CountDownClock(t1, t2, t3, -1, -1, -1, t4).CountDownClock$0();
  }, "call$0", "main$closure", 0, 0, 0],
  setElementPosition: function(elem, x, y) {
    J.set$left$x(elem.style, H.S(x) + "px");
    J.set$top$x(elem.style, H.S(y) + "px");
  },
  Balls: {
    "": "Object;root,lastTime,balls",
    tick$1: function(now) {
      var t1, t2, t3, delta;
      t1 = J.getInterceptor$n(now);
      t2 = J.$add$ns(t1.$sub(now, this.lastTime), 0.01);
      if (typeof t2 !== "number")
        return H.iae(t2);
      t2 = 1000 / t2;
      t3 = $.fpsAverage;
      if (t3 == null)
        $.fpsAverage = t2;
      else {
        if (typeof t3 !== "number")
          return t3.$mul();
        $.fpsAverage = t2 * 0.05 + t3 * 0.95;
        document.querySelector("#notes").textContent = "" + C.JSInt_methods.toInt$0(C.JSNumber_methods.toInt$0(J.roundToDouble$0$n($.fpsAverage))) + " fps";
      }
      t1 = t1.$sub(now, this.lastTime);
      if (typeof t1 !== "number")
        return t1.$div();
      delta = P.min(t1 / 1000, 0.1);
      this.lastTime = now;
      H.IterableMixinWorkaround_removeWhereList(this.balls, new Q.Balls_tick_closure(delta));
      this.collideBalls$1(delta);
    },
    collideBalls$1: function(delta) {
      H.IterableMixinWorkaround_forEach(this.balls, new Q.Balls_collideBalls_closure(this, delta));
    },
    Balls$0: function() {
      var t1 = document.createElement("div", null);
      this.root = t1;
      document.body.appendChild(t1);
      J.set$position$x(this.root.style, "absolute");
      t1 = this.root;
      Q.setElementPosition(t1, 0, 0);
      J.set$right$x(t1.style, "0px");
      J.set$bottom$x(t1.style, "0px");
    },
    static: {"": "Balls_RADIUS2,Balls_LT_GRAY_BALL_INDEX,Balls_GREEN_BALL_INDEX,Balls_BLUE_BALL_INDEX,Balls_DK_GRAY_BALL_INDEX,Balls_RED_BALL_INDEX,Balls_MD_GRAY_BALL_INDEX,Balls_PNGS"}
  },
  Balls_tick_closure: {
    "": "Closure:10;delta_0",
    call$1: function(ball) {
      return ball.tick$1(this.delta_0);
    }
  },
  Balls_collideBalls_closure: {
    "": "Closure:10;this_0,delta_1",
    call$1: function(b0) {
      var t1 = this.this_0;
      H.IterableMixinWorkaround_forEach(t1.balls, new Q.Balls_collideBalls__closure(t1, this.delta_1, b0));
    }
  },
  Balls_collideBalls__closure: {
    "": "Closure:10;this_2,delta_3,b0_4",
    call$1: function(b1) {
      var t1, t2, t3, t4, t5, dx, dy, d2, t6, t7, t8, t9, t10, ndx, ndy, d, impactSpeed;
      t1 = this.b0_4;
      t2 = J.getInterceptor$x(t1);
      t3 = t2.get$x(t1);
      t4 = J.getInterceptor$x(b1);
      t5 = t4.get$x(b1);
      if (typeof t3 !== "number")
        return t3.$sub();
      if (typeof t5 !== "number")
        return H.iae(t5);
      dx = Math.abs(t3 - t5);
      t5 = t2.get$y(t1);
      t3 = t4.get$y(b1);
      if (typeof t5 !== "number")
        return t5.$sub();
      if (typeof t3 !== "number")
        return H.iae(t3);
      dy = Math.abs(t5 - t3);
      d2 = dx * dx + dy * dy;
      if (d2 < 196) {
        t3 = this.delta_3;
        t2 = t2.get$x(t1);
        t5 = t1.get$vx();
        if (typeof t2 !== "number")
          return t2.$add();
        t6 = t1.y;
        t7 = t1.vy;
        if (typeof t6 !== "number")
          return t6.$add();
        t4 = t4.get$x(b1);
        t8 = b1.get$vx();
        if (typeof t4 !== "number")
          return t4.$add();
        t9 = b1.y;
        t10 = b1.vy;
        if (typeof t9 !== "number")
          return t9.$add();
        ndx = Math.abs(t2 + t5 * t3 - (t4 + t8 * t3));
        ndy = Math.abs(t6 + t7 * t3 - (t9 + t10 * t3));
        if (ndx * ndx + ndy * ndy > d2)
          return;
        d = Math.sqrt(d2);
        if (d === 0)
          return;
        dx /= d;
        dy /= d;
        t2 = t1.get$vx();
        impactSpeed = (t2 - b1.vx) * dx + (t1.vy - b1.vy) * dy;
        t3 = dx * impactSpeed;
        t1.vx = t2 - t3;
        t2 = dy * impactSpeed;
        t1.vy = t1.vy - t2;
        b1.vx = b1.vx + t3;
        b1.vy = b1.vy + t2;
      }
    }
  },
  Ball: {
    "": "Object;root,elem,x>,y>,vx<,vy,ax,ay,age",
    tick$1: function(delta) {
      var t1, t2, t3, t4;
      t1 = this.vx + this.ax * delta;
      this.vx = t1;
      t2 = this.vy + this.ay * delta;
      this.vy = t2;
      t3 = this.x;
      if (typeof t3 !== "number")
        return t3.$add();
      t1 = t3 + t1 * delta;
      this.x = t1;
      t3 = this.y;
      if (typeof t3 !== "number")
        return t3.$add();
      t3 += t2 * delta;
      this.y = t3;
      if (!(t1 < 14)) {
        t4 = window.innerWidth;
        if (typeof t4 !== "number")
          return H.iae(t4);
        t4 = t1 > t4;
      } else
        t4 = true;
      if (t4) {
        t1 = this.elem;
        t2 = t1.parentNode;
        if (t2 != null)
          t2.removeChild(t1);
        return true;
      }
      t4 = window.innerHeight;
      if (typeof t4 !== "number")
        return H.iae(t4);
      if (t3 > t4) {
        t3 = window.innerHeight;
        t3.toString;
        this.y = t3;
        this.vy = t2 * -0.8;
        t2 = t3;
      } else
        t2 = t3;
      t3 = this.elem;
      if (typeof t2 !== "number")
        return t2.$sub();
      Q.setElementPosition(t3, t1 - 14, t2 - 14);
      return false;
    },
    Ball$4: function(root, x, y, color) {
      var t1;
      if (color >= 7)
        return H.ioore(C.List_8eb, color);
      t1 = W.ImageElement_ImageElement(null, C.List_8eb[color], null);
      this.elem = t1;
      J.set$position$x(t1.style, "absolute");
      Q.setElementPosition(this.elem, this.x, this.y);
      this.root.appendChild(this.elem);
      this.ax = 0;
      this.ay = 400;
      this.vx = Q.Ball_randomVelocity();
      this.vy = Q.Ball_randomVelocity();
    },
    static: {"": "Ball_GRAVITY,Ball_RESTITUTION,Ball_MIN_VELOCITY,Ball_INIT_VELOCITY,Ball_RADIUS,Ball_random", Ball_randomVelocity: function() {
        var t1 = $.Ball_random;
        if (t1 == null) {
          $.Ball_random = C.C__JSRandom;
          t1 = C.C__JSRandom;
        }
        return (t1.nextDouble$0() - 0.5) * 800;
      }}
  },
  CountDownClock: {
    "": "Object;hours,minutes,seconds,displayedHour,displayedMinute,displayedSecond,balls",
    tick$1: [function(time) {
      var t1, t2;
      this.updateTime$1(P.DateTime$_now());
      this.balls.tick$1(time);
      t1 = window;
      t2 = this.get$tick();
      C.Window_methods._ensureRequestAnimationFrame$0(t1);
      C.Window_methods._requestAnimationFrame$1(t1, W._wrapZone(t2));
    }, "call$1", "get$tick", 2, 0, 13],
    updateTime$1: function(now) {
      if (H.Primitives_getHours(now) !== this.displayedHour) {
        this.setDigits$2(this.pad2$1(H.Primitives_getHours(now)), this.hours);
        this.displayedHour = H.Primitives_getHours(now);
      }
      if (H.Primitives_getMinutes(now) !== this.displayedMinute) {
        this.setDigits$2(this.pad2$1(H.Primitives_getMinutes(now)), this.minutes);
        this.displayedMinute = H.Primitives_getMinutes(now);
      }
      if (H.Primitives_getSeconds(now) !== this.displayedSecond) {
        this.setDigits$2(this.pad2$1(H.Primitives_getSeconds(now)), this.seconds);
        this.displayedSecond = H.Primitives_getSeconds(now);
      }
    },
    setDigits$2: function(digits, numbers) {
      var t1, i, t2, digit;
      for (t1 = digits.length, i = 0; i < 2; ++i) {
        if (i >= t1)
          H.throwExpression(P.RangeError$value(i));
        t2 = digits.charCodeAt(i);
        digit = t2 - "0".charCodeAt(0);
        t2 = numbers[i];
        if (digit < 0 || digit >= 10)
          return H.ioore(C.List_e3I, digit);
        t2.setPixels$1(C.List_e3I[digit]);
      }
    },
    pad2$1: function(number) {
      if (number < 10)
        return "0" + number;
      return "" + number;
    },
    createNumbers$3: function($parent, width, height) {
      var root, x, y, t1, i, t2;
      root = document.createElement("div", null);
      J.set$position$x(root.style, "relative");
      J.set$textAlign$x(root.style, "center");
      document.querySelector("#canvas-content").appendChild(root);
      if (typeof width !== "number")
        return width.$sub();
      x = (width - 627) / 2;
      if (typeof height !== "number")
        return height.$sub();
      y = (height - 133) / 3;
      for (t1 = this.hours, i = 0; i < 2; ++i) {
        t2 = Q.ClockNumber$(this, x, 2);
        t1[i] = t2;
        root.appendChild(t2.root);
        t2 = t1[i].root;
        J.set$left$x(t2.style, "" + x + "px");
        J.set$top$x(t2.style, "" + y + "px");
        x += 95;
      }
      root.appendChild(Q.Colon$(x, y).root);
      x += 38;
      for (t1 = this.minutes, i = 0; i < 2; ++i) {
        t2 = Q.ClockNumber$(this, x, 5);
        t1[i] = t2;
        root.appendChild(t2.root);
        t2 = t1[i].root;
        J.set$left$x(t2.style, "" + x + "px");
        J.set$top$x(t2.style, "" + y + "px");
        x += 95;
      }
      root.appendChild(Q.Colon$(x, y).root);
      x += 38;
      for (t1 = this.seconds, i = 0; i < 2; ++i) {
        t2 = Q.ClockNumber$(this, x, 1);
        t1[i] = t2;
        root.appendChild(t2.root);
        t2 = t1[i].root;
        J.set$left$x(t2.style, "" + x + "px");
        J.set$top$x(t2.style, "" + y + "px");
        x += 95;
      }
    },
    CountDownClock$0: function() {
      var $parent, t1, t2;
      $parent = document.querySelector("#canvas-content");
      this.createNumbers$3($parent, $parent.clientWidth, $parent.clientHeight);
      this.updateTime$1(P.DateTime$_now());
      t1 = window;
      t2 = this.get$tick();
      C.Window_methods._ensureRequestAnimationFrame$0(t1);
      C.Window_methods._requestAnimationFrame$1(t1, W._wrapZone(t2));
    },
    static: {"": "CountDownClock_NUMBER_SPACING,CountDownClock_BALL_WIDTH,CountDownClock_BALL_HEIGHT"}
  },
  ClockNumber: {
    "": "Object;app,root,imgs,pixels,ballColor",
    setPixels$1: function(px) {
      var t1, y, x, img, t2;
      for (t1 = this.ballColor, y = 0; y < 7; ++y)
        for (x = 0; x < 4; ++x) {
          img = this.imgs[y][x];
          t2 = this.pixels;
          if (t2 != null)
            if (t2[y][x] !== 0 && px[y][x] === 0)
              P.scheduleMicrotask(new Q.ClockNumber_setPixels_closure(this, img));
          if (px[y][x] !== 0) {
            if (t1 >= 7)
              return H.ioore(C.List_8eb, t1);
            t2 = C.List_8eb[t1];
          } else
            t2 = "images/ball-c9c9c9.png";
          J.set$src$x(img, t2);
        }
      this.pixels = px;
    },
    ClockNumber$3: function(app, pos, ballColor) {
      var t1, y, t2, x;
      this.imgs = H.setRuntimeTypeInfo(Array(7), [[J.JSArray, W.ImageElement]]);
      t1 = document.createElement("div", null);
      this.root = t1;
      J.set$position$x(t1.style, "absolute");
      Q.setElementPosition(this.root, pos, 0);
      for (y = 0; y < 7; ++y) {
        t1 = this.imgs;
        t2 = Array(4);
        t2.$builtinTypeInfo = [W.ImageElement];
        t1[y] = t2;
      }
      for (y = 0; y < 7; ++y)
        for (t1 = y * 19, x = 0; x < 4; ++x) {
          this.imgs[y][x] = W.ImageElement_ImageElement(null, null, null);
          t2 = this.root;
          t2.toString;
          t2.appendChild(this.imgs[y][x]);
          J.set$position$x(this.imgs[y][x].style, "absolute");
          t2 = this.imgs[y][x];
          J.set$left$x(t2.style, "" + x * 19 + "px");
          J.set$top$x(t2.style, "" + t1 + "px");
        }
    },
    static: {"": "ClockNumber_WIDTH,ClockNumber_HEIGHT", ClockNumber$: function(app, pos, ballColor) {
        var t1 = new Q.ClockNumber(app, null, null, null, ballColor);
        t1.ClockNumber$3(app, pos, ballColor);
        return t1;
      }}
  },
  ClockNumber_setPixels_closure: {
    "": "Closure:8;this_0,img_1",
    call$0: function() {
      var r, t1, absx, absy, t2, t3, t4;
      r = this.img_1.getBoundingClientRect();
      t1 = J.getInterceptor$x(r);
      absx = t1.get$left(r);
      absy = t1.get$top(r);
      t1 = this.this_0;
      t2 = t1.app.balls;
      t3 = t2.root;
      t4 = new Q.Ball(t3, null, absx, absy, null, null, null, null, null);
      t4.Ball$4(t3, absx, absy, t1.ballColor);
      t2.balls.push(t4);
    }
  },
  Colon: {
    "": "Object;root",
    Colon$2: function(xpos, ypos) {
      var t1, dot;
      t1 = document.createElement("div", null);
      this.root = t1;
      J.set$position$x(t1.style, "absolute");
      Q.setElementPosition(this.root, xpos, ypos);
      dot = W.ImageElement_ImageElement(null, "images/ball-b6b4b5.png", null);
      this.root.appendChild(dot);
      J.set$position$x(dot.style, "absolute");
      Q.setElementPosition(dot, 0, 38);
      dot = W.ImageElement_ImageElement(null, "images/ball-b6b4b5.png", null);
      this.root.appendChild(dot);
      J.set$position$x(dot.style, "absolute");
      Q.setElementPosition(dot, 0, 76);
    },
    static: {Colon$: function(xpos, ypos) {
        var t1 = new Q.Colon(null);
        t1.Colon$2(xpos, ypos);
        return t1;
      }}
  }
},
1],
["dart._collection.dev", "dart:_collection-dev", , H, {
  "": "",
  IterableMixinWorkaround_forEach: function(iterable, f) {
    var t1;
    for (t1 = new H.ListIterator(iterable, iterable.length, 0, null); t1.moveNext$0();)
      f.call$1(t1._dev$_current);
  },
  IterableMixinWorkaround_removeWhereList: function(list, test) {
    var retained, $length, t1, i, element;
    retained = [];
    $length = list.length;
    for (t1 = $length, i = 0; i < $length; ++i) {
      if (i >= t1)
        return H.ioore(list, i);
      element = list[i];
      if (test.call$1(element) !== true)
        retained.push(element);
      t1 = list.length;
      if ($length !== t1)
        throw H.wrapException(P.ConcurrentModificationError$(list));
    }
    t1 = retained.length;
    if (t1 === $length)
      return;
    C.JSArray_methods.set$length(list, t1);
    for (i = 0; i < retained.length; ++i)
      C.JSArray_methods.$indexSet(list, i, retained[i]);
  },
  IterableMixinWorkaround_toStringIterable: function(iterable, leftDelimiter, rightDelimiter) {
    var result, i, t1;
    for (i = 0; t1 = $.get$IterableMixinWorkaround__toStringList(), i < t1.length; ++i)
      if (t1[i] === iterable)
        return H.S(leftDelimiter) + "..." + H.S(rightDelimiter);
    result = P.StringBuffer$("");
    try {
      $.get$IterableMixinWorkaround__toStringList().push(iterable);
      result.write$1(leftDelimiter);
      result.writeAll$2(iterable, ", ");
      result.write$1(rightDelimiter);
    } finally {
      t1 = $.get$IterableMixinWorkaround__toStringList();
      if (0 >= t1.length)
        return H.ioore(t1, 0);
      t1.pop();
    }
    return result.get$_contents();
  },
  IterableMixinWorkaround_setRangeList: function(list, start, end, from, skipCount) {
    var $length;
    if (start < 0 || start > list.length)
      H.throwExpression(P.RangeError$range(start, 0, list.length));
    if (end < start || end > list.length)
      H.throwExpression(P.RangeError$range(end, start, list.length));
    $length = end - start;
    if ($length === 0)
      return;
    if (skipCount + $length > from.length)
      throw H.wrapException(P.StateError$("Not enough elements"));
    H.Lists_copy(from, skipCount, list, start, $length);
  },
  Lists_copy: function(src, srcStart, dst, dstStart, count) {
    var i, j, t1, t2;
    if (srcStart < dstStart)
      for (i = srcStart + count - 1, j = dstStart + count - 1, t1 = src.length; i >= srcStart; --i, --j) {
        if (i >= t1)
          return H.ioore(src, i);
        C.JSArray_methods.$indexSet(dst, j, src[i]);
      }
    else
      for (t1 = srcStart + count, t2 = src.length, j = dstStart, i = srcStart; i < t1; ++i, ++j) {
        if (i >= t2)
          return H.ioore(src, i);
        C.JSArray_methods.$indexSet(dst, j, src[i]);
      }
  },
  Symbol_getName: function(symbol) {
    return symbol.get$_name();
  },
  ListIterator: {
    "": "Object;_iterable,_dev$_length,_index,_dev$_current",
    get$current: function() {
      return this._dev$_current;
    },
    moveNext$0: function() {
      var t1, t2, $length, t3;
      t1 = this._iterable;
      t2 = J.getInterceptor$asx(t1);
      $length = t2.get$length(t1);
      if (this._dev$_length !== $length)
        throw H.wrapException(P.ConcurrentModificationError$(t1));
      t3 = this._index;
      if (t3 >= $length) {
        this._dev$_current = null;
        return false;
      }
      this._dev$_current = t2.elementAt$1(t1, t3);
      this._index = this._index + 1;
      return true;
    }
  },
  MappedIterable: {
    "": "IterableBase;_iterable,_f",
    get$iterator: function(_) {
      var t1 = this._iterable;
      t1 = new H.MappedIterator(null, t1.get$iterator(t1), this._f);
      t1.$builtinTypeInfo = this.$builtinTypeInfo;
      return t1;
    },
    get$length: function(_) {
      var t1 = this._iterable;
      return t1.get$length(t1);
    },
    $asIterableBase: function($S, $T) {
      return [$T];
    },
    static: {MappedIterable_MappedIterable: function(iterable, $function, $S, $T) {
        return H.setRuntimeTypeInfo(new H.EfficientLengthMappedIterable(iterable, $function), [$S, $T]);
      }}
  },
  EfficientLengthMappedIterable: {
    "": "MappedIterable;_iterable,_f",
    $asMappedIterable: null
  },
  MappedIterator: {
    "": "Iterator;_dev$_current,_iterator,_f",
    _f$1: function(arg0) {
      return this._f.call$1(arg0);
    },
    moveNext$0: function() {
      var t1 = this._iterator;
      if (t1.moveNext$0()) {
        this._dev$_current = this._f$1(t1.get$current());
        return true;
      }
      this._dev$_current = null;
      return false;
    },
    get$current: function() {
      return this._dev$_current;
    },
    $asIterator: function($S, $T) {
      return [$T];
    }
  },
  FixedLengthListMixin: {
    "": "Object;"
  }
}],
["dart._js_names", "dart:_js_names", , H, {
  "": "",
  extractKeys: function(victim) {
    var t1 = H.setRuntimeTypeInfo((function(victim, hasOwnProperty) {
  var result = [];
  for (var key in victim) {
    if (hasOwnProperty.call(victim, key)) result.push(key);
  }
  return result;
})(victim, Object.prototype.hasOwnProperty), [null]);
    t1.fixed$length = init;
    return t1;
  }
}],
["dart.async", "dart:async", , P, {
  "": "",
  _invokeErrorHandler: function(errorHandler, error, stackTrace) {
    var t1 = H.getDynamicRuntimeType();
    t1 = H.buildFunctionType(t1, [t1, t1])._isTest$1(errorHandler);
    if (t1)
      return errorHandler.call$2(error, stackTrace);
    else
      return errorHandler.call$1(error);
  },
  _registerErrorHandler: function(errorHandler, zone) {
    var t1 = H.getDynamicRuntimeType();
    t1 = H.buildFunctionType(t1, [t1, t1])._isTest$1(errorHandler);
    zone.toString;
    if (t1)
      return errorHandler;
    else
      return errorHandler;
  },
  _asyncRunCallback: [function() {
    var callback, t1, exception, milliseconds;
    for (; t1 = $.get$_asyncCallbacks(), t1._head !== t1._tail;) {
      callback = t1.removeFirst$0();
      try {
        callback.call$0();
      } catch (exception) {
        H.unwrapException(exception);
        milliseconds = C.JSInt_methods._tdivFast$1(C.Duration_0._duration, 1000);
        H.TimerImpl$(milliseconds < 0 ? 0 : milliseconds, P._asyncRunCallback$closure());
        throw exception;
      }

    }
    $._callbacksAreEnqueued = false;
  }, "call$0", "_asyncRunCallback$closure", 0, 0, 0],
  _scheduleAsyncCallback: function(callback) {
    $.get$_asyncCallbacks()._add$1(callback);
    if (!$._callbacksAreEnqueued) {
      P._createTimer(C.Duration_0, P._asyncRunCallback$closure());
      $._callbacksAreEnqueued = true;
    }
  },
  scheduleMicrotask: function(callback) {
    var t1, f, milliseconds;
    t1 = $.Zone__current;
    if (t1 === C.C__RootZone) {
      t1.toString;
      f = C.C__RootZone !== t1 ? t1.bindCallback$1(callback) : callback;
      $.get$_asyncCallbacks()._add$1(f);
      if (!$._callbacksAreEnqueued) {
        milliseconds = C.JSInt_methods._tdivFast$1(C.Duration_0._duration, 1000);
        H.TimerImpl$(milliseconds < 0 ? 0 : milliseconds, P._asyncRunCallback$closure());
        $._callbacksAreEnqueued = true;
      }
      return;
    }
    f = t1.bindCallback$2$runGuarded(callback, true);
    if (C.C__RootZone !== t1)
      f = t1.bindCallback$1(f);
    $.get$_asyncCallbacks()._add$1(f);
    if (!$._callbacksAreEnqueued) {
      milliseconds = C.JSInt_methods._tdivFast$1(C.Duration_0._duration, 1000);
      H.TimerImpl$(milliseconds < 0 ? 0 : milliseconds, P._asyncRunCallback$closure());
      $._callbacksAreEnqueued = true;
    }
  },
  StreamController_StreamController: function(onCancel, onListen, onPause, onResume, sync, $T) {
    return sync ? H.setRuntimeTypeInfo(new P._SyncStreamController(onListen, onPause, onResume, onCancel, null, 0, null), [$T]) : H.setRuntimeTypeInfo(new P._AsyncStreamController(onListen, onPause, onResume, onCancel, null, 0, null), [$T]);
  },
  _runGuarded: function(notificationHandler) {
    var result, e, s, t1, t2, exception;
    if (notificationHandler == null)
      return;
    try {
      result = notificationHandler.call$0();
      t1 = result;
      t2 = J.getInterceptor(t1);
      if (typeof t1 === "object" && t1 !== null && !!t2.$isFuture)
        return result;
      return;
    } catch (exception) {
      t1 = H.unwrapException(exception);
      e = t1;
      s = new H._StackTrace(exception, null);
      t1 = $.Zone__current;
      t1.toString;
      P._rootHandleUncaughtError(t1, null, t1, e, s);
    }

  },
  _nullDataHandler: [function(value) {
  }, "call$1", "_nullDataHandler$closure", 2, 0, 1],
  _nullErrorHandler: [function(error, stackTrace) {
    var t1 = $.Zone__current;
    t1.toString;
    P._rootHandleUncaughtError(t1, null, t1, error, stackTrace);
  }, function(error) {
    return P._nullErrorHandler(error, null);
  }, null, "call$2", "call$1", "_nullErrorHandler$closure", 2, 2, 2, 3],
  _nullDoneHandler: [function() {
    return;
  }, "call$0", "_nullDoneHandler$closure", 0, 0, 0],
  _runUserCode: function(userCode, onSuccess, onError) {
    var e, s, exception, t1;
    try {
      onSuccess.call$1(userCode.call$0());
    } catch (exception) {
      t1 = H.unwrapException(exception);
      e = t1;
      s = new H._StackTrace(exception, null);
      onError.call$2(e, s);
    }

  },
  _cancelAndError: function(subscription, future, error, stackTrace) {
    var cancelFuture, t1;
    cancelFuture = subscription.cancel$0();
    t1 = J.getInterceptor(cancelFuture);
    if (typeof cancelFuture === "object" && cancelFuture !== null && !!t1.$isFuture)
      cancelFuture.whenComplete$1(new P._cancelAndError_closure(future, error, stackTrace));
    else
      future._completeError$2(error, stackTrace);
  },
  _cancelAndErrorClosure: function(subscription, future) {
    return new P._cancelAndErrorClosure_closure(subscription, future);
  },
  Timer_Timer: function(duration, callback) {
    var t1 = $.Zone__current;
    if (t1 === C.C__RootZone) {
      t1.toString;
      return P._rootCreateTimer(t1, null, t1, duration, callback);
    }
    return P._rootCreateTimer(t1, null, t1, duration, t1.bindCallback$2$runGuarded(callback, true));
  },
  _createTimer: function(duration, callback) {
    var milliseconds = C.JSInt_methods._tdivFast$1(duration._duration, 1000);
    return H.TimerImpl$(milliseconds < 0 ? 0 : milliseconds, callback);
  },
  _rootHandleUncaughtError: function($self, $parent, zone, error, stackTrace) {
    P._rootRun($self, null, $self, new P._rootHandleUncaughtError_closure(error, stackTrace));
  },
  _rootRun: function($self, $parent, zone, f) {
    var old, t1, t2;
    t1 = $.Zone__current;
    t2 = zone;
    if (t1 == null ? t2 == null : t1 === t2)
      return f.call$0();
    old = t1;
    try {
      $.Zone__current = zone;
      t1 = f.call$0();
      return t1;
    } finally {
      $.Zone__current = old;
    }
  },
  _rootRunUnary: function($self, $parent, zone, f, arg) {
    var old, t1, t2;
    t1 = $.Zone__current;
    t2 = zone;
    if (t1 == null ? t2 == null : t1 === t2)
      return f.call$1(arg);
    old = t1;
    try {
      $.Zone__current = zone;
      t1 = f.call$1(arg);
      return t1;
    } finally {
      $.Zone__current = old;
    }
  },
  _rootScheduleMicrotask: function($self, $parent, zone, f) {
    P._scheduleAsyncCallback(C.C__RootZone !== zone ? zone.bindCallback$1(f) : f);
  },
  _rootCreateTimer: function($self, $parent, zone, duration, callback) {
    return P._createTimer(duration, C.C__RootZone !== zone ? zone.bindCallback$1(callback) : callback);
  },
  _AsyncError: {
    "": "Object;error>,stackTrace<",
    $isError: true
  },
  Future: {
    "": "Object;",
    $isFuture: true
  },
  _Future: {
    "": "Object;_state,_zone<,_resultOrListeners,_nextListener<,_onValueCallback,_errorTestCallback,_onErrorCallback,_whenCompleteActionCallback",
    get$_isComplete: function() {
      return this._state >= 4;
    },
    get$_hasError: function() {
      return this._state === 8;
    },
    set$_isChained: function(value) {
      if (value)
        this._state = 2;
      else
        this._state = 0;
    },
    get$_onValue: function() {
      return this._state === 2 ? null : this._onValueCallback;
    },
    _onValue$1: function(arg0) {
      return this.get$_onValue().call$1(arg0);
    },
    get$_whenCompleteAction: function() {
      return this._state === 2 ? null : this._whenCompleteActionCallback;
    },
    _whenCompleteAction$0: function() {
      return this.get$_whenCompleteAction().call$0();
    },
    then$2$onError: function(f, onError) {
      var t1, result;
      t1 = $.Zone__current;
      t1.toString;
      result = H.setRuntimeTypeInfo(new P._Future(0, t1, null, null, f, null, P._registerErrorHandler(onError, t1), null), [null]);
      this._addListener$1(result);
      return result;
    },
    whenComplete$1: function(action) {
      var t1, result;
      t1 = $.Zone__current;
      t1.toString;
      result = new P._Future(0, t1, null, null, null, null, null, action);
      result.$builtinTypeInfo = this.$builtinTypeInfo;
      this._addListener$1(result);
      return result;
    },
    get$_async$_value: function() {
      return this._resultOrListeners;
    },
    get$_error: function() {
      return this._resultOrListeners;
    },
    _setValue$1: function(value) {
      this._state = 4;
      this._resultOrListeners = value;
    },
    _setError$2: function(error, stackTrace) {
      this._state = 8;
      this._resultOrListeners = new P._AsyncError(error, stackTrace);
    },
    _addListener$1: function(listener) {
      var t1;
      if (this._state >= 4) {
        t1 = this._zone;
        t1.toString;
        P._rootScheduleMicrotask(t1, null, t1, new P._Future__addListener_closure(this, listener));
      } else {
        listener._nextListener = this._resultOrListeners;
        this._resultOrListeners = listener;
      }
    },
    _removeListeners$0: function() {
      var current, prev, next;
      current = this._resultOrListeners;
      this._resultOrListeners = null;
      for (prev = null; current != null; prev = current, current = next) {
        next = current.get$_nextListener();
        current._nextListener = prev;
      }
      return prev;
    },
    _complete$1: function(value) {
      var t1, listeners;
      t1 = J.getInterceptor(value);
      if (typeof value === "object" && value !== null && !!t1.$isFuture) {
        P._Future__chainFutures(value, this);
        return;
      }
      listeners = this._removeListeners$0();
      this._setValue$1(value);
      P._Future__propagateToListeners(this, listeners);
    },
    _completeError$2: [function(error, stackTrace) {
      var listeners = this._removeListeners$0();
      this._setError$2(error, stackTrace);
      P._Future__propagateToListeners(this, listeners);
    }, function(error) {
      return this._completeError$2(error, null);
    }, "_completeError$1", "call$2", "call$1", "get$_completeError", 2, 2, 2, 3],
    _asyncComplete$1: function(value) {
      var t1;
      if (this._state !== 0)
        H.throwExpression(new P.StateError("Future already completed"));
      this._state = 1;
      t1 = this._zone;
      t1.toString;
      P._rootScheduleMicrotask(t1, null, t1, new P._Future__asyncComplete_closure(this, value));
    },
    $is_Future: true,
    $isFuture: true,
    static: {"": "_Future__INCOMPLETE,_Future__PENDING_COMPLETE,_Future__CHAINED,_Future__VALUE,_Future__ERROR", _Future$: function($T) {
        return H.setRuntimeTypeInfo(new P._Future(0, $.Zone__current, null, null, null, null, null, null), [$T]);
      }, _Future__chainFutures: function(source, target) {
        var t1;
        target._state = 2;
        t1 = J.getInterceptor(source);
        if (typeof source === "object" && source !== null && !!t1.$is_Future)
          if (source._state >= 4)
            P._Future__propagateToListeners(source, target);
          else
            source._addListener$1(target);
        else
          source.then$2$onError(new P._Future__chainFutures_closure(target), new P._Future__chainFutures_closure0(target));
      }, _Future__propagateMultipleListeners: function(source, listeners) {
        var listeners0;
        do {
          listeners0 = listeners.get$_nextListener();
          listeners._nextListener = null;
          P._Future__propagateToListeners(source, listeners);
          if (listeners0 != null) {
            listeners = listeners0;
            continue;
          } else
            break;
        } while (true);
      }, _Future__propagateToListeners: function(source, listeners) {
        var t1, t2, t3, hasError, asyncError, t4, t5, chainSource, listeners0;
        t1 = {};
        t1.source_4 = source;
        for (t2 = source; true;) {
          t3 = {};
          if (!t2.get$_isComplete())
            return;
          hasError = t1.source_4.get$_hasError();
          if (hasError && listeners == null) {
            t2 = t1.source_4;
            asyncError = t2.get$_error();
            t2 = t2._zone;
            t3 = J.get$error$x(asyncError);
            t4 = asyncError.get$stackTrace();
            t2.toString;
            P._rootHandleUncaughtError(t2, null, t2, t3, t4);
            return;
          }
          if (listeners == null)
            return;
          if (listeners._nextListener != null) {
            P._Future__propagateMultipleListeners(t1.source_4, listeners);
            return;
          }
          if (hasError) {
            t2 = t1.source_4.get$_zone();
            t4 = listeners._zone;
            t2.toString;
            t4.toString;
            t2 = t4 == null ? t2 != null : t4 !== t2;
          } else
            t2 = false;
          if (t2) {
            t2 = t1.source_4;
            asyncError = t2.get$_error();
            t2 = t2._zone;
            t3 = J.get$error$x(asyncError);
            t4 = asyncError.get$stackTrace();
            t2.toString;
            P._rootHandleUncaughtError(t2, null, t2, t3, t4);
            return;
          }
          t2 = $.Zone__current;
          t4 = listeners._zone;
          if (t2 == null ? t4 != null : t2 !== t4) {
            t4.toString;
            P._rootRun(t4, null, t4, new P._Future__propagateToListeners_closure(t1, listeners));
            return;
          }
          t3.listenerHasValue_1 = null;
          t3.listenerValueOrError_2 = null;
          t3.isPropagationAborted_3 = false;
          t4.toString;
          P._rootRun(t4, null, t4, new P._Future__propagateToListeners_closure0(t1, t3, hasError, listeners));
          if (t3.isPropagationAborted_3)
            return;
          t2 = t3.listenerHasValue_1 === true;
          if (t2) {
            t4 = t3.listenerValueOrError_2;
            t5 = J.getInterceptor(t4);
            t5 = typeof t4 === "object" && t4 !== null && !!t5.$isFuture;
            t4 = t5;
          } else
            t4 = false;
          if (t4) {
            chainSource = t3.listenerValueOrError_2;
            t2 = J.getInterceptor(chainSource);
            if (typeof chainSource === "object" && chainSource !== null && !!t2.$is_Future && chainSource._state >= 4) {
              listeners._state = 2;
              t1.source_4 = chainSource;
              t2 = chainSource;
              continue;
            }
            P._Future__chainFutures(chainSource, listeners);
            return;
          }
          if (t2) {
            listeners0 = listeners._removeListeners$0();
            t2 = t3.listenerValueOrError_2;
            listeners._state = 4;
            listeners._resultOrListeners = t2;
          } else {
            listeners0 = listeners._removeListeners$0();
            asyncError = t3.listenerValueOrError_2;
            t2 = J.get$error$x(asyncError);
            t3 = asyncError.get$stackTrace();
            listeners._state = 8;
            listeners._resultOrListeners = new P._AsyncError(t2, t3);
          }
          t1.source_4 = listeners;
          t2 = listeners;
          listeners = listeners0;
        }
      }}
  },
  _Future__addListener_closure: {
    "": "Closure:8;this_0,listener_1",
    call$0: function() {
      P._Future__propagateToListeners(this.this_0, this.listener_1);
    }
  },
  _Future__chainFutures_closure: {
    "": "Closure:10;target_0",
    call$1: function(value) {
      this.target_0._complete$1(value);
    }
  },
  _Future__chainFutures_closure0: {
    "": "Closure:14;target_1",
    call$2: function(error, stackTrace) {
      this.target_1._completeError$2(error, stackTrace);
    },
    call$1: function(error) {
      return this.call$2(error, null);
    }
  },
  _Future__asyncComplete_closure: {
    "": "Closure:8;this_0,value_1",
    call$0: function() {
      this.this_0._complete$1(this.value_1);
    }
  },
  _Future__propagateToListeners_closure: {
    "": "Closure:8;box_2,listener_3",
    call$0: function() {
      P._Future__propagateToListeners(this.box_2.source_4, this.listener_3);
    }
  },
  _Future__propagateToListeners_closure0: {
    "": "Closure:8;box_2,box_1,hasError_4,listener_5",
    call$0: function() {
      var t1, value, asyncError, test, matchesTest, errorCallback, e, s, t2, t3, t4, t5, completeResult, exception;
      t1 = {};
      try {
        t2 = this.box_2;
        if (!this.hasError_4) {
          value = t2.source_4.get$_async$_value();
          t3 = this.listener_5;
          t4 = t3._state === 2 ? null : t3._onValueCallback;
          t5 = this.box_1;
          if (t4 != null) {
            t5.listenerValueOrError_2 = t3._onValue$1(value);
            t5.listenerHasValue_1 = true;
          } else {
            t5.listenerValueOrError_2 = value;
            t5.listenerHasValue_1 = true;
          }
          t4 = t5;
        } else {
          asyncError = t2.source_4.get$_error();
          t3 = this.listener_5;
          test = t3._state === 2 ? null : t3._errorTestCallback;
          matchesTest = true;
          if (test != null)
            matchesTest = test.call$1(J.get$error$x(asyncError));
          if (matchesTest === true)
            t4 = (t3._state === 2 ? null : t3._onErrorCallback) != null;
          else
            t4 = false;
          if (t4) {
            errorCallback = t3._state === 2 ? null : t3._onErrorCallback;
            t4 = this.box_1;
            t4.listenerValueOrError_2 = P._invokeErrorHandler(errorCallback, J.get$error$x(asyncError), asyncError.get$stackTrace());
            t4.listenerHasValue_1 = true;
          } else {
            t4 = this.box_1;
            t4.listenerValueOrError_2 = asyncError;
            t4.listenerHasValue_1 = false;
          }
        }
        if ((t3._state === 2 ? null : t3._whenCompleteActionCallback) != null) {
          completeResult = t3._whenCompleteAction$0();
          t1.completeResult_0 = completeResult;
          t5 = J.getInterceptor(completeResult);
          if (typeof completeResult === "object" && completeResult !== null && !!t5.$isFuture) {
            t3.set$_isChained(true);
            t1.completeResult_0.then$2$onError(new P._Future__propagateToListeners__closure(t2, t3), new P._Future__propagateToListeners__closure0(t1, t3));
            t4.isPropagationAborted_3 = true;
          }
        }
      } catch (exception) {
        t1 = H.unwrapException(exception);
        e = t1;
        s = new H._StackTrace(exception, null);
        if (this.hasError_4) {
          t1 = J.get$error$x(this.box_2.source_4.get$_error());
          t2 = e;
          t2 = t1 == null ? t2 == null : t1 === t2;
          t1 = t2;
        } else
          t1 = false;
        t2 = this.box_1;
        if (t1)
          t2.listenerValueOrError_2 = this.box_2.source_4.get$_error();
        else
          t2.listenerValueOrError_2 = new P._AsyncError(e, s);
        t2.listenerHasValue_1 = false;
      }

    }
  },
  _Future__propagateToListeners__closure: {
    "": "Closure:10;box_2,listener_6",
    call$1: function(ignored) {
      P._Future__propagateToListeners(this.box_2.source_4, this.listener_6);
    }
  },
  _Future__propagateToListeners__closure0: {
    "": "Closure:14;box_0,listener_7",
    call$2: function(error, stackTrace) {
      var t1, t2, t3, completeResult;
      t1 = this.box_0;
      t2 = t1.completeResult_0;
      t3 = J.getInterceptor(t2);
      if (typeof t2 !== "object" || t2 === null || !t3.$is_Future) {
        completeResult = P._Future$(null);
        t1.completeResult_0 = completeResult;
        completeResult._setError$2(error, stackTrace);
      }
      P._Future__propagateToListeners(t1.completeResult_0, this.listener_7);
    },
    call$1: function(error) {
      return this.call$2(error, null);
    }
  },
  Stream: {
    "": "Object;",
    forEach$1: function(_, action) {
      var t1, future;
      t1 = {};
      future = P._Future$(null);
      t1.subscription_0 = null;
      t1.subscription_0 = this.listen$4$cancelOnError$onDone$onError(new P.Stream_forEach_closure(t1, this, action, future), true, new P.Stream_forEach_closure0(future), future.get$_completeError());
      return future;
    },
    get$length: function(_) {
      var t1, future;
      t1 = {};
      future = P._Future$(J.JSInt);
      t1.count_0 = 0;
      this.listen$4$cancelOnError$onDone$onError(new P.Stream_length_closure(t1), true, new P.Stream_length_closure0(t1, future), future.get$_completeError());
      return future;
    }
  },
  Stream_forEach_closure: {
    "": "Closure;box_0,this_1,action_2,future_3",
    call$1: function(element) {
      P._runUserCode(new P.Stream_forEach__closure(this.action_2, element), new P.Stream_forEach__closure0(), P._cancelAndErrorClosure(this.box_0.subscription_0, this.future_3));
    },
    $signature: function() {
      return H.computeSignature(function(T) {
        return {func: "dynamic__T", args: [T]};
      }, this.this_1, "Stream");
    }
  },
  Stream_forEach__closure: {
    "": "Closure:8;action_4,element_5",
    call$0: function() {
      return this.action_4.call$1(this.element_5);
    }
  },
  Stream_forEach__closure0: {
    "": "Closure:10;",
    call$1: function(_) {
    }
  },
  Stream_forEach_closure0: {
    "": "Closure:8;future_6",
    call$0: function() {
      this.future_6._complete$1(null);
    }
  },
  Stream_length_closure: {
    "": "Closure:10;box_0",
    call$1: function(_) {
      var t1 = this.box_0;
      t1.count_0 = t1.count_0 + 1;
    }
  },
  Stream_length_closure0: {
    "": "Closure:8;box_0,future_1",
    call$0: function() {
      this.future_1._complete$1(this.box_0.count_0);
    }
  },
  StreamSubscription: {
    "": "Object;"
  },
  _StreamController: {
    "": "Object;",
    get$_pendingEvents: function() {
      if ((this._state & 8) === 0)
        return this._varData;
      return this._varData.get$varData();
    },
    _ensurePendingEvents$0: function() {
      if ((this._state & 8) === 0) {
        var t1 = this._varData;
        if (t1 == null) {
          t1 = new P._StreamImplEvents(null, null, 0);
          this._varData = t1;
        }
        return t1;
      }
      t1 = this._varData.get$varData();
      return t1;
    },
    get$_subscription: function() {
      if ((this._state & 8) !== 0)
        return this._varData.get$varData();
      return this._varData;
    },
    _badEventState$0: function() {
      if ((this._state & 4) !== 0)
        return new P.StateError("Cannot add event after closing");
      return new P.StateError("Cannot add event while adding a stream");
    },
    add$1: [function(_, value) {
      var t1 = this._state;
      if (t1 >= 4)
        throw H.wrapException(this._badEventState$0());
      if ((t1 & 1) !== 0)
        this._sendData$1(value);
      else if ((t1 & 3) === 0) {
        t1 = this._ensurePendingEvents$0();
        t1.add$1(t1, new P._DelayedData(value, null));
      }
    }, "call$1", "get$add", 2, 0, function() {
      return H.computeSignature(function(T) {
        return {func: "void__T", void: true, args: [T]};
      }, this.$receiver, "_StreamController");
    }],
    close$0: function(_) {
      var t1, t2;
      t1 = this._state;
      if ((t1 & 4) !== 0)
        return this._doneFuture;
      if (t1 >= 4)
        throw H.wrapException(this._badEventState$0());
      t1 |= 4;
      this._state = t1;
      if (this._doneFuture == null) {
        t2 = P._Future$(null);
        this._doneFuture = t2;
        if ((t1 & 2) !== 0)
          t2._complete$1(null);
      }
      t1 = this._state;
      if ((t1 & 1) !== 0)
        this._sendDone$0();
      else if ((t1 & 3) === 0) {
        t1 = this._ensurePendingEvents$0();
        t1.add$1(t1, C.C__DelayedDone);
      }
      return this._doneFuture;
    },
    _subscribe$1: function(cancelOnError) {
      var t1, t2, subscription, pendingEvents, addState;
      if ((this._state & 3) !== 0)
        throw H.wrapException(new P.StateError("Stream has already been listened to."));
      t1 = $.Zone__current;
      t2 = cancelOnError ? 1 : 0;
      subscription = H.setRuntimeTypeInfo(new P._ControllerSubscription(this, null, null, null, t1, t2, null, null), [null]);
      pendingEvents = this.get$_pendingEvents();
      t2 = this._state | 1;
      this._state = t2;
      if ((t2 & 8) !== 0) {
        addState = this._varData;
        addState.set$varData(subscription);
        addState.resume$0();
      } else
        this._varData = subscription;
      subscription._setPendingEvents$1(pendingEvents);
      subscription._guardCallback$1(new P._StreamController__subscribe_closure(this));
      return subscription;
    },
    _recordCancel$1: function(subscription) {
      var t1, future;
      if ((this._state & 8) !== 0)
        this._varData.cancel$0();
      this._varData = null;
      this._state = this._state & 4294967286 | 2;
      t1 = new P._StreamController__recordCancel_complete(this);
      future = P._runGuarded(this.get$_onCancel());
      if (future != null)
        future = future.whenComplete$1(t1);
      else
        t1.call$0();
      return future;
    }
  },
  _StreamController__subscribe_closure: {
    "": "Closure:8;this_0",
    call$0: function() {
      P._runGuarded(this.this_0.get$_onListen());
    }
  },
  _StreamController__recordCancel_complete: {
    "": "Closure:0;this_0",
    call$0: function() {
      var t1 = this.this_0._doneFuture;
      if (t1 != null && t1._state === 0)
        t1._asyncComplete$1(null);
    }
  },
  _SyncStreamControllerDispatch: {
    "": "Object;",
    _sendData$1: function(data) {
      this.get$_subscription()._async$_add$1(data);
    },
    _sendDone$0: function() {
      this.get$_subscription()._close$0();
    }
  },
  _AsyncStreamControllerDispatch: {
    "": "Object;",
    _sendData$1: function(data) {
      this.get$_subscription()._addPending$1(new P._DelayedData(data, null));
    },
    _sendDone$0: function() {
      this.get$_subscription()._addPending$1(C.C__DelayedDone);
    }
  },
  _AsyncStreamController: {
    "": "_StreamController__AsyncStreamControllerDispatch;_onListen<,_onPause<,_onResume<,_onCancel<,_varData,_state,_doneFuture",
    $as_StreamController__AsyncStreamControllerDispatch: null
  },
  _StreamController__AsyncStreamControllerDispatch: {
    "": "_StreamController+_AsyncStreamControllerDispatch;",
    $as_StreamController: null
  },
  _SyncStreamController: {
    "": "_StreamController__SyncStreamControllerDispatch;_onListen<,_onPause<,_onResume<,_onCancel<,_varData,_state,_doneFuture",
    $as_StreamController__SyncStreamControllerDispatch: null
  },
  _StreamController__SyncStreamControllerDispatch: {
    "": "_StreamController+_SyncStreamControllerDispatch;",
    $as_StreamController: null
  },
  _ControllerStream: {
    "": "_StreamImpl;_async$_controller",
    _createSubscription$1: function(cancelOnError) {
      return this._async$_controller._subscribe$1(cancelOnError);
    },
    get$hashCode: function(_) {
      return (H.Primitives_objectHashCode(this._async$_controller) ^ 892482866) >>> 0;
    },
    $eq: function(_, other) {
      var t1;
      if (other == null)
        return false;
      if (this === other)
        return true;
      t1 = J.getInterceptor(other);
      if (typeof other !== "object" || other === null || !t1.$is_ControllerStream)
        return false;
      return other._async$_controller === this._async$_controller;
    },
    $is_ControllerStream: true,
    $as_StreamImpl: null
  },
  _ControllerSubscription: {
    "": "_BufferingStreamSubscription;_async$_controller,_onData,_onError,_onDone,_zone,_state,_cancelFuture,_pending",
    _onCancel$0: function() {
      return this._async$_controller._recordCancel$1(this);
    },
    _onPause$0: [function() {
      var t1, addState;
      t1 = this._async$_controller;
      if ((t1._state & 8) !== 0) {
        addState = t1._varData;
        addState.pause$0(addState);
      }
      P._runGuarded(t1.get$_onPause());
    }, "call$0", "get$_onPause", 0, 0, 0],
    _onResume$0: [function() {
      var t1 = this._async$_controller;
      if ((t1._state & 8) !== 0)
        t1._varData.resume$0();
      P._runGuarded(t1.get$_onResume());
    }, "call$0", "get$_onResume", 0, 0, 0],
    $as_BufferingStreamSubscription: null
  },
  _EventSink: {
    "": "Object;"
  },
  _BufferingStreamSubscription: {
    "": "Object;_onData,_onError,_onDone,_zone<,_state,_cancelFuture,_pending",
    _setPendingEvents$1: function(pendingEvents) {
      if (pendingEvents == null)
        return;
      this._pending = pendingEvents;
      if (!pendingEvents.get$isEmpty(pendingEvents)) {
        this._state = (this._state | 64) >>> 0;
        pendingEvents.schedule$1(this);
      }
    },
    onData$1: function(handleData) {
      this._zone.toString;
      this._onData = handleData;
    },
    onError$1: function(_, handleError) {
      this._onError = P._registerErrorHandler(handleError, this._zone);
    },
    onDone$1: function(handleDone) {
      this._zone.toString;
      this._onDone = handleDone;
    },
    pause$1: function(_, resumeSignal) {
      var t1 = this._state;
      if ((t1 & 8) !== 0)
        return;
      this._state = (t1 + 128 | 4) >>> 0;
      if (t1 < 128 && this._pending != null)
        this._pending.cancelSchedule$0();
      if ((t1 & 4) === 0 && (this._state & 32) === 0)
        this._guardCallback$1(this.get$_onPause());
    },
    pause$0: function($receiver) {
      return this.pause$1($receiver, null);
    },
    resume$0: function() {
      var t1, t2;
      t1 = this._state;
      if ((t1 & 8) !== 0)
        return;
      if (t1 >= 128) {
        t1 -= 128;
        this._state = t1;
        if (t1 < 128) {
          if ((t1 & 64) !== 0) {
            t2 = this._pending;
            t2 = !t2.get$isEmpty(t2);
          } else
            t2 = false;
          if (t2)
            this._pending.schedule$1(this);
          else {
            t1 = (t1 & 4294967291) >>> 0;
            this._state = t1;
            if ((t1 & 32) === 0)
              this._guardCallback$1(this.get$_onResume());
          }
        }
      }
    },
    cancel$0: function() {
      var t1 = (this._state & 4294967279) >>> 0;
      this._state = t1;
      if ((t1 & 8) !== 0)
        return this._cancelFuture;
      this._cancel$0();
      return this._cancelFuture;
    },
    _cancel$0: function() {
      var t1 = (this._state | 8) >>> 0;
      this._state = t1;
      if ((t1 & 64) !== 0)
        this._pending.cancelSchedule$0();
      if ((this._state & 32) === 0)
        this._pending = null;
      this._cancelFuture = this._onCancel$0();
    },
    _async$_add$1: function(data) {
      var t1 = this._state;
      if ((t1 & 8) !== 0)
        return;
      if (t1 < 32)
        this._sendData$1(data);
      else
        this._addPending$1(new P._DelayedData(data, null));
    },
    _close$0: function() {
      var t1 = this._state;
      if ((t1 & 8) !== 0)
        return;
      t1 = (t1 | 2) >>> 0;
      this._state = t1;
      if (t1 < 32)
        this._sendDone$0();
      else
        this._addPending$1(C.C__DelayedDone);
    },
    _onPause$0: [function() {
    }, "call$0", "get$_onPause", 0, 0, 0],
    _onResume$0: [function() {
    }, "call$0", "get$_onResume", 0, 0, 0],
    _onCancel$0: function() {
    },
    _addPending$1: function($event) {
      var pending, t1;
      pending = this._pending;
      if (pending == null) {
        pending = new P._StreamImplEvents(null, null, 0);
        this._pending = pending;
      }
      pending.add$1(pending, $event);
      t1 = this._state;
      if ((t1 & 64) === 0) {
        t1 = (t1 | 64) >>> 0;
        this._state = t1;
        if (t1 < 128)
          this._pending.schedule$1(this);
      }
    },
    _sendData$1: function(data) {
      var t1 = this._state;
      this._state = (t1 | 32) >>> 0;
      this._zone.runUnaryGuarded$2(this._onData, data);
      this._state = (this._state & 4294967263) >>> 0;
      this._checkState$1((t1 & 4) !== 0);
    },
    _sendDone$0: function() {
      var t1, t2, t3;
      t1 = new P._BufferingStreamSubscription__sendDone_sendDone(this);
      this._cancel$0();
      this._state = (this._state | 16) >>> 0;
      t2 = this._cancelFuture;
      t3 = J.getInterceptor(t2);
      if (typeof t2 === "object" && t2 !== null && !!t3.$isFuture)
        t2.whenComplete$1(t1);
      else
        t1.call$0();
    },
    _guardCallback$1: function(callback) {
      var t1 = this._state;
      this._state = (t1 | 32) >>> 0;
      callback.call$0();
      this._state = (this._state & 4294967263) >>> 0;
      this._checkState$1((t1 & 4) !== 0);
    },
    _checkState$1: function(wasInputPaused) {
      var t1, t2, isInputPaused;
      t1 = this._state;
      if ((t1 & 64) !== 0) {
        t2 = this._pending;
        t2 = t2.get$isEmpty(t2);
      } else
        t2 = false;
      if (t2) {
        t1 = (t1 & 4294967231) >>> 0;
        this._state = t1;
        if ((t1 & 4) !== 0)
          if (t1 < 128) {
            t2 = this._pending;
            t2 = t2 == null || t2.get$isEmpty(t2);
          } else
            t2 = false;
        else
          t2 = false;
        if (t2) {
          t1 = (t1 & 4294967291) >>> 0;
          this._state = t1;
        }
      }
      for (; true; wasInputPaused = isInputPaused) {
        if ((t1 & 8) !== 0) {
          this._pending = null;
          return;
        }
        isInputPaused = (t1 & 4) !== 0;
        if (wasInputPaused === isInputPaused)
          break;
        this._state = (t1 ^ 32) >>> 0;
        if (isInputPaused)
          this._onPause$0();
        else
          this._onResume$0();
        t1 = (this._state & 4294967263) >>> 0;
        this._state = t1;
      }
      if ((t1 & 64) !== 0 && t1 < 128)
        this._pending.schedule$1(this);
    },
    static: {"": "_BufferingStreamSubscription__STATE_CANCEL_ON_ERROR,_BufferingStreamSubscription__STATE_CLOSED,_BufferingStreamSubscription__STATE_INPUT_PAUSED,_BufferingStreamSubscription__STATE_CANCELED,_BufferingStreamSubscription__STATE_WAIT_FOR_CANCEL,_BufferingStreamSubscription__STATE_IN_CALLBACK,_BufferingStreamSubscription__STATE_HAS_PENDING,_BufferingStreamSubscription__STATE_PAUSE_COUNT,_BufferingStreamSubscription__STATE_PAUSE_COUNT_SHIFT"}
  },
  _BufferingStreamSubscription__sendDone_sendDone: {
    "": "Closure:0;this_0",
    call$0: function() {
      var t1, t2;
      t1 = this.this_0;
      t2 = t1._state;
      if ((t2 & 16) === 0)
        return;
      t1._state = (t2 | 42) >>> 0;
      t1._zone.runGuarded$1(t1._onDone);
      t1._state = (t1._state & 4294967263) >>> 0;
    }
  },
  _StreamImpl: {
    "": "Stream;",
    listen$4$cancelOnError$onDone$onError: function(onData, cancelOnError, onDone, onError) {
      var subscription = this._createSubscription$1(true === cancelOnError);
      subscription.onData$1(onData);
      subscription.onError$1(subscription, onError);
      subscription.onDone$1(onDone);
      return subscription;
    },
    _createSubscription$1: function(cancelOnError) {
      var t1, t2;
      t1 = $.Zone__current;
      t2 = cancelOnError ? 1 : 0;
      return new P._BufferingStreamSubscription(null, null, null, t1, t2, null, null);
    },
    $asStream: null
  },
  _DelayedEvent: {
    "": "Object;next@"
  },
  _DelayedData: {
    "": "_DelayedEvent;value,next",
    perform$1: function(dispatch) {
      dispatch._sendData$1(this.value);
    }
  },
  _DelayedDone: {
    "": "Object;",
    perform$1: function(dispatch) {
      dispatch._sendDone$0();
    },
    get$next: function() {
      return;
    },
    set$next: function(_) {
      throw H.wrapException(P.StateError$("No events after a done."));
    }
  },
  _PendingEvents: {
    "": "Object;",
    schedule$1: function(dispatch) {
      var t1 = this._state;
      if (t1 === 1)
        return;
      if (t1 >= 1) {
        this._state = 1;
        return;
      }
      P.scheduleMicrotask(new P._PendingEvents_schedule_closure(this, dispatch));
      this._state = 1;
    },
    cancelSchedule$0: function() {
      if (this._state === 1)
        this._state = 3;
    }
  },
  _PendingEvents_schedule_closure: {
    "": "Closure:8;this_0,dispatch_1",
    call$0: function() {
      var t1, oldState;
      t1 = this.this_0;
      oldState = t1._state;
      t1._state = 0;
      if (oldState === 3)
        return;
      t1.handleNext$1(this.dispatch_1);
    }
  },
  _StreamImplEvents: {
    "": "_PendingEvents;firstPendingEvent,lastPendingEvent,_state",
    get$isEmpty: function(_) {
      return this.lastPendingEvent == null;
    },
    add$1: function(_, $event) {
      var t1 = this.lastPendingEvent;
      if (t1 == null) {
        this.lastPendingEvent = $event;
        this.firstPendingEvent = $event;
      } else {
        t1.set$next($event);
        this.lastPendingEvent = $event;
      }
    },
    handleNext$1: function(dispatch) {
      var $event, t1;
      $event = this.firstPendingEvent;
      t1 = $event.get$next();
      this.firstPendingEvent = t1;
      if (t1 == null)
        this.lastPendingEvent = null;
      $event.perform$1(dispatch);
    }
  },
  _cancelAndError_closure: {
    "": "Closure:8;future_0,error_1,stackTrace_2",
    call$0: function() {
      return this.future_0._completeError$2(this.error_1, this.stackTrace_2);
    }
  },
  _cancelAndErrorClosure_closure: {
    "": "Closure:15;subscription_0,future_1",
    call$2: function(error, stackTrace) {
      return P._cancelAndError(this.subscription_0, this.future_1, error, stackTrace);
    }
  },
  _BaseZone: {
    "": "Object;",
    runGuarded$1: function(f) {
      var e, s, t1, exception;
      try {
        t1 = this.run$1(f);
        return t1;
      } catch (exception) {
        t1 = H.unwrapException(exception);
        e = t1;
        s = new H._StackTrace(exception, null);
        return this.handleUncaughtError$2(e, s);
      }

    },
    runUnaryGuarded$2: function(f, arg) {
      var e, s, t1, exception;
      try {
        t1 = this.runUnary$2(f, arg);
        return t1;
      } catch (exception) {
        t1 = H.unwrapException(exception);
        e = t1;
        s = new H._StackTrace(exception, null);
        return this.handleUncaughtError$2(e, s);
      }

    },
    bindCallback$2$runGuarded: function(f, runGuarded) {
      var registered = this.registerCallback$1(f);
      if (runGuarded)
        return new P._BaseZone_bindCallback_closure(this, registered);
      else
        return new P._BaseZone_bindCallback_closure0(this, registered);
    },
    bindCallback$1: function(f) {
      return this.bindCallback$2$runGuarded(f, true);
    },
    bindUnaryCallback$2$runGuarded: function(f, runGuarded) {
      var registered = this.registerUnaryCallback$1(f);
      if (runGuarded)
        return new P._BaseZone_bindUnaryCallback_closure(this, registered);
      else
        return new P._BaseZone_bindUnaryCallback_closure0(this, registered);
    }
  },
  _BaseZone_bindCallback_closure: {
    "": "Closure:8;this_0,registered_1",
    call$0: function() {
      return this.this_0.runGuarded$1(this.registered_1);
    }
  },
  _BaseZone_bindCallback_closure0: {
    "": "Closure:8;this_2,registered_3",
    call$0: function() {
      return this.this_2.run$1(this.registered_3);
    }
  },
  _BaseZone_bindUnaryCallback_closure: {
    "": "Closure:10;this_0,registered_1",
    call$1: function(arg) {
      return this.this_0.runUnaryGuarded$2(this.registered_1, arg);
    }
  },
  _BaseZone_bindUnaryCallback_closure0: {
    "": "Closure:10;this_2,registered_3",
    call$1: function(arg) {
      return this.this_2.runUnary$2(this.registered_3, arg);
    }
  },
  _rootHandleUncaughtError_closure: {
    "": "Closure:8;error_0,stackTrace_1",
    call$0: function() {
      P._scheduleAsyncCallback(new P._rootHandleUncaughtError__closure(this.error_0, this.stackTrace_1));
    }
  },
  _rootHandleUncaughtError__closure: {
    "": "Closure:8;error_2,stackTrace_3",
    call$0: function() {
      var t1, trace, t2;
      t1 = this.error_2;
      P.print("Uncaught Error: " + H.S(t1));
      trace = this.stackTrace_3;
      if (trace == null) {
        t2 = J.getInterceptor(t1);
        t2 = typeof t1 === "object" && t1 !== null && !!t2.$isError;
      } else
        t2 = false;
      if (t2)
        trace = t1.get$stackTrace();
      if (trace != null)
        P.print("Stack Trace: \n" + H.S(trace) + "\n");
      throw H.wrapException(t1);
    }
  },
  _RootZone: {
    "": "_BaseZone;",
    $index: function(_, key) {
      return;
    },
    handleUncaughtError$2: function(error, stackTrace) {
      return P._rootHandleUncaughtError(this, null, this, error, stackTrace);
    },
    run$1: function(f) {
      return P._rootRun(this, null, this, f);
    },
    runUnary$2: function(f, arg) {
      return P._rootRunUnary(this, null, this, f, arg);
    },
    registerCallback$1: function(f) {
      return f;
    },
    registerUnaryCallback$1: function(f) {
      return f;
    }
  }
}],
["dart.collection", "dart:collection", , P, {
  "": "",
  _HashSet__newHashTable: function() {
    var table = Object.create(null);
    table["<non-identifier-key>"] = table;
    delete table["<non-identifier-key>"];
    return table;
  },
  _defaultEquals: [function(a, b) {
    return J.$eq(a, b);
  }, "call$2", "_defaultEquals$closure", 4, 0, 4],
  _defaultHashCode: [function(a) {
    return J.get$hashCode$(a);
  }, "call$1", "_defaultHashCode$closure", 2, 0, 5],
  HashMap_HashMap: function(equals, hashCode, isValidKey, $K, $V) {
    return H.setRuntimeTypeInfo(new P._HashMap(0, null, null, null, null), [$K, $V]);
  },
  HashSet_HashSet$identity: function($E) {
    return H.setRuntimeTypeInfo(new P._IdentityHashSet(0, null, null, null, null), [$E]);
  },
  _iterableToString: function(iterable) {
    var parts, t1;
    t1 = $.get$_toStringVisiting();
    if (t1.contains$1(t1, iterable))
      return "(...)";
    t1 = $.get$_toStringVisiting();
    t1.add$1(t1, iterable);
    parts = [];
    try {
      P._iterablePartsToStrings(iterable, parts);
    } finally {
      t1 = $.get$_toStringVisiting();
      t1.remove$1(t1, iterable);
    }
    t1 = P.StringBuffer$("(");
    t1.writeAll$2(parts, ", ");
    t1.write$1(")");
    return t1._contents;
  },
  _iterablePartsToStrings: function(iterable, parts) {
    var it, $length, count, next, ultimateString, penultimateString, penultimate, ultimate, ultimate0, elision;
    it = iterable.get$iterator(iterable);
    $length = 0;
    count = 0;
    while (true) {
      if (!($length < 80 || count < 3))
        break;
      if (!it.moveNext$0())
        return;
      next = H.S(it.get$current());
      parts.push(next);
      $length += next.length + 2;
      ++count;
    }
    if (!it.moveNext$0()) {
      if (count <= 5)
        return;
      if (0 >= parts.length)
        return H.ioore(parts, 0);
      ultimateString = parts.pop();
      if (0 >= parts.length)
        return H.ioore(parts, 0);
      penultimateString = parts.pop();
    } else {
      penultimate = it.get$current();
      ++count;
      if (!it.moveNext$0()) {
        if (count <= 4) {
          parts.push(H.S(penultimate));
          return;
        }
        ultimateString = H.S(penultimate);
        if (0 >= parts.length)
          return H.ioore(parts, 0);
        penultimateString = parts.pop();
        $length += ultimateString.length + 2;
      } else {
        ultimate = it.get$current();
        ++count;
        for (; it.moveNext$0(); penultimate = ultimate, ultimate = ultimate0) {
          ultimate0 = it.get$current();
          ++count;
          if (count > 100) {
            while (true) {
              if (!($length > 75 && count > 3))
                break;
              if (0 >= parts.length)
                return H.ioore(parts, 0);
              $length -= parts.pop().length + 2;
              --count;
            }
            parts.push("...");
            return;
          }
        }
        penultimateString = H.S(penultimate);
        ultimateString = H.S(ultimate);
        $length += ultimateString.length + penultimateString.length + 4;
      }
    }
    if (count > parts.length + 2) {
      $length += 5;
      elision = "...";
    } else
      elision = null;
    while (true) {
      if (!($length > 80 && parts.length > 3))
        break;
      if (0 >= parts.length)
        return H.ioore(parts, 0);
      $length -= parts.pop().length + 2;
      if (elision == null) {
        $length += 5;
        elision = "...";
      }
    }
    if (elision != null)
      parts.push(elision);
    parts.push(penultimateString);
    parts.push(ultimateString);
  },
  LinkedHashMap_LinkedHashMap: function(equals, hashCode, isValidKey, $K, $V) {
    return H.setRuntimeTypeInfo(new P._LinkedHashMap(0, null, null, null, null, null, 0), [$K, $V]);
  },
  LinkedHashSet_LinkedHashSet: function(equals, hashCode, isValidKey, $E) {
    return H.setRuntimeTypeInfo(new P._LinkedHashSet(0, null, null, null, null, null, 0), [$E]);
  },
  Maps_mapToString: function(m) {
    var t1, result, i, t2;
    t1 = {};
    for (i = 0; t2 = $.get$Maps__toStringList(), i < t2.length; ++i)
      if (t2[i] === m)
        return "{...}";
    result = P.StringBuffer$("");
    try {
      $.get$Maps__toStringList().push(m);
      result.write$1("{");
      t1.first_0 = true;
      J.forEach$1$ax(m, new P.Maps_mapToString_closure(t1, result));
      result.write$1("}");
    } finally {
      t1 = $.get$Maps__toStringList();
      if (0 >= t1.length)
        return H.ioore(t1, 0);
      t1.pop();
    }
    return result.get$_contents();
  },
  _HashMap: {
    "": "Object;_collection$_length,_strings,_nums,_rest,_keys",
    get$length: function(_) {
      return this._collection$_length;
    },
    get$keys: function() {
      return H.setRuntimeTypeInfo(new P.HashMapKeyIterable(this), [H.getTypeArgumentByIndex(this, 0)]);
    },
    get$values: function(_) {
      return H.MappedIterable_MappedIterable(H.setRuntimeTypeInfo(new P.HashMapKeyIterable(this), [H.getTypeArgumentByIndex(this, 0)]), new P._HashMap_values_closure(this), H.getTypeArgumentByIndex(this, 0), H.getTypeArgumentByIndex(this, 1));
    },
    $index: function(_, key) {
      var strings, t1, entry, nums, rest, bucket, index;
      if (typeof key === "string" && key !== "__proto__") {
        strings = this._strings;
        if (strings == null)
          t1 = null;
        else {
          entry = strings[key];
          t1 = entry === strings ? null : entry;
        }
        return t1;
      } else if (typeof key === "number" && (key & 0x3ffffff) === key) {
        nums = this._nums;
        if (nums == null)
          t1 = null;
        else {
          entry = nums[key];
          t1 = entry === nums ? null : entry;
        }
        return t1;
      } else {
        rest = this._rest;
        if (rest == null)
          return;
        bucket = rest[this._computeHashCode$1(key)];
        index = this._findBucketIndex$2(bucket, key);
        return index < 0 ? null : bucket[index + 1];
      }
    },
    $indexSet: function(_, key, value) {
      var strings, nums, rest, hash, bucket, index;
      if (typeof key === "string" && key !== "__proto__") {
        strings = this._strings;
        if (strings == null) {
          strings = P._HashMap__newHashTable();
          this._strings = strings;
        }
        this._addHashTableEntry$3(strings, key, value);
      } else if (typeof key === "number" && (key & 0x3ffffff) === key) {
        nums = this._nums;
        if (nums == null) {
          nums = P._HashMap__newHashTable();
          this._nums = nums;
        }
        this._addHashTableEntry$3(nums, key, value);
      } else {
        rest = this._rest;
        if (rest == null) {
          rest = P._HashMap__newHashTable();
          this._rest = rest;
        }
        hash = this._computeHashCode$1(key);
        bucket = rest[hash];
        if (bucket == null) {
          P._HashMap__setTableEntry(rest, hash, [key, value]);
          this._collection$_length = this._collection$_length + 1;
          this._keys = null;
        } else {
          index = this._findBucketIndex$2(bucket, key);
          if (index >= 0)
            bucket[index + 1] = value;
          else {
            bucket.push(key, value);
            this._collection$_length = this._collection$_length + 1;
            this._keys = null;
          }
        }
      }
    },
    forEach$1: function(_, action) {
      var keys, $length, i, key;
      keys = this._computeKeys$0();
      for ($length = keys.length, i = 0; i < $length; ++i) {
        key = keys[i];
        action.call$2(key, this.$index(this, key));
        if (keys !== this._keys)
          throw H.wrapException(P.ConcurrentModificationError$(this));
      }
    },
    _computeKeys$0: function() {
      var t1, result, strings, names, entries, index, i, nums, rest, bucket, $length, i0;
      t1 = this._keys;
      if (t1 != null)
        return t1;
      result = Array(this._collection$_length);
      result.fixed$length = init;
      strings = this._strings;
      if (strings != null) {
        names = Object.getOwnPropertyNames(strings);
        entries = names.length;
        for (index = 0, i = 0; i < entries; ++i) {
          result[index] = names[i];
          ++index;
        }
      } else
        index = 0;
      nums = this._nums;
      if (nums != null) {
        names = Object.getOwnPropertyNames(nums);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          result[index] = +names[i];
          ++index;
        }
      }
      rest = this._rest;
      if (rest != null) {
        names = Object.getOwnPropertyNames(rest);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          bucket = rest[names[i]];
          $length = bucket.length;
          for (i0 = 0; i0 < $length; i0 += 2) {
            result[index] = bucket[i0];
            ++index;
          }
        }
      }
      this._keys = result;
      return result;
    },
    _addHashTableEntry$3: function(table, key, value) {
      if (table[key] == null) {
        this._collection$_length = this._collection$_length + 1;
        this._keys = null;
      }
      P._HashMap__setTableEntry(table, key, value);
    },
    _computeHashCode$1: function(key) {
      return J.get$hashCode$(key) & 0x3ffffff;
    },
    _findBucketIndex$2: function(bucket, key) {
      var $length, i;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; i += 2)
        if (J.$eq(bucket[i], key))
          return i;
      return -1;
    },
    $isMap: true,
    static: {_HashMap__setTableEntry: function(table, key, value) {
        if (value == null)
          table[key] = table;
        else
          table[key] = value;
      }, _HashMap__newHashTable: function() {
        var table = Object.create(null);
        P._HashMap__setTableEntry(table, "<non-identifier-key>", table);
        delete table["<non-identifier-key>"];
        return table;
      }}
  },
  _HashMap_values_closure: {
    "": "Closure:10;this_0",
    call$1: function(each) {
      var t1 = this.this_0;
      return t1.$index(t1, each);
    }
  },
  HashMapKeyIterable: {
    "": "IterableBase;_map",
    get$length: function(_) {
      return this._map._collection$_length;
    },
    get$iterator: function(_) {
      var t1 = this._map;
      return new P.HashMapKeyIterator(t1, t1._computeKeys$0(), 0, null);
    },
    forEach$1: function(_, f) {
      var t1, keys, $length, i;
      t1 = this._map;
      keys = t1._computeKeys$0();
      for ($length = keys.length, i = 0; i < $length; ++i) {
        f.call$1(keys[i]);
        if (keys !== t1._keys)
          throw H.wrapException(P.ConcurrentModificationError$(t1));
      }
    },
    $asIterableBase: null
  },
  HashMapKeyIterator: {
    "": "Object;_map,_keys,_offset,_collection$_current",
    get$current: function() {
      return this._collection$_current;
    },
    moveNext$0: function() {
      var keys, offset, t1;
      keys = this._keys;
      offset = this._offset;
      t1 = this._map;
      if (keys !== t1._keys)
        throw H.wrapException(P.ConcurrentModificationError$(t1));
      else if (offset >= keys.length) {
        this._collection$_current = null;
        return false;
      } else {
        this._collection$_current = keys[offset];
        this._offset = offset + 1;
        return true;
      }
    }
  },
  _LinkedHashMap: {
    "": "Object;_collection$_length,_strings,_nums,_rest,_first,_last,_modifications",
    get$length: function(_) {
      return this._collection$_length;
    },
    get$keys: function() {
      return H.setRuntimeTypeInfo(new P.LinkedHashMapKeyIterable(this), [H.getTypeArgumentByIndex(this, 0)]);
    },
    get$values: function(_) {
      return H.MappedIterable_MappedIterable(H.setRuntimeTypeInfo(new P.LinkedHashMapKeyIterable(this), [H.getTypeArgumentByIndex(this, 0)]), new P._LinkedHashMap_values_closure(this), H.getTypeArgumentByIndex(this, 0), H.getTypeArgumentByIndex(this, 1));
    },
    containsKey$1: function(key) {
      var nums, rest;
      if ((key & 0x3ffffff) === key) {
        nums = this._nums;
        if (nums == null)
          return false;
        return nums[key] != null;
      } else {
        rest = this._rest;
        if (rest == null)
          return false;
        return this._findBucketIndex$2(rest[this._computeHashCode$1(key)], key) >= 0;
      }
    },
    $index: function(_, key) {
      var strings, cell, nums, rest, bucket, index;
      if (typeof key === "string" && key !== "__proto__") {
        strings = this._strings;
        if (strings == null)
          return;
        cell = strings[key];
        return cell == null ? null : cell.get$_value();
      } else if (typeof key === "number" && (key & 0x3ffffff) === key) {
        nums = this._nums;
        if (nums == null)
          return;
        cell = nums[key];
        return cell == null ? null : cell.get$_value();
      } else {
        rest = this._rest;
        if (rest == null)
          return;
        bucket = rest[this._computeHashCode$1(key)];
        index = this._findBucketIndex$2(bucket, key);
        if (index < 0)
          return;
        return bucket[index].get$_value();
      }
    },
    $indexSet: function(_, key, value) {
      var strings, nums, rest, hash, bucket, index;
      if (typeof key === "string" && key !== "__proto__") {
        strings = this._strings;
        if (strings == null) {
          strings = P._LinkedHashMap__newHashTable();
          this._strings = strings;
        }
        this._addHashTableEntry$3(strings, key, value);
      } else if (typeof key === "number" && (key & 0x3ffffff) === key) {
        nums = this._nums;
        if (nums == null) {
          nums = P._LinkedHashMap__newHashTable();
          this._nums = nums;
        }
        this._addHashTableEntry$3(nums, key, value);
      } else {
        rest = this._rest;
        if (rest == null) {
          rest = P._LinkedHashMap__newHashTable();
          this._rest = rest;
        }
        hash = this._computeHashCode$1(key);
        bucket = rest[hash];
        if (bucket == null)
          rest[hash] = [this._newLinkedCell$2(key, value)];
        else {
          index = this._findBucketIndex$2(bucket, key);
          if (index >= 0)
            bucket[index].set$_value(value);
          else
            bucket.push(this._newLinkedCell$2(key, value));
        }
      }
    },
    remove$1: function(_, key) {
      var rest, bucket, index, cell;
      if (typeof key === "string" && key !== "__proto__")
        return this._removeHashTableEntry$2(this._strings, key);
      else if (typeof key === "number" && (key & 0x3ffffff) === key)
        return this._removeHashTableEntry$2(this._nums, key);
      else {
        rest = this._rest;
        if (rest == null)
          return;
        bucket = rest[this._computeHashCode$1(key)];
        index = this._findBucketIndex$2(bucket, key);
        if (index < 0)
          return;
        cell = bucket.splice(index, 1)[0];
        this._unlinkCell$1(cell);
        return cell.get$_value();
      }
    },
    forEach$1: function(_, action) {
      var cell, modifications;
      cell = this._first;
      modifications = this._modifications;
      for (; cell != null;) {
        action.call$2(cell.get$_key(), cell._value);
        if (modifications !== this._modifications)
          throw H.wrapException(P.ConcurrentModificationError$(this));
        cell = cell._next;
      }
    },
    _addHashTableEntry$3: function(table, key, value) {
      var cell = table[key];
      if (cell == null)
        table[key] = this._newLinkedCell$2(key, value);
      else
        cell.set$_value(value);
    },
    _removeHashTableEntry$2: function(table, key) {
      var cell;
      if (table == null)
        return;
      cell = table[key];
      if (cell == null)
        return;
      this._unlinkCell$1(cell);
      delete table[key];
      return cell.get$_value();
    },
    _newLinkedCell$2: function(key, value) {
      var cell, last;
      cell = new P.LinkedHashMapCell(key, value, null, null);
      if (this._first == null) {
        this._last = cell;
        this._first = cell;
      } else {
        last = this._last;
        cell._previous = last;
        last.set$_next(cell);
        this._last = cell;
      }
      this._collection$_length = this._collection$_length + 1;
      this._modifications = this._modifications + 1 & 67108863;
      return cell;
    },
    _unlinkCell$1: function(cell) {
      var previous, next;
      previous = cell.get$_previous();
      next = cell.get$_next();
      if (previous == null)
        this._first = next;
      else
        previous.set$_next(next);
      if (next == null)
        this._last = previous;
      else
        next.set$_previous(previous);
      this._collection$_length = this._collection$_length - 1;
      this._modifications = this._modifications + 1 & 67108863;
    },
    _computeHashCode$1: function(key) {
      return J.get$hashCode$(key) & 0x3ffffff;
    },
    _findBucketIndex$2: function(bucket, key) {
      var $length, i;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; ++i)
        if (J.$eq(bucket[i].get$_key(), key))
          return i;
      return -1;
    },
    toString$0: function(_) {
      return P.Maps_mapToString(this);
    },
    $isMap: true,
    static: {_LinkedHashMap__newHashTable: function() {
        var table = Object.create(null);
        table["<non-identifier-key>"] = table;
        delete table["<non-identifier-key>"];
        return table;
      }}
  },
  _LinkedHashMap_values_closure: {
    "": "Closure:10;this_0",
    call$1: function(each) {
      var t1 = this.this_0;
      return t1.$index(t1, each);
    }
  },
  LinkedHashMapCell: {
    "": "Object;_key<,_value@,_next@,_previous@"
  },
  LinkedHashMapKeyIterable: {
    "": "IterableBase;_map",
    get$length: function(_) {
      return this._map._collection$_length;
    },
    get$iterator: function(_) {
      var t1, t2;
      t1 = this._map;
      t2 = new P.LinkedHashMapKeyIterator(t1, t1._modifications, null, null);
      t2._cell = t1._first;
      return t2;
    },
    forEach$1: function(_, f) {
      var t1, cell, modifications;
      t1 = this._map;
      cell = t1._first;
      modifications = t1._modifications;
      for (; cell != null;) {
        f.call$1(cell.get$_key());
        if (modifications !== t1._modifications)
          throw H.wrapException(P.ConcurrentModificationError$(t1));
        cell = cell._next;
      }
    },
    $asIterableBase: null
  },
  LinkedHashMapKeyIterator: {
    "": "Object;_map,_modifications,_cell,_collection$_current",
    get$current: function() {
      return this._collection$_current;
    },
    moveNext$0: function() {
      var t1 = this._map;
      if (this._modifications !== t1._modifications)
        throw H.wrapException(P.ConcurrentModificationError$(t1));
      else {
        t1 = this._cell;
        if (t1 == null) {
          this._collection$_current = null;
          return false;
        } else {
          this._collection$_current = t1.get$_key();
          this._cell = t1._next;
          return true;
        }
      }
    }
  },
  _HashSet: {
    "": "_HashSetBase;",
    get$iterator: function(_) {
      return new P.HashSetIterator(this, this._computeElements$0(), 0, null);
    },
    get$length: function(_) {
      return this._collection$_length;
    },
    contains$1: function(_, object) {
      var strings, nums, rest;
      if (typeof object === "string" && object !== "__proto__") {
        strings = this._strings;
        return strings == null ? false : strings[object] != null;
      } else if (typeof object === "number" && (object & 0x3ffffff) === object) {
        nums = this._nums;
        return nums == null ? false : nums[object] != null;
      } else {
        rest = this._rest;
        if (rest == null)
          return false;
        return this._findBucketIndex$2(rest[this._computeHashCode$1(object)], object) >= 0;
      }
    },
    lookup$1: function(object) {
      var t1, rest, bucket, index;
      if (!(typeof object === "string" && object !== "__proto__"))
        t1 = typeof object === "number" && (object & 0x3ffffff) === object;
      else
        t1 = true;
      if (t1)
        return this.contains$1(this, object) ? object : null;
      rest = this._rest;
      if (rest == null)
        return;
      bucket = rest[this._computeHashCode$1(object)];
      index = this._findBucketIndex$2(bucket, object);
      if (index < 0)
        return;
      return J.$index$asx(bucket, index);
    },
    add$1: function(_, element) {
      var rest, hash, bucket;
      rest = this._rest;
      if (rest == null) {
        rest = P._HashSet__newHashTable();
        this._rest = rest;
      }
      hash = this._computeHashCode$1(element);
      bucket = rest[hash];
      if (bucket == null)
        rest[hash] = [element];
      else {
        if (this._findBucketIndex$2(bucket, element) >= 0)
          return false;
        bucket.push(element);
      }
      this._collection$_length = this._collection$_length + 1;
      this._elements = null;
      return true;
    },
    remove$1: function(_, object) {
      var rest, bucket, index;
      if (typeof object === "string" && object !== "__proto__")
        return this._removeHashTableEntry$2(this._strings, object);
      else if (typeof object === "number" && (object & 0x3ffffff) === object)
        return this._removeHashTableEntry$2(this._nums, object);
      else {
        rest = this._rest;
        if (rest == null)
          return false;
        bucket = rest[this._computeHashCode$1(object)];
        index = this._findBucketIndex$2(bucket, object);
        if (index < 0)
          return false;
        this._collection$_length = this._collection$_length - 1;
        this._elements = null;
        bucket.splice(index, 1);
        return true;
      }
    },
    _computeElements$0: function() {
      var t1, result, strings, names, entries, index, i, nums, rest, bucket, $length, i0;
      t1 = this._elements;
      if (t1 != null)
        return t1;
      result = Array(this._collection$_length);
      result.fixed$length = init;
      strings = this._strings;
      if (strings != null) {
        names = Object.getOwnPropertyNames(strings);
        entries = names.length;
        for (index = 0, i = 0; i < entries; ++i) {
          result[index] = names[i];
          ++index;
        }
      } else
        index = 0;
      nums = this._nums;
      if (nums != null) {
        names = Object.getOwnPropertyNames(nums);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          result[index] = +names[i];
          ++index;
        }
      }
      rest = this._rest;
      if (rest != null) {
        names = Object.getOwnPropertyNames(rest);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          bucket = rest[names[i]];
          $length = bucket.length;
          for (i0 = 0; i0 < $length; ++i0) {
            result[index] = bucket[i0];
            ++index;
          }
        }
      }
      this._elements = result;
      return result;
    },
    _removeHashTableEntry$2: function(table, element) {
      if (table != null && table[element] != null) {
        delete table[element];
        this._collection$_length = this._collection$_length - 1;
        this._elements = null;
        return true;
      } else
        return false;
    },
    _computeHashCode$1: function(element) {
      return J.get$hashCode$(element) & 0x3ffffff;
    },
    _findBucketIndex$2: function(bucket, element) {
      var $length, i;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; ++i)
        if (J.$eq(bucket[i], element))
          return i;
      return -1;
    },
    $as_HashSetBase: null
  },
  _IdentityHashSet: {
    "": "_HashSet;_collection$_length,_strings,_nums,_rest,_elements",
    _computeHashCode$1: function(key) {
      return H.objectHashCode(key) & 0x3ffffff;
    },
    _findBucketIndex$2: function(bucket, element) {
      var $length, i, t1;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; ++i) {
        t1 = bucket[i];
        if (t1 == null ? element == null : t1 === element)
          return i;
      }
      return -1;
    },
    $as_HashSet: null
  },
  HashSetIterator: {
    "": "Object;_set,_elements,_offset,_collection$_current",
    get$current: function() {
      return this._collection$_current;
    },
    moveNext$0: function() {
      var elements, offset, t1;
      elements = this._elements;
      offset = this._offset;
      t1 = this._set;
      if (elements !== t1._elements)
        throw H.wrapException(P.ConcurrentModificationError$(t1));
      else if (offset >= elements.length) {
        this._collection$_current = null;
        return false;
      } else {
        this._collection$_current = elements[offset];
        this._offset = offset + 1;
        return true;
      }
    }
  },
  _LinkedHashSet: {
    "": "_HashSetBase;_collection$_length,_strings,_nums,_rest,_first,_last,_modifications",
    get$iterator: function(_) {
      var t1 = new P.LinkedHashSetIterator(this, this._modifications, null, null);
      t1._cell = this._first;
      return t1;
    },
    get$length: function(_) {
      return this._collection$_length;
    },
    contains$1: function(_, object) {
      var strings, nums, rest;
      if (typeof object === "string" && object !== "__proto__") {
        strings = this._strings;
        if (strings == null)
          return false;
        return strings[object] != null;
      } else if (typeof object === "number" && (object & 0x3ffffff) === object) {
        nums = this._nums;
        if (nums == null)
          return false;
        return nums[object] != null;
      } else {
        rest = this._rest;
        if (rest == null)
          return false;
        return this._findBucketIndex$2(rest[this._computeHashCode$1(object)], object) >= 0;
      }
    },
    lookup$1: function(object) {
      var t1, rest, bucket, index;
      if (!(typeof object === "string" && object !== "__proto__"))
        t1 = typeof object === "number" && (object & 0x3ffffff) === object;
      else
        t1 = true;
      if (t1)
        return this.contains$1(this, object) ? object : null;
      else {
        rest = this._rest;
        if (rest == null)
          return;
        bucket = rest[this._computeHashCode$1(object)];
        index = this._findBucketIndex$2(bucket, object);
        if (index < 0)
          return;
        return J.$index$asx(bucket, index).get$_element();
      }
    },
    forEach$1: function(_, action) {
      var cell, modifications;
      cell = this._first;
      modifications = this._modifications;
      for (; cell != null;) {
        action.call$1(cell.get$_element());
        if (modifications !== this._modifications)
          throw H.wrapException(P.ConcurrentModificationError$(this));
        cell = cell._next;
      }
    },
    add$1: function(_, element) {
      var nums, rest, hash, bucket;
      if ((element & 0x3ffffff) === element) {
        nums = this._nums;
        if (nums == null) {
          nums = P._LinkedHashSet__newHashTable();
          this._nums = nums;
        }
        return this._addHashTableEntry$2(nums, element);
      } else {
        rest = this._rest;
        if (rest == null) {
          rest = P._LinkedHashSet__newHashTable();
          this._rest = rest;
        }
        hash = this._computeHashCode$1(element);
        bucket = rest[hash];
        if (bucket == null)
          rest[hash] = [this._newLinkedCell$1(element)];
        else {
          if (this._findBucketIndex$2(bucket, element) >= 0)
            return false;
          bucket.push(this._newLinkedCell$1(element));
        }
        return true;
      }
    },
    _addHashTableEntry$2: function(table, element) {
      if (table[element] != null)
        return false;
      table[element] = this._newLinkedCell$1(element);
      return true;
    },
    _newLinkedCell$1: function(element) {
      var cell, last;
      cell = new P.LinkedHashSetCell(element, null, null);
      if (this._first == null) {
        this._last = cell;
        this._first = cell;
      } else {
        last = this._last;
        cell._previous = last;
        last.set$_next(cell);
        this._last = cell;
      }
      this._collection$_length = this._collection$_length + 1;
      this._modifications = this._modifications + 1 & 67108863;
      return cell;
    },
    _computeHashCode$1: function(element) {
      return J.get$hashCode$(element) & 0x3ffffff;
    },
    _findBucketIndex$2: function(bucket, element) {
      var $length, i;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; ++i)
        if (bucket[i].get$_element() === element)
          return i;
      return -1;
    },
    $as_HashSetBase: null,
    static: {_LinkedHashSet__newHashTable: function() {
        var table = Object.create(null);
        table["<non-identifier-key>"] = table;
        delete table["<non-identifier-key>"];
        return table;
      }}
  },
  LinkedHashSetCell: {
    "": "Object;_element<,_next@,_previous@"
  },
  LinkedHashSetIterator: {
    "": "Object;_set,_modifications,_cell,_collection$_current",
    get$current: function() {
      return this._collection$_current;
    },
    moveNext$0: function() {
      var t1 = this._set;
      if (this._modifications !== t1._modifications)
        throw H.wrapException(P.ConcurrentModificationError$(t1));
      else {
        t1 = this._cell;
        if (t1 == null) {
          this._collection$_current = null;
          return false;
        } else {
          this._collection$_current = t1.get$_element();
          this._cell = t1._next;
          return true;
        }
      }
    }
  },
  _HashSetBase: {
    "": "IterableBase;",
    toString$0: function(_) {
      return H.IterableMixinWorkaround_toStringIterable(this, "{", "}");
    },
    $asIterableBase: null
  },
  IterableBase: {
    "": "Object;",
    forEach$1: function(_, f) {
      var t1;
      for (t1 = this.get$iterator(this); t1.moveNext$0();)
        f.call$1(t1.get$current());
    },
    get$length: function(_) {
      var it, count;
      it = this.get$iterator(this);
      for (count = 0; it.moveNext$0();)
        ++count;
      return count;
    },
    elementAt$1: function(_, index) {
      var t1, remaining, element;
      if (index < 0)
        throw H.wrapException(P.RangeError$value(index));
      for (t1 = this.get$iterator(this), remaining = index; t1.moveNext$0();) {
        element = t1.get$current();
        if (remaining === 0)
          return element;
        --remaining;
      }
      throw H.wrapException(P.RangeError$value(index));
    },
    toString$0: function(_) {
      return P._iterableToString(this);
    }
  },
  ListMixin: {
    "": "Object;",
    get$iterator: function(receiver) {
      return new H.ListIterator(receiver, this.get$length(receiver), 0, null);
    },
    elementAt$1: function(receiver, index) {
      return this.$index(receiver, index);
    },
    forEach$1: function(receiver, action) {
      var $length, i;
      $length = this.get$length(receiver);
      for (i = 0; i < $length; ++i) {
        action.call$1(this.$index(receiver, i));
        if ($length !== this.get$length(receiver))
          throw H.wrapException(P.ConcurrentModificationError$(receiver));
      }
    },
    toString$0: function(receiver) {
      var result, t1;
      t1 = $.get$_toStringVisiting();
      if (t1.contains$1(t1, receiver))
        return "[...]";
      result = P.StringBuffer$("");
      try {
        t1 = $.get$_toStringVisiting();
        t1.add$1(t1, receiver);
        result.write$1("[");
        result.writeAll$2(receiver, ", ");
        result.write$1("]");
      } finally {
        t1 = $.get$_toStringVisiting();
        t1.remove$1(t1, receiver);
      }
      return result.get$_contents();
    },
    $isList: true,
    $asList: null
  },
  Maps_mapToString_closure: {
    "": "Closure:9;box_0,result_1",
    call$2: function(k, v) {
      var t1 = this.box_0;
      if (!t1.first_0)
        this.result_1.write$1(", ");
      t1.first_0 = false;
      t1 = this.result_1;
      t1.write$1(k);
      t1.write$1(": ");
      t1.write$1(v);
    }
  },
  ListQueue: {
    "": "IterableBase;_table,_head,_tail,_modificationCount",
    get$iterator: function(_) {
      return new P._ListQueueIterator(this, this._tail, this._modificationCount, this._head, null);
    },
    forEach$1: function(_, action) {
      var modificationCount, i, t1;
      modificationCount = this._modificationCount;
      for (i = this._head; i !== this._tail; i = (i + 1 & this._table.length - 1) >>> 0) {
        t1 = this._table;
        if (i < 0 || i >= t1.length)
          return H.ioore(t1, i);
        action.call$1(t1[i]);
        if (modificationCount !== this._modificationCount)
          H.throwExpression(P.ConcurrentModificationError$(this));
      }
    },
    get$length: function(_) {
      return (this._tail - this._head & this._table.length - 1) >>> 0;
    },
    toString$0: function(_) {
      return H.IterableMixinWorkaround_toStringIterable(this, "{", "}");
    },
    removeFirst$0: function() {
      var t1, t2, t3, result;
      t1 = this._head;
      if (t1 === this._tail)
        throw H.wrapException(P.StateError$("No elements"));
      this._modificationCount = this._modificationCount + 1;
      t2 = this._table;
      t3 = t2.length;
      if (t1 >= t3)
        return H.ioore(t2, t1);
      result = t2[t1];
      this._head = (t1 + 1 & t3 - 1) >>> 0;
      return result;
    },
    _add$1: function(element) {
      var t1, t2, t3, newTable, split;
      t1 = this._table;
      t2 = this._tail;
      t3 = t1.length;
      if (t2 >= t3)
        return H.ioore(t1, t2);
      t1[t2] = element;
      t2 = (t2 + 1 & t3 - 1) >>> 0;
      this._tail = t2;
      if (this._head === t2) {
        t1 = Array(t3 * 2);
        t1.fixed$length = init;
        newTable = H.setRuntimeTypeInfo(t1, [H.getTypeArgumentByIndex(this, 0)]);
        t1 = this._table;
        t2 = this._head;
        split = t1.length - t2;
        H.IterableMixinWorkaround_setRangeList(newTable, 0, split, t1, t2);
        t1 = this._head;
        t2 = this._table;
        H.IterableMixinWorkaround_setRangeList(newTable, split, split + t1, t2, 0);
        this._head = 0;
        this._tail = this._table.length;
        this._table = newTable;
      }
      this._modificationCount = this._modificationCount + 1;
    },
    ListQueue$1: function(initialCapacity, $E) {
      var t1 = Array(8);
      t1.fixed$length = init;
      this._table = H.setRuntimeTypeInfo(t1, [$E]);
    },
    $asIterableBase: null,
    static: {"": "ListQueue__INITIAL_CAPACITY"}
  },
  _ListQueueIterator: {
    "": "Object;_queue,_end,_modificationCount,_collection$_position,_collection$_current",
    get$current: function() {
      return this._collection$_current;
    },
    moveNext$0: function() {
      var t1, t2, t3;
      t1 = this._queue;
      if (this._modificationCount !== t1._modificationCount)
        H.throwExpression(P.ConcurrentModificationError$(t1));
      t2 = this._collection$_position;
      if (t2 === this._end) {
        this._collection$_current = null;
        return false;
      }
      t1 = t1._table;
      t3 = t1.length;
      if (t2 >= t3)
        return H.ioore(t1, t2);
      this._collection$_current = t1[t2];
      this._collection$_position = (t2 + 1 & t3 - 1) >>> 0;
      return true;
    }
  }
}],
["dart.core", "dart:core", , P, {
  "": "",
  _symbolToString: function(symbol) {
    return H.Symbol_getName(symbol);
  },
  Error_safeToString: function(object) {
    var buffer, t1, i, t2, codeUnit, charCodes;
    if (typeof object === "number" || typeof object === "boolean" || null == object)
      return J.toString$0(object);
    if (typeof object === "string") {
      buffer = new P.StringBuffer("");
      buffer._contents = "\"";
      for (t1 = object.length, i = 0, t2 = "\""; i < t1; ++i) {
        codeUnit = C.JSString_methods.codeUnitAt$1(object, i);
        if (codeUnit <= 31)
          if (codeUnit === 10) {
            t2 = buffer._contents + "\\n";
            buffer._contents = t2;
          } else if (codeUnit === 13) {
            t2 = buffer._contents + "\\r";
            buffer._contents = t2;
          } else if (codeUnit === 9) {
            t2 = buffer._contents + "\\t";
            buffer._contents = t2;
          } else {
            t2 = buffer._contents + "\\x";
            buffer._contents = t2;
            if (codeUnit < 16)
              buffer._contents = t2 + "0";
            else {
              buffer._contents = t2 + "1";
              codeUnit -= 16;
            }
            t2 = codeUnit < 10 ? 48 + codeUnit : 87 + codeUnit;
            charCodes = P.List_List$filled(1, t2, J.JSInt);
            t2 = H.Primitives_stringFromCharCodes(charCodes);
            t2 = buffer._contents + t2;
            buffer._contents = t2;
          }
        else if (codeUnit === 92) {
          t2 = buffer._contents + "\\\\";
          buffer._contents = t2;
        } else if (codeUnit === 34) {
          t2 = buffer._contents + "\\\"";
          buffer._contents = t2;
        } else {
          charCodes = P.List_List$filled(1, codeUnit, J.JSInt);
          t2 = H.Primitives_stringFromCharCodes(charCodes);
          t2 = buffer._contents + t2;
          buffer._contents = t2;
        }
      }
      t1 = t2 + "\"";
      buffer._contents = t1;
      return t1;
    }
    return "Instance of '" + H.Primitives_objectTypeName(object) + "'";
  },
  Exception_Exception: function(message) {
    return new P._ExceptionImplementation(message);
  },
  identical: [function(a, b) {
    return a == null ? b == null : a === b;
  }, "call$2", "identical$closure", 4, 0, 6],
  identityHashCode: [function(object) {
    return H.objectHashCode(object);
  }, "call$1", "identityHashCode$closure", 2, 0, 7],
  List_List$filled: function($length, fill, $E) {
    var result, t1, i;
    result = J.JSArray_JSArray$fixed($length, $E);
    if ($length !== 0 && true)
      for (t1 = result.length, i = 0; i < t1; ++i)
        result[i] = fill;
    return result;
  },
  List_List$from: function(other, growable, $E) {
    var list, t1, $length, fixedList, t2, i, t3;
    list = H.setRuntimeTypeInfo([], [$E]);
    for (t1 = J.get$iterator$ax(other); t1.moveNext$0();)
      list.push(t1.get$current());
    if (growable)
      return list;
    $length = list.length;
    t1 = Array($length);
    t1.fixed$length = init;
    fixedList = H.setRuntimeTypeInfo(t1, [$E]);
    for (t1 = list.length, t2 = fixedList.length, i = 0; i < $length; ++i) {
      if (i >= t1)
        return H.ioore(list, i);
      t3 = list[i];
      if (i >= t2)
        return H.ioore(fixedList, i);
      fixedList[i] = t3;
    }
    return fixedList;
  },
  print: function(object) {
    var line = H.S(object);
    H.printString(line);
  },
  NoSuchMethodError_toString_closure: {
    "": "Closure:16;box_0",
    call$2: function(key, value) {
      var t1 = this.box_0;
      if (t1.i_1 > 0)
        t1.sb_0.write$1(", ");
      t1.sb_0.write$1(P._symbolToString(key));
    }
  },
  DateTime: {
    "": "Object;millisecondsSinceEpoch,isUtc",
    $eq: function(_, other) {
      var t1;
      if (other == null)
        return false;
      t1 = J.getInterceptor(other);
      if (typeof other !== "object" || other === null || !t1.$isDateTime)
        return false;
      return this.millisecondsSinceEpoch === other.millisecondsSinceEpoch && this.isUtc === other.isUtc;
    },
    get$hashCode: function(_) {
      return this.millisecondsSinceEpoch;
    },
    toString$0: function(_) {
      var t1, t2, t3, y, m, d, h, min, sec, ms;
      t1 = new P.DateTime_toString_twoDigits();
      t2 = this.isUtc;
      t3 = t2 ? H.Primitives_lazyAsJsDate(this).getUTCFullYear() + 0 : H.Primitives_lazyAsJsDate(this).getFullYear() + 0;
      y = new P.DateTime_toString_fourDigits().call$1(t3);
      m = t1.call$1(t2 ? H.Primitives_lazyAsJsDate(this).getUTCMonth() + 1 : H.Primitives_lazyAsJsDate(this).getMonth() + 1);
      d = t1.call$1(t2 ? H.Primitives_lazyAsJsDate(this).getUTCDate() + 0 : H.Primitives_lazyAsJsDate(this).getDate() + 0);
      h = t1.call$1(H.Primitives_getHours(this));
      min = t1.call$1(H.Primitives_getMinutes(this));
      sec = t1.call$1(H.Primitives_getSeconds(this));
      t1 = t2 ? H.Primitives_lazyAsJsDate(this).getUTCMilliseconds() + 0 : H.Primitives_lazyAsJsDate(this).getMilliseconds() + 0;
      ms = new P.DateTime_toString_threeDigits().call$1(t1);
      if (t2)
        return H.S(y) + "-" + H.S(m) + "-" + H.S(d) + " " + H.S(h) + ":" + H.S(min) + ":" + H.S(sec) + "." + H.S(ms) + "Z";
      else
        return H.S(y) + "-" + H.S(m) + "-" + H.S(d) + " " + H.S(h) + ":" + H.S(min) + ":" + H.S(sec) + "." + H.S(ms);
    },
    DateTime$_now$0: function() {
      H.Primitives_lazyAsJsDate(this);
    },
    $isDateTime: true,
    static: {"": "DateTime_MONDAY,DateTime_TUESDAY,DateTime_WEDNESDAY,DateTime_THURSDAY,DateTime_FRIDAY,DateTime_SATURDAY,DateTime_SUNDAY,DateTime_DAYS_PER_WEEK,DateTime_JANUARY,DateTime_FEBRUARY,DateTime_MARCH,DateTime_APRIL,DateTime_MAY,DateTime_JUNE,DateTime_JULY,DateTime_AUGUST,DateTime_SEPTEMBER,DateTime_OCTOBER,DateTime_NOVEMBER,DateTime_DECEMBER,DateTime_MONTHS_PER_YEAR,DateTime__MAX_MILLISECONDS_SINCE_EPOCH", DateTime$_now: function() {
        var t1 = new P.DateTime(Date.now(), false);
        t1.DateTime$_now$0();
        return t1;
      }}
  },
  DateTime_toString_fourDigits: {
    "": "Closure:17;",
    call$1: function(n) {
      var absN, sign;
      absN = Math.abs(n);
      sign = n < 0 ? "-" : "";
      if (absN >= 1000)
        return "" + n;
      if (absN >= 100)
        return sign + "0" + H.S(absN);
      if (absN >= 10)
        return sign + "00" + H.S(absN);
      return sign + "000" + H.S(absN);
    }
  },
  DateTime_toString_threeDigits: {
    "": "Closure:17;",
    call$1: function(n) {
      if (n >= 100)
        return "" + n;
      if (n >= 10)
        return "0" + n;
      return "00" + n;
    }
  },
  DateTime_toString_twoDigits: {
    "": "Closure:17;",
    call$1: function(n) {
      if (n >= 10)
        return "" + n;
      return "0" + n;
    }
  },
  Duration: {
    "": "Object;_duration<",
    $add: function(_, other) {
      return P.Duration$(0, 0, C.JSInt_methods.$add(this._duration, other.get$_duration()), 0, 0, 0);
    },
    $sub: function(_, other) {
      return P.Duration$(0, 0, this._duration - other.get$_duration(), 0, 0, 0);
    },
    $lt: function(_, other) {
      return C.JSInt_methods.$lt(this._duration, other.get$_duration());
    },
    $ge: function(_, other) {
      return C.JSInt_methods.$ge(this._duration, other.get$_duration());
    },
    $eq: function(_, other) {
      var t1;
      if (other == null)
        return false;
      t1 = J.getInterceptor(other);
      if (typeof other !== "object" || other === null || !t1.$isDuration)
        return false;
      return this._duration === other._duration;
    },
    get$hashCode: function(_) {
      return this._duration & 0x1FFFFFFF;
    },
    toString$0: function(_) {
      var t1, t2, twoDigitMinutes, twoDigitSeconds, sixDigitUs;
      t1 = new P.Duration_toString_twoDigits();
      t2 = this._duration;
      if (t2 < 0)
        return "-" + H.S(P.Duration$(0, 0, -t2, 0, 0, 0));
      twoDigitMinutes = t1.call$1(C.JSInt_methods.remainder$1(C.JSInt_methods._tdivFast$1(t2, 60000000), 60));
      twoDigitSeconds = t1.call$1(C.JSInt_methods.remainder$1(C.JSInt_methods._tdivFast$1(t2, 1000000), 60));
      sixDigitUs = new P.Duration_toString_sixDigits().call$1(C.JSInt_methods.remainder$1(t2, 1000000));
      return "" + C.JSInt_methods._tdivFast$1(t2, 3600000000) + ":" + H.S(twoDigitMinutes) + ":" + H.S(twoDigitSeconds) + "." + H.S(sixDigitUs);
    },
    $isDuration: true,
    static: {"": "Duration_MICROSECONDS_PER_MILLISECOND,Duration_MILLISECONDS_PER_SECOND,Duration_SECONDS_PER_MINUTE,Duration_MINUTES_PER_HOUR,Duration_HOURS_PER_DAY,Duration_MICROSECONDS_PER_SECOND,Duration_MICROSECONDS_PER_MINUTE,Duration_MICROSECONDS_PER_HOUR,Duration_MICROSECONDS_PER_DAY,Duration_MILLISECONDS_PER_MINUTE,Duration_MILLISECONDS_PER_HOUR,Duration_MILLISECONDS_PER_DAY,Duration_SECONDS_PER_HOUR,Duration_SECONDS_PER_DAY,Duration_MINUTES_PER_DAY,Duration_ZERO", Duration$: function(days, hours, microseconds, milliseconds, minutes, seconds) {
        return new P.Duration(days * 86400000000 + hours * 3600000000 + minutes * 60000000 + seconds * 1000000 + milliseconds * 1000 + microseconds);
      }}
  },
  Duration_toString_sixDigits: {
    "": "Closure:17;",
    call$1: function(n) {
      if (n >= 100000)
        return "" + n;
      if (n >= 10000)
        return "0" + n;
      if (n >= 1000)
        return "00" + n;
      if (n >= 100)
        return "000" + n;
      if (n > 10)
        return "0000" + n;
      return "00000" + n;
    }
  },
  Duration_toString_twoDigits: {
    "": "Closure:17;",
    call$1: function(n) {
      if (n >= 10)
        return "" + n;
      return "0" + n;
    }
  },
  Error: {
    "": "Object;",
    get$stackTrace: function() {
      return new H._StackTrace(this.$thrownJsError, null);
    },
    $isError: true
  },
  NullThrownError: {
    "": "Error;",
    toString$0: function(_) {
      return "Throw of null.";
    }
  },
  ArgumentError: {
    "": "Error;message",
    toString$0: function(_) {
      var t1 = this.message;
      if (t1 != null)
        return "Illegal argument(s): " + H.S(t1);
      return "Illegal argument(s)";
    },
    static: {ArgumentError$: function(message) {
        return new P.ArgumentError(message);
      }}
  },
  RangeError: {
    "": "ArgumentError;message",
    toString$0: function(_) {
      return "RangeError: " + H.S(this.message);
    },
    static: {RangeError$value: function(value) {
        return new P.RangeError("value " + H.S(value));
      }, RangeError$range: function(value, start, end) {
        return new P.RangeError("value " + H.S(value) + " not in range " + start + ".." + H.S(end));
      }}
  },
  UnsupportedError: {
    "": "Error;message",
    toString$0: function(_) {
      return "Unsupported operation: " + this.message;
    },
    static: {UnsupportedError$: function(message) {
        return new P.UnsupportedError(message);
      }}
  },
  UnimplementedError: {
    "": "Error;message",
    toString$0: function(_) {
      var t1 = this.message;
      return t1 != null ? "UnimplementedError: " + H.S(t1) : "UnimplementedError";
    },
    $isError: true,
    static: {UnimplementedError$: function(message) {
        return new P.UnimplementedError(message);
      }}
  },
  StateError: {
    "": "Error;message",
    toString$0: function(_) {
      return "Bad state: " + this.message;
    },
    static: {StateError$: function(message) {
        return new P.StateError(message);
      }}
  },
  ConcurrentModificationError: {
    "": "Error;modifiedObject",
    toString$0: function(_) {
      var t1 = this.modifiedObject;
      if (t1 == null)
        return "Concurrent modification during iteration.";
      return "Concurrent modification during iteration: " + H.S(P.Error_safeToString(t1)) + ".";
    },
    static: {ConcurrentModificationError$: function(modifiedObject) {
        return new P.ConcurrentModificationError(modifiedObject);
      }}
  },
  StackOverflowError: {
    "": "Object;",
    toString$0: function(_) {
      return "Stack Overflow";
    },
    get$stackTrace: function() {
      return;
    },
    $isError: true
  },
  CyclicInitializationError: {
    "": "Error;variableName",
    toString$0: function(_) {
      return "Reading static variable '" + this.variableName + "' during its initialization";
    },
    static: {CyclicInitializationError$: function(variableName) {
        return new P.CyclicInitializationError(variableName);
      }}
  },
  _ExceptionImplementation: {
    "": "Object;message",
    toString$0: function(_) {
      var t1 = this.message;
      if (t1 == null)
        return "Exception";
      return "Exception: " + H.S(t1);
    }
  },
  Expando: {
    "": "Object;name",
    toString$0: function(_) {
      return "Expando:" + H.S(this.name);
    },
    $index: function(_, object) {
      var values = H.Primitives_getProperty(object, "expando$values");
      return values == null ? null : H.Primitives_getProperty(values, this._getKey$0());
    },
    $indexSet: function(_, object, value) {
      var values = H.Primitives_getProperty(object, "expando$values");
      if (values == null) {
        values = new P.Object();
        H.Primitives_setProperty(object, "expando$values", values);
      }
      H.Primitives_setProperty(values, this._getKey$0(), value);
    },
    _getKey$0: function() {
      var key, t1;
      key = H.Primitives_getProperty(this, "expando$key");
      if (key == null) {
        t1 = $.Expando__keyCount;
        $.Expando__keyCount = t1 + 1;
        key = "expando$key$" + t1;
        H.Primitives_setProperty(this, "expando$key", key);
      }
      return key;
    },
    static: {"": "Expando__KEY_PROPERTY_NAME,Expando__EXPANDO_PROPERTY_NAME,Expando__keyCount"}
  },
  Iterator: {
    "": "Object;"
  },
  Null: {
    "": "Object;",
    toString$0: function(_) {
      return "null";
    }
  },
  Object: {
    "": ";",
    $eq: function(_, other) {
      return this === other;
    },
    get$hashCode: function(_) {
      return H.Primitives_objectHashCode(this);
    },
    toString$0: function(_) {
      return H.Primitives_objectToString(this);
    }
  },
  StackTrace: {
    "": "Object;"
  },
  StringBuffer: {
    "": "Object;_contents<",
    get$length: function(_) {
      return this._contents.length;
    },
    write$1: function(obj) {
      var str = typeof obj === "string" ? obj : H.S(obj);
      this._contents = this._contents + str;
    },
    writeAll$2: function(objects, separator) {
      var iterator, str;
      iterator = J.get$iterator$ax(objects);
      if (!iterator.moveNext$0())
        return;
      if (separator.length === 0)
        do {
          str = iterator.get$current();
          str = typeof str === "string" ? str : H.S(str);
          this._contents = this._contents + str;
        } while (iterator.moveNext$0());
      else {
        this.write$1(iterator.get$current());
        for (; iterator.moveNext$0();) {
          this._contents = this._contents + separator;
          str = iterator.get$current();
          str = typeof str === "string" ? str : H.S(str);
          this._contents = this._contents + str;
        }
      }
    },
    toString$0: function(_) {
      return this._contents;
    },
    StringBuffer$1: function($content) {
      this._contents = $content;
    },
    static: {StringBuffer$: function($content) {
        var t1 = new P.StringBuffer("");
        t1.StringBuffer$1($content);
        return t1;
      }}
  },
  Symbol: {
    "": "Object;"
  }
}],
["dart.dom.html", "dart:html", , W, {
  "": "",
  ImageElement_ImageElement: function(height, src, width) {
    var e = document.createElement("img", null);
    if (src != null)
      J.set$src$x(e, src);
    return e;
  },
  _JenkinsSmiHash_combine: function(hash, value) {
    hash = 536870911 & hash + value;
    hash = 536870911 & hash + ((524287 & hash) << 10 >>> 0);
    return hash ^ hash >>> 6;
  },
  _wrapZone: function(callback) {
    var t1 = $.Zone__current;
    if (t1 === C.C__RootZone)
      return callback;
    return t1.bindUnaryCallback$2$runGuarded(callback, true);
  },
  HtmlElement: {
    "": "Element;",
    "%": "HTMLAppletElement|HTMLAreaElement|HTMLBRElement|HTMLBaseElement|HTMLBaseFontElement|HTMLBodyElement|HTMLButtonElement|HTMLCanvasElement|HTMLContentElement|HTMLDListElement|HTMLDataListElement|HTMLDetailsElement|HTMLDialogElement|HTMLDirectoryElement|HTMLDivElement|HTMLFieldSetElement|HTMLFontElement|HTMLFrameElement|HTMLFrameSetElement|HTMLHRElement|HTMLHeadElement|HTMLHeadingElement|HTMLHtmlElement|HTMLKeygenElement|HTMLLIElement|HTMLLabelElement|HTMLLegendElement|HTMLLinkElement|HTMLMapElement|HTMLMarqueeElement|HTMLMenuElement|HTMLMetaElement|HTMLMeterElement|HTMLModElement|HTMLOListElement|HTMLObjectElement|HTMLOptGroupElement|HTMLOptionElement|HTMLOutputElement|HTMLParagraphElement|HTMLParamElement|HTMLPreElement|HTMLProgressElement|HTMLQuoteElement|HTMLShadowElement|HTMLSpanElement|HTMLStyleElement|HTMLTableCaptionElement|HTMLTableCellElement|HTMLTableColElement|HTMLTableDataCellElement|HTMLTableElement|HTMLTableHeaderCellElement|HTMLTableRowElement|HTMLTableSectionElement|HTMLTemplateElement|HTMLTextAreaElement|HTMLTitleElement|HTMLUListElement|HTMLUnknownElement;HTMLElement"
  },
  AnchorElement: {
    "": "HtmlElement;",
    toString$0: function(receiver) {
      return receiver.toString();
    },
    "%": "HTMLAnchorElement"
  },
  CharacterData: {
    "": "Node;length=",
    "%": "CDATASection|CharacterData|Comment|ProcessingInstruction|Text"
  },
  CssStyleDeclaration: {
    "": "Interceptor_CssStyleDeclarationBase;length=",
    setProperty$3: function(receiver, propertyName, value, priority) {
      var exception;
      try {
        if (priority == null)
          priority = "";
        receiver.setProperty(propertyName, value, priority);
        if (!!receiver.setAttribute)
          receiver.setAttribute(propertyName, value);
      } catch (exception) {
        H.unwrapException(exception);
      }

    },
    "%": "CSS2Properties|CSSStyleDeclaration|MSStyleCSSProperties"
  },
  DomException: {
    "": "Interceptor;",
    toString$0: function(receiver) {
      return receiver.toString();
    },
    "%": "DOMException"
  },
  Element: {
    "": "Node;",
    toString$0: function(receiver) {
      return receiver.localName;
    },
    "%": ";Element"
  },
  EmbedElement: {
    "": "HtmlElement;src}",
    "%": "HTMLEmbedElement"
  },
  ErrorEvent: {
    "": "Event;error=",
    "%": "ErrorEvent"
  },
  Event: {
    "": "Interceptor;",
    "%": "AudioProcessingEvent|AutocompleteErrorEvent|BeforeLoadEvent|BeforeUnloadEvent|CSSFontFaceLoadEvent|CloseEvent|CompositionEvent|CustomEvent|DeviceMotionEvent|DeviceOrientationEvent|DragEvent|FocusEvent|HashChangeEvent|IDBVersionChangeEvent|KeyboardEvent|MIDIConnectionEvent|MIDIMessageEvent|MSPointerEvent|MediaKeyEvent|MediaKeyMessageEvent|MediaKeyNeededEvent|MediaStreamEvent|MediaStreamTrackEvent|MessageEvent|MouseEvent|MouseScrollEvent|MouseWheelEvent|MutationEvent|OfflineAudioCompletionEvent|OverflowEvent|PageTransitionEvent|PointerEvent|PopStateEvent|ProgressEvent|RTCDTMFToneChangeEvent|RTCDataChannelEvent|RTCIceCandidateEvent|ResourceProgressEvent|SVGZoomEvent|SecurityPolicyViolationEvent|SpeechInputEvent|SpeechRecognitionEvent|SpeechSynthesisEvent|StorageEvent|TextEvent|TouchEvent|TrackEvent|TransitionEvent|UIEvent|WebGLContextEvent|WebKitAnimationEvent|WebKitTransitionEvent|WheelEvent|XMLHttpRequestProgressEvent;Event"
  },
  EventTarget: {
    "": "Interceptor;",
    "%": "MediaStream;EventTarget"
  },
  FormElement: {
    "": "HtmlElement;length=",
    "%": "HTMLFormElement"
  },
  IFrameElement: {
    "": "HtmlElement;src}",
    "%": "HTMLIFrameElement"
  },
  ImageElement: {
    "": "HtmlElement;src}",
    "%": "HTMLImageElement"
  },
  InputElement: {
    "": "HtmlElement;src}",
    "%": "HTMLInputElement"
  },
  MediaElement: {
    "": "HtmlElement;error=,src}",
    "%": "HTMLAudioElement|HTMLMediaElement|HTMLVideoElement"
  },
  Node: {
    "": "EventTarget;",
    toString$0: function(receiver) {
      var t1 = receiver.nodeValue;
      return t1 == null ? J.Interceptor.prototype.toString$0.call(this, receiver) : t1;
    },
    "%": "Attr|Document|DocumentFragment|DocumentType|Entity|HTMLDocument|Notation|SVGDocument|ShadowRoot;Node"
  },
  NodeList: {
    "": "Interceptor_ListMixin_ImmutableListMixin;",
    get$length: function(receiver) {
      return receiver.length;
    },
    $index: function(receiver, index) {
      var t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw H.wrapException(P.RangeError$range(index, 0, t1));
      return receiver[index];
    },
    $indexSet: function(receiver, index, value) {
      throw H.wrapException(P.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1: function(receiver, index) {
      if (index < 0 || index >= receiver.length)
        return H.ioore(receiver, index);
      return receiver[index];
    },
    $isList: true,
    $asList: function() {
      return [W.Node];
    },
    $isJavaScriptIndexingBehavior: true,
    "%": "NodeList|RadioNodeList"
  },
  ScriptElement: {
    "": "HtmlElement;src}",
    "%": "HTMLScriptElement"
  },
  SelectElement: {
    "": "HtmlElement;length=",
    "%": "HTMLSelectElement"
  },
  SourceElement: {
    "": "HtmlElement;src}",
    "%": "HTMLSourceElement"
  },
  SpeechRecognitionError: {
    "": "Event;error=",
    "%": "SpeechRecognitionError"
  },
  TrackElement: {
    "": "HtmlElement;src}",
    "%": "HTMLTrackElement"
  },
  Window: {
    "": "EventTarget;",
    _requestAnimationFrame$1: function(receiver, callback) {
      return receiver.requestAnimationFrame(H.convertDartClosureToJS(callback, 1));
    },
    _ensureRequestAnimationFrame$0: function(receiver) {
      if (!!(receiver.requestAnimationFrame && receiver.cancelAnimationFrame))
        return;
        (function($this) {
   var vendors = ['ms', 'moz', 'webkit', 'o'];
   for (var i = 0; i < vendors.length && !$this.requestAnimationFrame; ++i) {
     $this.requestAnimationFrame = $this[vendors[i] + 'RequestAnimationFrame'];
     $this.cancelAnimationFrame =
         $this[vendors[i]+'CancelAnimationFrame'] ||
         $this[vendors[i]+'CancelRequestAnimationFrame'];
   }
   if ($this.requestAnimationFrame && $this.cancelAnimationFrame) return;
   $this.requestAnimationFrame = function(callback) {
      return window.setTimeout(function() {
        callback(Date.now());
      }, 16 /* 16ms ~= 60fps */);
   };
   $this.cancelAnimationFrame = function(id) { clearTimeout(id); }
  })(receiver);
    },
    toString$0: function(receiver) {
      return receiver.toString();
    },
    "%": "DOMWindow|Window"
  },
  _ClientRect: {
    "": "Interceptor;height=,left=,top=,width=",
    toString$0: function(receiver) {
      return "Rectangle (" + H.S(receiver.left) + ", " + H.S(receiver.top) + ") " + H.S(receiver.width) + " x " + H.S(receiver.height);
    },
    $eq: function(receiver, other) {
      var t1, t2, t3;
      if (other == null)
        return false;
      t1 = J.getInterceptor$x(other);
      if (typeof other !== "object" || other === null || !t1.$isRectangle)
        return false;
      t2 = receiver.left;
      t3 = t1.get$left(other);
      if (t2 == null ? t3 == null : t2 === t3) {
        t2 = receiver.top;
        t3 = t1.get$top(other);
        if (t2 == null ? t3 == null : t2 === t3) {
          t2 = receiver.width;
          t3 = t1.get$width(other);
          if (t2 == null ? t3 == null : t2 === t3) {
            t2 = receiver.height;
            t1 = t1.get$height(other);
            t1 = t2 == null ? t1 == null : t2 === t1;
          } else
            t1 = false;
        } else
          t1 = false;
      } else
        t1 = false;
      return t1;
    },
    get$hashCode: function(receiver) {
      var t1, t2, t3, t4, hash;
      t1 = J.get$hashCode$(receiver.left);
      t2 = J.get$hashCode$(receiver.top);
      t3 = J.get$hashCode$(receiver.width);
      t4 = J.get$hashCode$(receiver.height);
      t4 = W._JenkinsSmiHash_combine(W._JenkinsSmiHash_combine(W._JenkinsSmiHash_combine(W._JenkinsSmiHash_combine(0, t1), t2), t3), t4);
      hash = 536870911 & t4 + ((67108863 & t4) << 3 >>> 0);
      hash ^= hash >>> 11;
      return 536870911 & hash + ((16383 & hash) << 15 >>> 0);
    },
    $isRectangle: true,
    $asRectangle: function() {
      return [null];
    },
    "%": "ClientRect|DOMRect"
  },
  Interceptor_CssStyleDeclarationBase: {
    "": "Interceptor+CssStyleDeclarationBase;"
  },
  CssStyleDeclarationBase: {
    "": "Object;",
    set$bottom: function(receiver, value) {
      this.setProperty$3(receiver, "bottom", value, "");
    },
    set$left: function(receiver, value) {
      this.setProperty$3(receiver, "left", value, "");
    },
    set$position: function(receiver, value) {
      this.setProperty$3(receiver, "position", value, "");
    },
    set$right: function(receiver, value) {
      this.setProperty$3(receiver, "right", value, "");
    },
    set$textAlign: function(receiver, value) {
      this.setProperty$3(receiver, "text-align", value, "");
    },
    set$top: function(receiver, value) {
      this.setProperty$3(receiver, "top", value, "");
    }
  },
  Interceptor_ListMixin: {
    "": "Interceptor+ListMixin;",
    $isList: true,
    $asList: function() {
      return [W.Node];
    }
  },
  Interceptor_ListMixin_ImmutableListMixin: {
    "": "Interceptor_ListMixin+ImmutableListMixin;",
    $isList: true,
    $asList: function() {
      return [W.Node];
    }
  },
  ImmutableListMixin: {
    "": "Object;",
    get$iterator: function(receiver) {
      return new W.FixedSizeListIterator(receiver, this.get$length(receiver), -1, null);
    },
    $isList: true,
    $asList: null
  },
  FixedSizeListIterator: {
    "": "Object;_array,_length,_position,_current",
    moveNext$0: function() {
      var nextPosition, t1;
      nextPosition = this._position + 1;
      t1 = this._length;
      if (nextPosition < t1) {
        this._current = J.$index$asx(this._array, nextPosition);
        this._position = nextPosition;
        return true;
      }
      this._current = null;
      this._position = t1;
      return false;
    },
    get$current: function() {
      return this._current;
    }
  }
}],
["dart.dom.svg", "dart:svg", , P, {
  "": "",
  FEBlendElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEBlendElement"
  },
  FEColorMatrixElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEColorMatrixElement"
  },
  FEComponentTransferElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEComponentTransferElement"
  },
  FECompositeElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFECompositeElement"
  },
  FEConvolveMatrixElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEConvolveMatrixElement"
  },
  FEDiffuseLightingElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEDiffuseLightingElement"
  },
  FEDisplacementMapElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEDisplacementMapElement"
  },
  FEFloodElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEFloodElement"
  },
  FEGaussianBlurElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEGaussianBlurElement"
  },
  FEImageElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEImageElement"
  },
  FEMergeElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEMergeElement"
  },
  FEMorphologyElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEMorphologyElement"
  },
  FEOffsetElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEOffsetElement"
  },
  FEPointLightElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFEPointLightElement"
  },
  FESpecularLightingElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFESpecularLightingElement"
  },
  FESpotLightElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFESpotLightElement"
  },
  FETileElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFETileElement"
  },
  FETurbulenceElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFETurbulenceElement"
  },
  FilterElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGFilterElement"
  },
  ForeignObjectElement: {
    "": "GraphicsElement;x=,y=",
    "%": "SVGForeignObjectElement"
  },
  GraphicsElement: {
    "": "SvgElement;",
    "%": "SVGAElement|SVGCircleElement|SVGClipPathElement|SVGDefsElement|SVGEllipseElement|SVGGElement|SVGLineElement|SVGPathElement|SVGPolygonElement|SVGPolylineElement|SVGSwitchElement;SVGGraphicsElement"
  },
  ImageElement0: {
    "": "GraphicsElement;x=,y=",
    "%": "SVGImageElement"
  },
  MaskElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGMaskElement"
  },
  PatternElement: {
    "": "SvgElement;x=,y=",
    "%": "SVGPatternElement"
  },
  RectElement: {
    "": "GraphicsElement;x=,y=",
    "%": "SVGRectElement"
  },
  SvgElement: {
    "": "Element;",
    "%": "SVGAltGlyphDefElement|SVGAltGlyphItemElement|SVGAnimateColorElement|SVGAnimateElement|SVGAnimateMotionElement|SVGAnimateTransformElement|SVGAnimationElement|SVGComponentTransferFunctionElement|SVGCursorElement|SVGDescElement|SVGFEDistantLightElement|SVGFEDropShadowElement|SVGFEFuncAElement|SVGFEFuncBElement|SVGFEFuncGElement|SVGFEFuncRElement|SVGFEMergeNodeElement|SVGFontElement|SVGFontFaceElement|SVGFontFaceFormatElement|SVGFontFaceNameElement|SVGFontFaceSrcElement|SVGFontFaceUriElement|SVGGlyphElement|SVGGlyphRefElement|SVGGradientElement|SVGHKernElement|SVGLinearGradientElement|SVGMPathElement|SVGMarkerElement|SVGMetadataElement|SVGMissingGlyphElement|SVGRadialGradientElement|SVGScriptElement|SVGSetElement|SVGStopElement|SVGStyleElement|SVGSymbolElement|SVGTitleElement|SVGVKernElement|SVGViewElement;SVGElement"
  },
  SvgSvgElement: {
    "": "GraphicsElement;x=,y=",
    "%": "SVGSVGElement"
  },
  TextContentElement: {
    "": "GraphicsElement;",
    "%": "SVGTextPathElement;SVGTextContentElement"
  },
  TextPositioningElement: {
    "": "TextContentElement;x=,y=",
    "%": "SVGAltGlyphElement|SVGTSpanElement|SVGTextElement|SVGTextPositioningElement"
  },
  UseElement: {
    "": "GraphicsElement;x=,y=",
    "%": "SVGUseElement"
  }
}],
["dart.math", "dart:math", , P, {
  "": "",
  _JenkinsSmiHash_combine0: function(hash, value) {
    hash = 536870911 & C.JSInt_methods.$add(hash, value);
    hash = 536870911 & hash + ((524287 & hash) << 10 >>> 0);
    return hash ^ hash >>> 6;
  },
  min: function(a, b) {
    var t1;
    if (a > b)
      return b;
    if (a < b)
      return a;
    if (typeof a === "number")
      if (a === 0)
        return (a + b) * a * b;
    if (a === 0)
      t1 = b === 0 ? 1 / b < 0 : b < 0;
    else
      t1 = false;
    if (t1 || isNaN(b))
      return b;
    return a;
  },
  _JSRandom: {
    "": "Object;",
    nextDouble$0: function() {
      return Math.random();
    }
  }
}],
["dart.typed_data", "dart:typed_data", , P, {
  "": "",
  TypedData: {
    "": "Interceptor;",
    _invalidIndex$2: function(receiver, index, $length) {
      var t1 = J.getInterceptor$n(index);
      if (t1.$lt(index, 0) || t1.$ge(index, $length))
        throw H.wrapException(P.RangeError$range(index, 0, $length));
      else
        throw H.wrapException(P.ArgumentError$("Invalid list index " + H.S(index)));
    },
    "%": ";ArrayBufferView;_NativeTypedArray|_NativeTypedArray_ListMixin|_NativeTypedArray_ListMixin_FixedLengthListMixin|_NativeTypedArrayOfInt"
  },
  Uint8List: {
    "": "_NativeTypedArrayOfInt;",
    get$length: function(receiver) {
      return receiver.length;
    },
    $index: function(receiver, index) {
      var t1, t2;
      t1 = receiver.length;
      if (!(index >>> 0 != index)) {
        if (typeof index !== "number")
          return index.$ge();
        t2 = index >= t1;
      } else
        t2 = true;
      if (t2)
        this._invalidIndex$2(receiver, index, t1);
      return receiver[index];
    },
    $indexSet: function(receiver, index, value) {
      var t1 = receiver.length;
      if (index >>> 0 != index || J.$ge$n(index, t1))
        this._invalidIndex$2(receiver, index, t1);
      receiver[index] = value;
    },
    "%": ";Uint8Array"
  },
  _NativeTypedArray: {
    "": "TypedData;",
    get$length: function(receiver) {
      return receiver.length;
    },
    $isJavaScriptIndexingBehavior: true
  },
  _NativeTypedArrayOfInt: {
    "": "_NativeTypedArray_ListMixin_FixedLengthListMixin;",
    $isList: true,
    $asList: function() {
      return [J.JSInt];
    }
  },
  _NativeTypedArray_ListMixin: {
    "": "_NativeTypedArray+ListMixin;",
    $isList: true,
    $asList: function() {
      return [J.JSInt];
    }
  },
  _NativeTypedArray_ListMixin_FixedLengthListMixin: {
    "": "_NativeTypedArray_ListMixin+FixedLengthListMixin;"
  }
}],
["dart2js._js_primitives", "dart:_js_primitives", , H, {
  "": "",
  printString: function(string) {
    if (typeof dartPrint == "function") {
      dartPrint(string);
      return;
    }
    if (typeof console == "object" && typeof console.log == "function") {
      console.log(string);
      return;
    }
    if (typeof window == "object")
      return;
    if (typeof print == "function") {
      print(string);
      return;
    }
    throw "Unable to print message: " + String(string);
  }
}],
]);
Isolate.$finishClasses($$, $, null);
$$ = null;

// Runtime type support
W.Node.$isNode = true;
W.Node.$isObject = true;
J.JSInt.$isint = true;
J.JSInt.$isnum = true;
J.JSInt.$isObject = true;
J.JSNumber.$isnum = true;
J.JSNumber.$isObject = true;
J.JSString.$isString = true;
J.JSString.$isObject = true;
P.Duration.$isDuration = true;
P.Duration.$isObject = true;
Q.ClockNumber.$isObject = true;
Q.Ball.$isObject = true;
J.JSArray.$isObject = true;
W.ImageElement.$isNode = true;
W.ImageElement.$isObject = true;
H.RawReceivePortImpl.$isObject = true;
H._IsolateEvent.$isObject = true;
H._IsolateContext.$isObject = true;
P.Symbol.$isSymbol = true;
P.Symbol.$isObject = true;
P.StackTrace.$isStackTrace = true;
P.StackTrace.$isObject = true;
P.Object.$isObject = true;
J.JSBool.$isbool = true;
J.JSBool.$isObject = true;
P._EventSink.$is_EventSink = true;
P._EventSink.$isObject = true;
P.Future.$isFuture = true;
P.Future.$isObject = true;
J.JSDouble.$isdouble = true;
J.JSDouble.$isnum = true;
J.JSDouble.$isObject = true;
P._DelayedEvent.$is_DelayedEvent = true;
P._DelayedEvent.$isObject = true;
P.DateTime.$isDateTime = true;
P.DateTime.$isObject = true;
P.StreamSubscription.$isStreamSubscription = true;
P.StreamSubscription.$isObject = true;
$.$signature_args2 = {func: "args2", args: [null, null]};
$.$signature_args1 = {func: "args1", args: [null]};
// getInterceptor methods
J.getInterceptor = function(receiver) {
  if (typeof receiver == "number") {
    if (Math.floor(receiver) == receiver)
      return J.JSInt.prototype;
    return J.JSDouble.prototype;
  }
  if (typeof receiver == "string")
    return J.JSString.prototype;
  if (receiver == null)
    return J.JSNull.prototype;
  if (typeof receiver == "boolean")
    return J.JSBool.prototype;
  if (receiver.constructor == Array)
    return J.JSArray.prototype;
  if (typeof receiver != "object")
    return receiver;
  if (receiver instanceof P.Object)
    return receiver;
  return J.getNativeInterceptor(receiver);
};
J.getInterceptor$asx = function(receiver) {
  if (typeof receiver == "string")
    return J.JSString.prototype;
  if (receiver == null)
    return receiver;
  if (receiver.constructor == Array)
    return J.JSArray.prototype;
  if (typeof receiver != "object")
    return receiver;
  if (receiver instanceof P.Object)
    return receiver;
  return J.getNativeInterceptor(receiver);
};
J.getInterceptor$ax = function(receiver) {
  if (receiver == null)
    return receiver;
  if (receiver.constructor == Array)
    return J.JSArray.prototype;
  if (typeof receiver != "object")
    return receiver;
  if (receiver instanceof P.Object)
    return receiver;
  return J.getNativeInterceptor(receiver);
};
J.getInterceptor$n = function(receiver) {
  if (typeof receiver == "number")
    return J.JSNumber.prototype;
  if (receiver == null)
    return receiver;
  if (!(receiver instanceof P.Object))
    return J.UnknownJavaScriptObject.prototype;
  return receiver;
};
J.getInterceptor$ns = function(receiver) {
  if (typeof receiver == "number")
    return J.JSNumber.prototype;
  if (typeof receiver == "string")
    return J.JSString.prototype;
  if (receiver == null)
    return receiver;
  if (!(receiver instanceof P.Object))
    return J.UnknownJavaScriptObject.prototype;
  return receiver;
};
J.getInterceptor$s = function(receiver) {
  if (typeof receiver == "string")
    return J.JSString.prototype;
  if (receiver == null)
    return receiver;
  if (!(receiver instanceof P.Object))
    return J.UnknownJavaScriptObject.prototype;
  return receiver;
};
J.getInterceptor$x = function(receiver) {
  if (receiver == null)
    return receiver;
  if (typeof receiver != "object")
    return receiver;
  if (receiver instanceof P.Object)
    return receiver;
  return J.getNativeInterceptor(receiver);
};
J.$add$ns = function(receiver, a0) {
  if (typeof receiver == "number" && typeof a0 == "number")
    return receiver + a0;
  return J.getInterceptor$ns(receiver).$add(receiver, a0);
};
J.$eq = function(receiver, a0) {
  if (receiver == null)
    return a0 == null;
  if (typeof receiver != "object")
    return a0 != null && receiver === a0;
  return J.getInterceptor(receiver).$eq(receiver, a0);
};
J.$ge$n = function(receiver, a0) {
  if (typeof receiver == "number" && typeof a0 == "number")
    return receiver >= a0;
  return J.getInterceptor$n(receiver).$ge(receiver, a0);
};
J.$index$asx = function(receiver, a0) {
  if (receiver.constructor == Array || typeof receiver == "string" || H.isJsIndexable(receiver, receiver[init.dispatchPropertyName]))
    if (a0 >>> 0 === a0 && a0 < receiver.length)
      return receiver[a0];
  return J.getInterceptor$asx(receiver).$index(receiver, a0);
};
J.$indexSet$ax = function(receiver, a0, a1) {
  if ((receiver.constructor == Array || H.isJsIndexable(receiver, receiver[init.dispatchPropertyName])) && !receiver.immutable$list && a0 >>> 0 === a0 && a0 < receiver.length)
    return receiver[a0] = a1;
  return J.getInterceptor$ax(receiver).$indexSet(receiver, a0, a1);
};
J.forEach$1$ax = function(receiver, a0) {
  return J.getInterceptor$ax(receiver).forEach$1(receiver, a0);
};
J.get$error$x = function(receiver) {
  return J.getInterceptor$x(receiver).get$error(receiver);
};
J.get$hashCode$ = function(receiver) {
  return J.getInterceptor(receiver).get$hashCode(receiver);
};
J.get$iterator$ax = function(receiver) {
  return J.getInterceptor$ax(receiver).get$iterator(receiver);
};
J.get$length$asx = function(receiver) {
  return J.getInterceptor$asx(receiver).get$length(receiver);
};
J.roundToDouble$0$n = function(receiver) {
  return J.getInterceptor$n(receiver).roundToDouble$0(receiver);
};
J.set$bottom$x = function(receiver, value) {
  return J.getInterceptor$x(receiver).set$bottom(receiver, value);
};
J.set$left$x = function(receiver, value) {
  return J.getInterceptor$x(receiver).set$left(receiver, value);
};
J.set$position$x = function(receiver, value) {
  return J.getInterceptor$x(receiver).set$position(receiver, value);
};
J.set$right$x = function(receiver, value) {
  return J.getInterceptor$x(receiver).set$right(receiver, value);
};
J.set$src$x = function(receiver, value) {
  return J.getInterceptor$x(receiver).set$src(receiver, value);
};
J.set$textAlign$x = function(receiver, value) {
  return J.getInterceptor$x(receiver).set$textAlign(receiver, value);
};
J.set$top$x = function(receiver, value) {
  return J.getInterceptor$x(receiver).set$top(receiver, value);
};
J.toString$0 = function(receiver) {
  return J.getInterceptor(receiver).toString$0(receiver);
};
C.C_DynamicRuntimeType = new H.DynamicRuntimeType();
C.C__DelayedDone = new P._DelayedDone();
C.C__JSRandom = new P._JSRandom();
C.C__RootZone = new P._RootZone();
C.Duration_0 = new P.Duration(0);
C.JSArray_methods = J.JSArray.prototype;
C.JSInt_methods = J.JSInt.prototype;
C.JSNumber_methods = J.JSNumber.prototype;
C.JSString_methods = J.JSString.prototype;
C.JS_CONST_0 = function(hooks) {
  if (typeof dartExperimentalFixupGetTag != "function") return hooks;
  hooks.getTag = dartExperimentalFixupGetTag(hooks.getTag);
};
C.JS_CONST_Fs4 = function(hooks) { return hooks; }
;
C.JS_CONST_IX5 = function getTagFallback(o) {
  var constructor = o.constructor;
  if (typeof constructor == "function") {
    var name = constructor.name;
    if (typeof name == "string"
        && name !== ""
        && name !== "Object"
        && name !== "Function.prototype") {
      return name;
    }
  }
  var s = Object.prototype.toString.call(o);
  return s.substring(8, s.length - 1);
};
C.JS_CONST_QJm = function(getTagFallback) {
  return function(hooks) {
    if (typeof navigator != "object") return hooks;
    var ua = navigator.userAgent;
    if (ua.indexOf("DumpRenderTree") >= 0) return hooks;
    if (ua.indexOf("Chrome") >= 0) {
      function confirm(p) {
        return typeof window == "object" && window[p] && window[p].name == p;
      }
      if (confirm("Window") && confirm("HTMLElement")) return hooks;
    }
    hooks.getTag = getTagFallback;
  };
};
C.JS_CONST_U4w = function(hooks) {
  var userAgent = typeof navigator == "object" ? navigator.userAgent : "";
  if (userAgent.indexOf("Firefox") == -1) return hooks;
  var getTag = hooks.getTag;
  var quickMap = {
    "BeforeUnloadEvent": "Event",
    "DataTransfer": "Clipboard",
    "GeoGeolocation": "Geolocation",
    "WorkerMessageEvent": "MessageEvent",
    "XMLDocument": "!Document"};
  function getTagFirefox(o) {
    var tag = getTag(o);
    return quickMap[tag] || tag;
  }
  hooks.getTag = getTagFirefox;
};
C.JS_CONST_aQP = function() {
  function typeNameInChrome(o) {
    var name = o.constructor.name;
    if (name) return name;
    var s = Object.prototype.toString.call(o);
    return s.substring(8, s.length - 1);
  }
  function getUnknownTag(object, tag) {
    if (/^HTML[A-Z].*Element$/.test(tag)) {
      var name = Object.prototype.toString.call(object);
      if (name == "[object Object]") return null;
      return "HTMLElement";
    }
  }
  function getUnknownTagGenericBrowser(object, tag) {
    if (object instanceof HTMLElement) return "HTMLElement";
    return getUnknownTag(object, tag);
  }
  function prototypeForTag(tag) {
    if (typeof window == "undefined") return null;
    if (typeof window[tag] == "undefined") return null;
    var constructor = window[tag];
    if (typeof constructor != "function") return null;
    return constructor.prototype;
  }
  function discriminator(tag) { return null; }
  var isBrowser = typeof navigator == "object";
  return {
    getTag: typeNameInChrome,
    getUnknownTag: isBrowser ? getUnknownTagGenericBrowser : getUnknownTag,
    prototypeForTag: prototypeForTag,
    discriminator: discriminator };
};
C.JS_CONST_gkc = function(hooks) {
  var userAgent = typeof navigator == "object" ? navigator.userAgent : "";
  if (userAgent.indexOf("Trident/") == -1) return hooks;
  var getTag = hooks.getTag;
  var quickMap = {
    "BeforeUnloadEvent": "Event",
    "DataTransfer": "Clipboard",
    "HTMLDDElement": "HTMLElement",
    "HTMLDTElement": "HTMLElement",
    "HTMLPhraseElement": "HTMLElement",
    "Position": "Geoposition"
  };
  function getTagIE(o) {
    var tag = getTag(o);
    var newTag = quickMap[tag];
    if (newTag) return newTag;
    if (tag == "Object") {
      if (window.DataView && (o instanceof window.DataView)) return "DataView";
    }
    return tag;
  }
  function prototypeForTagIE(tag) {
    var constructor = window[tag];
    if (constructor == null) return null;
    return constructor.prototype;
  }
  hooks.getTag = getTagIE;
  hooks.prototypeForTag = prototypeForTagIE;
};
C.JS_CONST_rr7 = function(hooks) {
  var getTag = hooks.getTag;
  var prototypeForTag = hooks.prototypeForTag;
  function getTagFixed(o) {
    var tag = getTag(o);
    if (tag == "Document") {
      if (!!o.xmlVersion) return "!Document";
      return "!HTMLDocument";
    }
    return tag;
  }
  function prototypeForTagFixed(tag) {
    if (tag == "Document") return null;
    return prototypeForTag(tag);
  }
  hooks.getTag = getTagFixed;
  hooks.prototypeForTag = prototypeForTagFixed;
};
Isolate.makeConstantList = function(list) {
  list.immutable$list = init;
  list.fixed$length = init;
  return list;
};
C.List_8eb = Isolate.makeConstantList(["images/ball-d9d9d9.png", "images/ball-009a49.png", "images/ball-13acfa.png", "images/ball-265897.png", "images/ball-b6b4b5.png", "images/ball-c0000b.png", "images/ball-c9c9c9.png"]);
C.List_1_1_1_1 = Isolate.makeConstantList([1, 1, 1, 1]);
C.List_1_0_0_1 = Isolate.makeConstantList([1, 0, 0, 1]);
C.List_Xdq = Isolate.makeConstantList([C.List_1_1_1_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_1_1_1]);
C.List_0_0_0_1 = Isolate.makeConstantList([0, 0, 0, 1]);
C.List_Xdq0 = Isolate.makeConstantList([C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1]);
C.List_1_0_0_0 = Isolate.makeConstantList([1, 0, 0, 0]);
C.List_Xdq1 = Isolate.makeConstantList([C.List_1_1_1_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_1_1_1_1, C.List_1_0_0_0, C.List_1_0_0_0, C.List_1_1_1_1]);
C.List_Xdq2 = Isolate.makeConstantList([C.List_1_1_1_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_1_1_1_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_1_1_1_1]);
C.List_Xdq3 = Isolate.makeConstantList([C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_1_1_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1]);
C.List_Xdq4 = Isolate.makeConstantList([C.List_1_1_1_1, C.List_1_0_0_0, C.List_1_0_0_0, C.List_1_1_1_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_1_1_1_1]);
C.List_Xdq5 = Isolate.makeConstantList([C.List_1_1_1_1, C.List_1_0_0_0, C.List_1_0_0_0, C.List_1_1_1_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_1_1_1]);
C.List_Xdq6 = Isolate.makeConstantList([C.List_1_1_1_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_0_0_0_1]);
C.List_Xdq7 = Isolate.makeConstantList([C.List_1_1_1_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_1_1_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_1_1_1]);
C.List_Xdq8 = Isolate.makeConstantList([C.List_1_1_1_1, C.List_1_0_0_1, C.List_1_0_0_1, C.List_1_1_1_1, C.List_0_0_0_1, C.List_0_0_0_1, C.List_1_1_1_1]);
C.List_e3I = Isolate.makeConstantList([C.List_Xdq, C.List_Xdq0, C.List_Xdq1, C.List_Xdq2, C.List_Xdq3, C.List_Xdq4, C.List_Xdq5, C.List_Xdq6, C.List_Xdq7, C.List_Xdq8]);
C.Type_6Vn = H.createRuntimeType('_NativeTypedArray');
C.Type_Hp8 = H.createRuntimeType('_NativeTypedArrayOfInt');
C.UnknownJavaScriptObject_methods = J.UnknownJavaScriptObject.prototype;
C.Window_methods = W.Window.prototype;
$.controlPort = null;
$.RawReceivePortImpl__nextFreeId = 1;
$.Primitives_mirrorFunctionCacheName = "$cachedFunction";
$.Primitives_mirrorInvokeCacheName = "$cachedInvocation";
$.Closure_functionCounter = 0;
$.BoundClosure_selfFieldNameCache = null;
$.BoundClosure_receiverFieldNameCache = null;
$.RuntimeFunctionType_inAssert = false;
$.getTagFunction = null;
$.alternateTagFunction = null;
$.prototypeForTagFunction = null;
$.dispatchRecordsForInstanceTags = null;
$.interceptorsForUncacheableTags = null;
$.initNativeDispatchFlag = null;
$.Ball_random = null;
$.fpsAverage = null;
$.printToZone = null;
$._callbacksAreEnqueued = false;
$.Zone__current = C.C__RootZone;
$.Expando__keyCount = 0;
$.Device__isOpera = null;
$.Device__isWebKit = null;
Isolate.$lazy($, "globalThis", "globalThis", "get$globalThis", function() {
  return function() { return this; }();
});
Isolate.$lazy($, "globalWindow", "globalWindow", "get$globalWindow", function() {
  return $.get$globalThis().window;
});
Isolate.$lazy($, "globalWorker", "globalWorker", "get$globalWorker", function() {
  return $.get$globalThis().Worker;
});
Isolate.$lazy($, "globalPostMessageDefined", "globalPostMessageDefined", "get$globalPostMessageDefined", function() {
  return $.get$globalThis().postMessage !== void 0;
});
Isolate.$lazy($, "thisScript", "IsolateNatives_thisScript", "get$IsolateNatives_thisScript", function() {
  return H.IsolateNatives_computeThisScript();
});
Isolate.$lazy($, "workerIds", "IsolateNatives_workerIds", "get$IsolateNatives_workerIds", function() {
  return new P.Expando(null);
});
Isolate.$lazy($, "noSuchMethodPattern", "TypeErrorDecoder_noSuchMethodPattern", "get$TypeErrorDecoder_noSuchMethodPattern", function() {
  return H.TypeErrorDecoder_extractPattern(H.TypeErrorDecoder_provokeCallErrorOn({ toString: function() { return "$receiver$"; } }));
});
Isolate.$lazy($, "notClosurePattern", "TypeErrorDecoder_notClosurePattern", "get$TypeErrorDecoder_notClosurePattern", function() {
  return H.TypeErrorDecoder_extractPattern(H.TypeErrorDecoder_provokeCallErrorOn({ $method$: null, toString: function() { return "$receiver$"; } }));
});
Isolate.$lazy($, "nullCallPattern", "TypeErrorDecoder_nullCallPattern", "get$TypeErrorDecoder_nullCallPattern", function() {
  return H.TypeErrorDecoder_extractPattern(H.TypeErrorDecoder_provokeCallErrorOn(null));
});
Isolate.$lazy($, "nullLiteralCallPattern", "TypeErrorDecoder_nullLiteralCallPattern", "get$TypeErrorDecoder_nullLiteralCallPattern", function() {
  return H.TypeErrorDecoder_extractPattern(function() {
  var $argumentsExpr$ = '$arguments$'
  try {
    null.$method$($argumentsExpr$);
  } catch (e) {
    return e.message;
  }
}());
});
Isolate.$lazy($, "undefinedCallPattern", "TypeErrorDecoder_undefinedCallPattern", "get$TypeErrorDecoder_undefinedCallPattern", function() {
  return H.TypeErrorDecoder_extractPattern(H.TypeErrorDecoder_provokeCallErrorOn(void 0));
});
Isolate.$lazy($, "undefinedLiteralCallPattern", "TypeErrorDecoder_undefinedLiteralCallPattern", "get$TypeErrorDecoder_undefinedLiteralCallPattern", function() {
  return H.TypeErrorDecoder_extractPattern(function() {
  var $argumentsExpr$ = '$arguments$'
  try {
    (void 0).$method$($argumentsExpr$);
  } catch (e) {
    return e.message;
  }
}());
});
Isolate.$lazy($, "nullPropertyPattern", "TypeErrorDecoder_nullPropertyPattern", "get$TypeErrorDecoder_nullPropertyPattern", function() {
  return H.TypeErrorDecoder_extractPattern(H.TypeErrorDecoder_provokePropertyErrorOn(null));
});
Isolate.$lazy($, "nullLiteralPropertyPattern", "TypeErrorDecoder_nullLiteralPropertyPattern", "get$TypeErrorDecoder_nullLiteralPropertyPattern", function() {
  return H.TypeErrorDecoder_extractPattern(function() {
  try {
    null.$method$;
  } catch (e) {
    return e.message;
  }
}());
});
Isolate.$lazy($, "undefinedPropertyPattern", "TypeErrorDecoder_undefinedPropertyPattern", "get$TypeErrorDecoder_undefinedPropertyPattern", function() {
  return H.TypeErrorDecoder_extractPattern(H.TypeErrorDecoder_provokePropertyErrorOn(void 0));
});
Isolate.$lazy($, "undefinedLiteralPropertyPattern", "TypeErrorDecoder_undefinedLiteralPropertyPattern", "get$TypeErrorDecoder_undefinedLiteralPropertyPattern", function() {
  return H.TypeErrorDecoder_extractPattern(function() {
  try {
    (void 0).$method$;
  } catch (e) {
    return e.message;
  }
}());
});
Isolate.$lazy($, "_toStringList", "IterableMixinWorkaround__toStringList", "get$IterableMixinWorkaround__toStringList", function() {
  return [];
});
Isolate.$lazy($, "_asyncCallbacks", "_asyncCallbacks", "get$_asyncCallbacks", function() {
  var t1, t2;
  t1 = {func: "void_", void: true};
  t2 = H.setRuntimeTypeInfo(new P.ListQueue(null, 0, 0, 0), [t1]);
  t2.ListQueue$1(null, t1);
  return t2;
});
Isolate.$lazy($, "_toStringVisiting", "_toStringVisiting", "get$_toStringVisiting", function() {
  return P.HashSet_HashSet$identity(null);
});
Isolate.$lazy($, "_toStringList", "Maps__toStringList", "get$Maps__toStringList", function() {
  return [];
});
// Native classes

init.functionAliases = {};
;
init.metadata = [{func: "void_", void: true},
{func: "void__dynamic", void: true, args: [null]},
{func: "void__dynamic__StackTrace", void: true, args: [null], opt: [P.StackTrace]},
,
{func: "bool__dynamic_dynamic", ret: J.JSBool, args: [null, null]},
{func: "int__dynamic", ret: J.JSInt, args: [null]},
{func: "bool__Object_Object", ret: J.JSBool, args: [P.Object, P.Object]},
{func: "int__Object", ret: J.JSInt, args: [P.Object]},
{func: "args0"},
{func: "args2", args: [null, null]},
{func: "args1", args: [null]},
{func: "dynamic__dynamic_String", args: [null, J.JSString]},
{func: "dynamic__String", args: [J.JSString]},
{func: "void__num", void: true, args: [J.JSNumber]},
{func: "dynamic__dynamic__dynamic", args: [null], opt: [null]},
{func: "dynamic__dynamic_StackTrace", args: [null, P.StackTrace]},
{func: "dynamic__Symbol_dynamic", args: [P.Symbol, null]},
{func: "String__int", ret: J.JSString, args: [J.JSInt]},
];
$ = null;
Isolate = Isolate.$finishIsolateConstructor(Isolate);
$ = new Isolate();
function convertToFastObject(properties) {
  function MyClass() {};
  MyClass.prototype = properties;
  new MyClass();
  return properties;
}
A = convertToFastObject(A);
B = convertToFastObject(B);
C = convertToFastObject(C);
D = convertToFastObject(D);
E = convertToFastObject(E);
F = convertToFastObject(F);
G = convertToFastObject(G);
H = convertToFastObject(H);
J = convertToFastObject(J);
K = convertToFastObject(K);
L = convertToFastObject(L);
M = convertToFastObject(M);
N = convertToFastObject(N);
O = convertToFastObject(O);
P = convertToFastObject(P);
Q = convertToFastObject(Q);
R = convertToFastObject(R);
S = convertToFastObject(S);
T = convertToFastObject(T);
U = convertToFastObject(U);
V = convertToFastObject(V);
W = convertToFastObject(W);
X = convertToFastObject(X);
Y = convertToFastObject(Y);
Z = convertToFastObject(Z);
!function() {
  var objectProto = Object.prototype;
  for (var i = 0;; i++) {
    var property = "___dart_dispatch_record_ZxYxX_0_";
    if (i > 0)
      property = rootProperty + "_" + i;
    if (!(property in objectProto))
      return init.dispatchPropertyName = property;
  }
}();
// BEGIN invoke [main].
;(function (callback) {
  if (typeof document === "undefined") {
    callback(null);
    return;
  }
  if (document.currentScript) {
    callback(document.currentScript);
    return;
  }

  var scripts = document.scripts;
  function onLoad(event) {
    for (var i = 0; i < scripts.length; ++i) {
      scripts[i].removeEventListener("load", onLoad, false);
    }
    callback(event.target);
  }
  for (var i = 0; i < scripts.length; ++i) {
    scripts[i].addEventListener("load", onLoad, false);
  }
})(function(currentScript) {
  init.currentScript = currentScript;

  if (typeof dartMainRunner === "function") {
    dartMainRunner(function() { H.startRootIsolate(Q.main$closure()); });
  } else {
    H.startRootIsolate(Q.main$closure());
  }
});
// END invoke [main].
function init() {
  Isolate.$isolateProperties = {};
  function generateAccessor(fieldDescriptor, accessors, cls) {
    var fieldInformation = fieldDescriptor.split("-");
    var field = fieldInformation[0];
    var len = field.length;
    var code = field.charCodeAt(len - 1);
    var reflectable;
    if (fieldInformation.length > 1)
      reflectable = true;
    else
      reflectable = false;
    code = code >= 60 && code <= 64 ? code - 59 : code >= 123 && code <= 126 ? code - 117 : code >= 37 && code <= 43 ? code - 27 : 0;
    if (code) {
      var getterCode = code & 3;
      var setterCode = code >> 2;
      var accessorName = field = field.substring(0, len - 1);
      var divider = field.indexOf(":");
      if (divider > 0) {
        accessorName = field.substring(0, divider);
        field = field.substring(divider + 1);
      }
      if (getterCode) {
        var args = getterCode & 2 ? "receiver" : "";
        var receiver = getterCode & 1 ? "this" : "receiver";
        var body = "return " + receiver + "." + field;
        var property = cls + ".prototype.get$" + accessorName + "=";
        var fn = "function(" + args + "){" + body + "}";
        if (reflectable)
          accessors.push(property + "$reflectable(" + fn + ");\n");
        else
          accessors.push(property + fn + ";\n");
      }
      if (setterCode) {
        var args = setterCode & 2 ? "receiver, value" : "value";
        var receiver = setterCode & 1 ? "this" : "receiver";
        var body = receiver + "." + field + " = value";
        var property = cls + ".prototype.set$" + accessorName + "=";
        var fn = "function(" + args + "){" + body + "}";
        if (reflectable)
          accessors.push(property + "$reflectable(" + fn + ");\n");
        else
          accessors.push(property + fn + ";\n");
      }
    }
    return field;
  }
  Isolate.$isolateProperties.$generateAccessor = generateAccessor;
  function defineClass(name, cls, fields) {
    var accessors = [];
    var str = "function " + cls + "(";
    var body = "";
    for (var i = 0; i < fields.length; i++) {
      if (i != 0)
        str += ", ";
      var field = generateAccessor(fields[i], accessors, cls);
      var parameter = "parameter_" + field;
      str += parameter;
      body += "this." + field + " = " + parameter + ";\n";
    }
    str += ") {\n" + body + "}\n";
    str += cls + ".builtin$cls=\"" + name + "\";\n";
    str += "$desc=$collectedClasses." + cls + ";\n";
    str += "if($desc instanceof Array) $desc = $desc[1];\n";
    str += cls + ".prototype = $desc;\n";
    if (typeof defineClass.name != "string") {
      str += cls + ".name=\"" + cls + "\";\n";
    }
    str += accessors.join("");
    return str;
  }
  var inheritFrom = function() {
    function tmp() {
    }
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    return function(constructor, superConstructor) {
      tmp.prototype = superConstructor.prototype;
      var object = new tmp();
      var properties = constructor.prototype;
      for (var member in properties)
        if (hasOwnProperty.call(properties, member))
          object[member] = properties[member];
      object.constructor = constructor;
      constructor.prototype = object;
      return object;
    };
  }();
  Isolate.$finishClasses = function(collectedClasses, isolateProperties, existingIsolateProperties) {
    var pendingClasses = {};
    if (!init.allClasses)
      init.allClasses = {};
    var allClasses = init.allClasses;
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    if (typeof dart_precompiled == "function") {
      var constructors = dart_precompiled(collectedClasses);
    } else {
      var combinedConstructorFunction = "function $reflectable(fn){fn.$reflectable=1;return fn};\n" + "var $desc;\n";
      var constructorsList = [];
    }
    for (var cls in collectedClasses) {
      if (hasOwnProperty.call(collectedClasses, cls)) {
        var desc = collectedClasses[cls];
        if (desc instanceof Array)
          desc = desc[1];
        var classData = desc[""], supr, name = cls, fields = classData;
        if (typeof classData == "string") {
          var split = classData.split("/");
          if (split.length == 2) {
            name = split[0];
            fields = split[1];
          }
        }
        var s = fields.split(";");
        fields = s[1] == "" ? [] : s[1].split(",");
        supr = s[0];
        split = supr.split(":");
        if (split.length == 2) {
          supr = split[0];
          var functionSignature = split[1];
          if (functionSignature)
            desc.$signature = function(s) {
              return function() {
                return init.metadata[s];
              };
            }(functionSignature);
        }
        if (supr && supr.indexOf("+") > 0) {
          s = supr.split("+");
          supr = s[0];
          var mixin = collectedClasses[s[1]];
          if (mixin instanceof Array)
            mixin = mixin[1];
          for (var d in mixin) {
            if (hasOwnProperty.call(mixin, d) && !hasOwnProperty.call(desc, d))
              desc[d] = mixin[d];
          }
        }
        if (typeof dart_precompiled != "function") {
          combinedConstructorFunction += defineClass(name, cls, fields);
          constructorsList.push(cls);
        }
        if (supr)
          pendingClasses[cls] = supr;
      }
    }
    if (typeof dart_precompiled != "function") {
      combinedConstructorFunction += "return [\n  " + constructorsList.join(",\n  ") + "\n]";
      var constructors = new Function("$collectedClasses", combinedConstructorFunction)(collectedClasses);
      combinedConstructorFunction = null;
    }
    for (var i = 0; i < constructors.length; i++) {
      var constructor = constructors[i];
      var cls = constructor.name;
      var desc = collectedClasses[cls];
      var globalObject = isolateProperties;
      if (desc instanceof Array) {
        globalObject = desc[0] || isolateProperties;
        desc = desc[1];
      }
      allClasses[cls] = constructor;
      globalObject[cls] = constructor;
    }
    constructors = null;
    var finishedClasses = {};
    init.interceptorsByTag = Object.create(null);
    init.leafTags = {};
    function finishClass(cls) {
      var hasOwnProperty = Object.prototype.hasOwnProperty;
      if (hasOwnProperty.call(finishedClasses, cls))
        return;
      finishedClasses[cls] = true;
      var superclass = pendingClasses[cls];
      if (!superclass || typeof superclass != "string")
        return;
      finishClass(superclass);
      var constructor = allClasses[cls];
      var superConstructor = allClasses[superclass];
      if (!superConstructor)
        superConstructor = existingIsolateProperties[superclass];
      var prototype = inheritFrom(constructor, superConstructor);
      if (hasOwnProperty.call(prototype, "%")) {
        var nativeSpec = prototype["%"].split(";");
        if (nativeSpec[0]) {
          var tags = nativeSpec[0].split("|");
          for (var i = 0; i < tags.length; i++) {
            init.interceptorsByTag[tags[i]] = constructor;
            init.leafTags[tags[i]] = true;
          }
        }
        if (nativeSpec[1]) {
          tags = nativeSpec[1].split("|");
          if (nativeSpec[2]) {
            var subclasses = nativeSpec[2].split("|");
            for (var i = 0; i < subclasses.length; i++) {
              var subclass = allClasses[subclasses[i]];
              subclass.$nativeSuperclassTag = tags[0];
            }
          }
          for (i = 0; i < tags.length; i++) {
            init.interceptorsByTag[tags[i]] = constructor;
            init.leafTags[tags[i]] = false;
          }
        }
      }
    }
    for (var cls in pendingClasses)
      finishClass(cls);
  };
  Isolate.$lazy = function(prototype, staticName, fieldName, getterName, lazyValue) {
    var sentinelUndefined = {};
    var sentinelInProgress = {};
    prototype[fieldName] = sentinelUndefined;
    prototype[getterName] = function() {
      var result = $[fieldName];
      try {
        if (result === sentinelUndefined) {
          $[fieldName] = sentinelInProgress;
          try {
            result = $[fieldName] = lazyValue();
          } finally {
            if (result === sentinelUndefined) {
              if ($[fieldName] === sentinelInProgress) {
                $[fieldName] = null;
              }
            }
          }
        } else {
          if (result === sentinelInProgress)
            H.throwCyclicInit(staticName);
        }
        return result;
      } finally {
        $[getterName] = function() {
          return this[fieldName];
        };
      }
    };
  };
  Isolate.$finishIsolateConstructor = function(oldIsolate) {
    var isolateProperties = oldIsolate.$isolateProperties;
    function Isolate() {
      var hasOwnProperty = Object.prototype.hasOwnProperty;
      for (var staticName in isolateProperties)
        if (hasOwnProperty.call(isolateProperties, staticName))
          this[staticName] = isolateProperties[staticName];
      function ForceEfficientMap() {
      }
      ForceEfficientMap.prototype = this;
      new ForceEfficientMap();
    }
    Isolate.prototype = oldIsolate.prototype;
    Isolate.prototype.constructor = Isolate;
    Isolate.$isolateProperties = isolateProperties;
    Isolate.$finishClasses = oldIsolate.$finishClasses;
    Isolate.makeConstantList = oldIsolate.makeConstantList;
    return Isolate;
  };
}
})()
function dart_precompiled($collectedClasses) {
  var $desc;
  function HtmlElement() {
  }
  HtmlElement.builtin$cls = "HtmlElement";
  if (!"name" in HtmlElement)
    HtmlElement.name = "HtmlElement";
  $desc = $collectedClasses.HtmlElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HtmlElement.prototype = $desc;
  function AnchorElement() {
  }
  AnchorElement.builtin$cls = "AnchorElement";
  if (!"name" in AnchorElement)
    AnchorElement.name = "AnchorElement";
  $desc = $collectedClasses.AnchorElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnchorElement.prototype = $desc;
  function AnimationEvent() {
  }
  AnimationEvent.builtin$cls = "AnimationEvent";
  if (!"name" in AnimationEvent)
    AnimationEvent.name = "AnimationEvent";
  $desc = $collectedClasses.AnimationEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimationEvent.prototype = $desc;
  function AreaElement() {
  }
  AreaElement.builtin$cls = "AreaElement";
  if (!"name" in AreaElement)
    AreaElement.name = "AreaElement";
  $desc = $collectedClasses.AreaElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AreaElement.prototype = $desc;
  function AudioElement() {
  }
  AudioElement.builtin$cls = "AudioElement";
  if (!"name" in AudioElement)
    AudioElement.name = "AudioElement";
  $desc = $collectedClasses.AudioElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AudioElement.prototype = $desc;
  function AutocompleteErrorEvent() {
  }
  AutocompleteErrorEvent.builtin$cls = "AutocompleteErrorEvent";
  if (!"name" in AutocompleteErrorEvent)
    AutocompleteErrorEvent.name = "AutocompleteErrorEvent";
  $desc = $collectedClasses.AutocompleteErrorEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AutocompleteErrorEvent.prototype = $desc;
  function BRElement() {
  }
  BRElement.builtin$cls = "BRElement";
  if (!"name" in BRElement)
    BRElement.name = "BRElement";
  $desc = $collectedClasses.BRElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  BRElement.prototype = $desc;
  function BaseElement() {
  }
  BaseElement.builtin$cls = "BaseElement";
  if (!"name" in BaseElement)
    BaseElement.name = "BaseElement";
  $desc = $collectedClasses.BaseElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  BaseElement.prototype = $desc;
  function BeforeLoadEvent() {
  }
  BeforeLoadEvent.builtin$cls = "BeforeLoadEvent";
  if (!"name" in BeforeLoadEvent)
    BeforeLoadEvent.name = "BeforeLoadEvent";
  $desc = $collectedClasses.BeforeLoadEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  BeforeLoadEvent.prototype = $desc;
  function BeforeUnloadEvent() {
  }
  BeforeUnloadEvent.builtin$cls = "BeforeUnloadEvent";
  if (!"name" in BeforeUnloadEvent)
    BeforeUnloadEvent.name = "BeforeUnloadEvent";
  $desc = $collectedClasses.BeforeUnloadEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  BeforeUnloadEvent.prototype = $desc;
  function BodyElement() {
  }
  BodyElement.builtin$cls = "BodyElement";
  if (!"name" in BodyElement)
    BodyElement.name = "BodyElement";
  $desc = $collectedClasses.BodyElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  BodyElement.prototype = $desc;
  function ButtonElement() {
  }
  ButtonElement.builtin$cls = "ButtonElement";
  if (!"name" in ButtonElement)
    ButtonElement.name = "ButtonElement";
  $desc = $collectedClasses.ButtonElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ButtonElement.prototype = $desc;
  function CDataSection() {
  }
  CDataSection.builtin$cls = "CDataSection";
  if (!"name" in CDataSection)
    CDataSection.name = "CDataSection";
  $desc = $collectedClasses.CDataSection;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CDataSection.prototype = $desc;
  function CanvasElement() {
  }
  CanvasElement.builtin$cls = "CanvasElement";
  if (!"name" in CanvasElement)
    CanvasElement.name = "CanvasElement";
  $desc = $collectedClasses.CanvasElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CanvasElement.prototype = $desc;
  function CharacterData() {
  }
  CharacterData.builtin$cls = "CharacterData";
  if (!"name" in CharacterData)
    CharacterData.name = "CharacterData";
  $desc = $collectedClasses.CharacterData;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CharacterData.prototype = $desc;
  CharacterData.prototype.get$length = function(receiver) {
    return receiver.length;
  };
  function CloseEvent() {
  }
  CloseEvent.builtin$cls = "CloseEvent";
  if (!"name" in CloseEvent)
    CloseEvent.name = "CloseEvent";
  $desc = $collectedClasses.CloseEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CloseEvent.prototype = $desc;
  function Comment() {
  }
  Comment.builtin$cls = "Comment";
  if (!"name" in Comment)
    Comment.name = "Comment";
  $desc = $collectedClasses.Comment;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Comment.prototype = $desc;
  function CompositionEvent() {
  }
  CompositionEvent.builtin$cls = "CompositionEvent";
  if (!"name" in CompositionEvent)
    CompositionEvent.name = "CompositionEvent";
  $desc = $collectedClasses.CompositionEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CompositionEvent.prototype = $desc;
  function ContentElement() {
  }
  ContentElement.builtin$cls = "ContentElement";
  if (!"name" in ContentElement)
    ContentElement.name = "ContentElement";
  $desc = $collectedClasses.ContentElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ContentElement.prototype = $desc;
  function CssFontFaceLoadEvent() {
  }
  CssFontFaceLoadEvent.builtin$cls = "CssFontFaceLoadEvent";
  if (!"name" in CssFontFaceLoadEvent)
    CssFontFaceLoadEvent.name = "CssFontFaceLoadEvent";
  $desc = $collectedClasses.CssFontFaceLoadEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CssFontFaceLoadEvent.prototype = $desc;
  function CssStyleDeclaration() {
  }
  CssStyleDeclaration.builtin$cls = "CssStyleDeclaration";
  if (!"name" in CssStyleDeclaration)
    CssStyleDeclaration.name = "CssStyleDeclaration";
  $desc = $collectedClasses.CssStyleDeclaration;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CssStyleDeclaration.prototype = $desc;
  CssStyleDeclaration.prototype.get$length = function(receiver) {
    return receiver.length;
  };
  function CustomEvent() {
  }
  CustomEvent.builtin$cls = "CustomEvent";
  if (!"name" in CustomEvent)
    CustomEvent.name = "CustomEvent";
  $desc = $collectedClasses.CustomEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CustomEvent.prototype = $desc;
  function DListElement() {
  }
  DListElement.builtin$cls = "DListElement";
  if (!"name" in DListElement)
    DListElement.name = "DListElement";
  $desc = $collectedClasses.DListElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DListElement.prototype = $desc;
  function DataListElement() {
  }
  DataListElement.builtin$cls = "DataListElement";
  if (!"name" in DataListElement)
    DataListElement.name = "DataListElement";
  $desc = $collectedClasses.DataListElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DataListElement.prototype = $desc;
  function DetailsElement() {
  }
  DetailsElement.builtin$cls = "DetailsElement";
  if (!"name" in DetailsElement)
    DetailsElement.name = "DetailsElement";
  $desc = $collectedClasses.DetailsElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DetailsElement.prototype = $desc;
  function DeviceMotionEvent() {
  }
  DeviceMotionEvent.builtin$cls = "DeviceMotionEvent";
  if (!"name" in DeviceMotionEvent)
    DeviceMotionEvent.name = "DeviceMotionEvent";
  $desc = $collectedClasses.DeviceMotionEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DeviceMotionEvent.prototype = $desc;
  function DeviceOrientationEvent() {
  }
  DeviceOrientationEvent.builtin$cls = "DeviceOrientationEvent";
  if (!"name" in DeviceOrientationEvent)
    DeviceOrientationEvent.name = "DeviceOrientationEvent";
  $desc = $collectedClasses.DeviceOrientationEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DeviceOrientationEvent.prototype = $desc;
  function DialogElement() {
  }
  DialogElement.builtin$cls = "DialogElement";
  if (!"name" in DialogElement)
    DialogElement.name = "DialogElement";
  $desc = $collectedClasses.DialogElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DialogElement.prototype = $desc;
  function DivElement() {
  }
  DivElement.builtin$cls = "DivElement";
  if (!"name" in DivElement)
    DivElement.name = "DivElement";
  $desc = $collectedClasses.DivElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DivElement.prototype = $desc;
  function Document() {
  }
  Document.builtin$cls = "Document";
  if (!"name" in Document)
    Document.name = "Document";
  $desc = $collectedClasses.Document;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Document.prototype = $desc;
  function DocumentFragment() {
  }
  DocumentFragment.builtin$cls = "DocumentFragment";
  if (!"name" in DocumentFragment)
    DocumentFragment.name = "DocumentFragment";
  $desc = $collectedClasses.DocumentFragment;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DocumentFragment.prototype = $desc;
  function DocumentType() {
  }
  DocumentType.builtin$cls = "DocumentType";
  if (!"name" in DocumentType)
    DocumentType.name = "DocumentType";
  $desc = $collectedClasses.DocumentType;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DocumentType.prototype = $desc;
  function DomError() {
  }
  DomError.builtin$cls = "DomError";
  if (!"name" in DomError)
    DomError.name = "DomError";
  $desc = $collectedClasses.DomError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DomError.prototype = $desc;
  function DomException() {
  }
  DomException.builtin$cls = "DomException";
  if (!"name" in DomException)
    DomException.name = "DomException";
  $desc = $collectedClasses.DomException;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DomException.prototype = $desc;
  function Element() {
  }
  Element.builtin$cls = "Element";
  if (!"name" in Element)
    Element.name = "Element";
  $desc = $collectedClasses.Element;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Element.prototype = $desc;
  function EmbedElement() {
  }
  EmbedElement.builtin$cls = "EmbedElement";
  if (!"name" in EmbedElement)
    EmbedElement.name = "EmbedElement";
  $desc = $collectedClasses.EmbedElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  EmbedElement.prototype = $desc;
  EmbedElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function ErrorEvent() {
  }
  ErrorEvent.builtin$cls = "ErrorEvent";
  if (!"name" in ErrorEvent)
    ErrorEvent.name = "ErrorEvent";
  $desc = $collectedClasses.ErrorEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ErrorEvent.prototype = $desc;
  ErrorEvent.prototype.get$error = function(receiver) {
    return receiver.error;
  };
  function Event() {
  }
  Event.builtin$cls = "Event";
  if (!"name" in Event)
    Event.name = "Event";
  $desc = $collectedClasses.Event;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Event.prototype = $desc;
  function EventTarget() {
  }
  EventTarget.builtin$cls = "EventTarget";
  if (!"name" in EventTarget)
    EventTarget.name = "EventTarget";
  $desc = $collectedClasses.EventTarget;
  if ($desc instanceof Array)
    $desc = $desc[1];
  EventTarget.prototype = $desc;
  function FieldSetElement() {
  }
  FieldSetElement.builtin$cls = "FieldSetElement";
  if (!"name" in FieldSetElement)
    FieldSetElement.name = "FieldSetElement";
  $desc = $collectedClasses.FieldSetElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FieldSetElement.prototype = $desc;
  function FileError() {
  }
  FileError.builtin$cls = "FileError";
  if (!"name" in FileError)
    FileError.name = "FileError";
  $desc = $collectedClasses.FileError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FileError.prototype = $desc;
  function FocusEvent() {
  }
  FocusEvent.builtin$cls = "FocusEvent";
  if (!"name" in FocusEvent)
    FocusEvent.name = "FocusEvent";
  $desc = $collectedClasses.FocusEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FocusEvent.prototype = $desc;
  function FormElement() {
  }
  FormElement.builtin$cls = "FormElement";
  if (!"name" in FormElement)
    FormElement.name = "FormElement";
  $desc = $collectedClasses.FormElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FormElement.prototype = $desc;
  FormElement.prototype.get$length = function(receiver) {
    return receiver.length;
  };
  function HRElement() {
  }
  HRElement.builtin$cls = "HRElement";
  if (!"name" in HRElement)
    HRElement.name = "HRElement";
  $desc = $collectedClasses.HRElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HRElement.prototype = $desc;
  function HashChangeEvent() {
  }
  HashChangeEvent.builtin$cls = "HashChangeEvent";
  if (!"name" in HashChangeEvent)
    HashChangeEvent.name = "HashChangeEvent";
  $desc = $collectedClasses.HashChangeEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HashChangeEvent.prototype = $desc;
  function HeadElement() {
  }
  HeadElement.builtin$cls = "HeadElement";
  if (!"name" in HeadElement)
    HeadElement.name = "HeadElement";
  $desc = $collectedClasses.HeadElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HeadElement.prototype = $desc;
  function HeadingElement() {
  }
  HeadingElement.builtin$cls = "HeadingElement";
  if (!"name" in HeadingElement)
    HeadingElement.name = "HeadingElement";
  $desc = $collectedClasses.HeadingElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HeadingElement.prototype = $desc;
  function HtmlDocument() {
  }
  HtmlDocument.builtin$cls = "HtmlDocument";
  if (!"name" in HtmlDocument)
    HtmlDocument.name = "HtmlDocument";
  $desc = $collectedClasses.HtmlDocument;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HtmlDocument.prototype = $desc;
  function HtmlHtmlElement() {
  }
  HtmlHtmlElement.builtin$cls = "HtmlHtmlElement";
  if (!"name" in HtmlHtmlElement)
    HtmlHtmlElement.name = "HtmlHtmlElement";
  $desc = $collectedClasses.HtmlHtmlElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HtmlHtmlElement.prototype = $desc;
  function IFrameElement() {
  }
  IFrameElement.builtin$cls = "IFrameElement";
  if (!"name" in IFrameElement)
    IFrameElement.name = "IFrameElement";
  $desc = $collectedClasses.IFrameElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  IFrameElement.prototype = $desc;
  IFrameElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function ImageElement() {
  }
  ImageElement.builtin$cls = "ImageElement";
  if (!"name" in ImageElement)
    ImageElement.name = "ImageElement";
  $desc = $collectedClasses.ImageElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ImageElement.prototype = $desc;
  ImageElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function InputElement() {
  }
  InputElement.builtin$cls = "InputElement";
  if (!"name" in InputElement)
    InputElement.name = "InputElement";
  $desc = $collectedClasses.InputElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  InputElement.prototype = $desc;
  InputElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function KeyboardEvent() {
  }
  KeyboardEvent.builtin$cls = "KeyboardEvent";
  if (!"name" in KeyboardEvent)
    KeyboardEvent.name = "KeyboardEvent";
  $desc = $collectedClasses.KeyboardEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  KeyboardEvent.prototype = $desc;
  function KeygenElement() {
  }
  KeygenElement.builtin$cls = "KeygenElement";
  if (!"name" in KeygenElement)
    KeygenElement.name = "KeygenElement";
  $desc = $collectedClasses.KeygenElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  KeygenElement.prototype = $desc;
  function LIElement() {
  }
  LIElement.builtin$cls = "LIElement";
  if (!"name" in LIElement)
    LIElement.name = "LIElement";
  $desc = $collectedClasses.LIElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LIElement.prototype = $desc;
  function LabelElement() {
  }
  LabelElement.builtin$cls = "LabelElement";
  if (!"name" in LabelElement)
    LabelElement.name = "LabelElement";
  $desc = $collectedClasses.LabelElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LabelElement.prototype = $desc;
  function LegendElement() {
  }
  LegendElement.builtin$cls = "LegendElement";
  if (!"name" in LegendElement)
    LegendElement.name = "LegendElement";
  $desc = $collectedClasses.LegendElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LegendElement.prototype = $desc;
  function LinkElement() {
  }
  LinkElement.builtin$cls = "LinkElement";
  if (!"name" in LinkElement)
    LinkElement.name = "LinkElement";
  $desc = $collectedClasses.LinkElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LinkElement.prototype = $desc;
  function MapElement() {
  }
  MapElement.builtin$cls = "MapElement";
  if (!"name" in MapElement)
    MapElement.name = "MapElement";
  $desc = $collectedClasses.MapElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MapElement.prototype = $desc;
  function MediaElement() {
  }
  MediaElement.builtin$cls = "MediaElement";
  if (!"name" in MediaElement)
    MediaElement.name = "MediaElement";
  $desc = $collectedClasses.MediaElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaElement.prototype = $desc;
  MediaElement.prototype.get$error = function(receiver) {
    return receiver.error;
  };
  MediaElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function MediaError() {
  }
  MediaError.builtin$cls = "MediaError";
  if (!"name" in MediaError)
    MediaError.name = "MediaError";
  $desc = $collectedClasses.MediaError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaError.prototype = $desc;
  function MediaKeyError() {
  }
  MediaKeyError.builtin$cls = "MediaKeyError";
  if (!"name" in MediaKeyError)
    MediaKeyError.name = "MediaKeyError";
  $desc = $collectedClasses.MediaKeyError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaKeyError.prototype = $desc;
  function MediaKeyEvent() {
  }
  MediaKeyEvent.builtin$cls = "MediaKeyEvent";
  if (!"name" in MediaKeyEvent)
    MediaKeyEvent.name = "MediaKeyEvent";
  $desc = $collectedClasses.MediaKeyEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaKeyEvent.prototype = $desc;
  function MediaKeyMessageEvent() {
  }
  MediaKeyMessageEvent.builtin$cls = "MediaKeyMessageEvent";
  if (!"name" in MediaKeyMessageEvent)
    MediaKeyMessageEvent.name = "MediaKeyMessageEvent";
  $desc = $collectedClasses.MediaKeyMessageEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaKeyMessageEvent.prototype = $desc;
  function MediaKeyNeededEvent() {
  }
  MediaKeyNeededEvent.builtin$cls = "MediaKeyNeededEvent";
  if (!"name" in MediaKeyNeededEvent)
    MediaKeyNeededEvent.name = "MediaKeyNeededEvent";
  $desc = $collectedClasses.MediaKeyNeededEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaKeyNeededEvent.prototype = $desc;
  function MediaStream() {
  }
  MediaStream.builtin$cls = "MediaStream";
  if (!"name" in MediaStream)
    MediaStream.name = "MediaStream";
  $desc = $collectedClasses.MediaStream;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaStream.prototype = $desc;
  function MediaStreamEvent() {
  }
  MediaStreamEvent.builtin$cls = "MediaStreamEvent";
  if (!"name" in MediaStreamEvent)
    MediaStreamEvent.name = "MediaStreamEvent";
  $desc = $collectedClasses.MediaStreamEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaStreamEvent.prototype = $desc;
  function MediaStreamTrackEvent() {
  }
  MediaStreamTrackEvent.builtin$cls = "MediaStreamTrackEvent";
  if (!"name" in MediaStreamTrackEvent)
    MediaStreamTrackEvent.name = "MediaStreamTrackEvent";
  $desc = $collectedClasses.MediaStreamTrackEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MediaStreamTrackEvent.prototype = $desc;
  function MenuElement() {
  }
  MenuElement.builtin$cls = "MenuElement";
  if (!"name" in MenuElement)
    MenuElement.name = "MenuElement";
  $desc = $collectedClasses.MenuElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MenuElement.prototype = $desc;
  function MessageEvent() {
  }
  MessageEvent.builtin$cls = "MessageEvent";
  if (!"name" in MessageEvent)
    MessageEvent.name = "MessageEvent";
  $desc = $collectedClasses.MessageEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MessageEvent.prototype = $desc;
  function MetaElement() {
  }
  MetaElement.builtin$cls = "MetaElement";
  if (!"name" in MetaElement)
    MetaElement.name = "MetaElement";
  $desc = $collectedClasses.MetaElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MetaElement.prototype = $desc;
  function MeterElement() {
  }
  MeterElement.builtin$cls = "MeterElement";
  if (!"name" in MeterElement)
    MeterElement.name = "MeterElement";
  $desc = $collectedClasses.MeterElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MeterElement.prototype = $desc;
  function MidiConnectionEvent() {
  }
  MidiConnectionEvent.builtin$cls = "MidiConnectionEvent";
  if (!"name" in MidiConnectionEvent)
    MidiConnectionEvent.name = "MidiConnectionEvent";
  $desc = $collectedClasses.MidiConnectionEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MidiConnectionEvent.prototype = $desc;
  function MidiMessageEvent() {
  }
  MidiMessageEvent.builtin$cls = "MidiMessageEvent";
  if (!"name" in MidiMessageEvent)
    MidiMessageEvent.name = "MidiMessageEvent";
  $desc = $collectedClasses.MidiMessageEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MidiMessageEvent.prototype = $desc;
  function ModElement() {
  }
  ModElement.builtin$cls = "ModElement";
  if (!"name" in ModElement)
    ModElement.name = "ModElement";
  $desc = $collectedClasses.ModElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ModElement.prototype = $desc;
  function MouseEvent() {
  }
  MouseEvent.builtin$cls = "MouseEvent";
  if (!"name" in MouseEvent)
    MouseEvent.name = "MouseEvent";
  $desc = $collectedClasses.MouseEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MouseEvent.prototype = $desc;
  function Navigator() {
  }
  Navigator.builtin$cls = "Navigator";
  if (!"name" in Navigator)
    Navigator.name = "Navigator";
  $desc = $collectedClasses.Navigator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Navigator.prototype = $desc;
  function NavigatorUserMediaError() {
  }
  NavigatorUserMediaError.builtin$cls = "NavigatorUserMediaError";
  if (!"name" in NavigatorUserMediaError)
    NavigatorUserMediaError.name = "NavigatorUserMediaError";
  $desc = $collectedClasses.NavigatorUserMediaError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  NavigatorUserMediaError.prototype = $desc;
  function Node() {
  }
  Node.builtin$cls = "Node";
  if (!"name" in Node)
    Node.name = "Node";
  $desc = $collectedClasses.Node;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Node.prototype = $desc;
  function NodeList() {
  }
  NodeList.builtin$cls = "NodeList";
  if (!"name" in NodeList)
    NodeList.name = "NodeList";
  $desc = $collectedClasses.NodeList;
  if ($desc instanceof Array)
    $desc = $desc[1];
  NodeList.prototype = $desc;
  function OListElement() {
  }
  OListElement.builtin$cls = "OListElement";
  if (!"name" in OListElement)
    OListElement.name = "OListElement";
  $desc = $collectedClasses.OListElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  OListElement.prototype = $desc;
  function ObjectElement() {
  }
  ObjectElement.builtin$cls = "ObjectElement";
  if (!"name" in ObjectElement)
    ObjectElement.name = "ObjectElement";
  $desc = $collectedClasses.ObjectElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ObjectElement.prototype = $desc;
  function OptGroupElement() {
  }
  OptGroupElement.builtin$cls = "OptGroupElement";
  if (!"name" in OptGroupElement)
    OptGroupElement.name = "OptGroupElement";
  $desc = $collectedClasses.OptGroupElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  OptGroupElement.prototype = $desc;
  function OptionElement() {
  }
  OptionElement.builtin$cls = "OptionElement";
  if (!"name" in OptionElement)
    OptionElement.name = "OptionElement";
  $desc = $collectedClasses.OptionElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  OptionElement.prototype = $desc;
  function OutputElement() {
  }
  OutputElement.builtin$cls = "OutputElement";
  if (!"name" in OutputElement)
    OutputElement.name = "OutputElement";
  $desc = $collectedClasses.OutputElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  OutputElement.prototype = $desc;
  function OverflowEvent() {
  }
  OverflowEvent.builtin$cls = "OverflowEvent";
  if (!"name" in OverflowEvent)
    OverflowEvent.name = "OverflowEvent";
  $desc = $collectedClasses.OverflowEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  OverflowEvent.prototype = $desc;
  function PageTransitionEvent() {
  }
  PageTransitionEvent.builtin$cls = "PageTransitionEvent";
  if (!"name" in PageTransitionEvent)
    PageTransitionEvent.name = "PageTransitionEvent";
  $desc = $collectedClasses.PageTransitionEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PageTransitionEvent.prototype = $desc;
  function ParagraphElement() {
  }
  ParagraphElement.builtin$cls = "ParagraphElement";
  if (!"name" in ParagraphElement)
    ParagraphElement.name = "ParagraphElement";
  $desc = $collectedClasses.ParagraphElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ParagraphElement.prototype = $desc;
  function ParamElement() {
  }
  ParamElement.builtin$cls = "ParamElement";
  if (!"name" in ParamElement)
    ParamElement.name = "ParamElement";
  $desc = $collectedClasses.ParamElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ParamElement.prototype = $desc;
  function PopStateEvent() {
  }
  PopStateEvent.builtin$cls = "PopStateEvent";
  if (!"name" in PopStateEvent)
    PopStateEvent.name = "PopStateEvent";
  $desc = $collectedClasses.PopStateEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PopStateEvent.prototype = $desc;
  function PositionError() {
  }
  PositionError.builtin$cls = "PositionError";
  if (!"name" in PositionError)
    PositionError.name = "PositionError";
  $desc = $collectedClasses.PositionError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PositionError.prototype = $desc;
  function PreElement() {
  }
  PreElement.builtin$cls = "PreElement";
  if (!"name" in PreElement)
    PreElement.name = "PreElement";
  $desc = $collectedClasses.PreElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PreElement.prototype = $desc;
  function ProcessingInstruction() {
  }
  ProcessingInstruction.builtin$cls = "ProcessingInstruction";
  if (!"name" in ProcessingInstruction)
    ProcessingInstruction.name = "ProcessingInstruction";
  $desc = $collectedClasses.ProcessingInstruction;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ProcessingInstruction.prototype = $desc;
  function ProgressElement() {
  }
  ProgressElement.builtin$cls = "ProgressElement";
  if (!"name" in ProgressElement)
    ProgressElement.name = "ProgressElement";
  $desc = $collectedClasses.ProgressElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ProgressElement.prototype = $desc;
  function ProgressEvent() {
  }
  ProgressEvent.builtin$cls = "ProgressEvent";
  if (!"name" in ProgressEvent)
    ProgressEvent.name = "ProgressEvent";
  $desc = $collectedClasses.ProgressEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ProgressEvent.prototype = $desc;
  function QuoteElement() {
  }
  QuoteElement.builtin$cls = "QuoteElement";
  if (!"name" in QuoteElement)
    QuoteElement.name = "QuoteElement";
  $desc = $collectedClasses.QuoteElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  QuoteElement.prototype = $desc;
  function ResourceProgressEvent() {
  }
  ResourceProgressEvent.builtin$cls = "ResourceProgressEvent";
  if (!"name" in ResourceProgressEvent)
    ResourceProgressEvent.name = "ResourceProgressEvent";
  $desc = $collectedClasses.ResourceProgressEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ResourceProgressEvent.prototype = $desc;
  function RtcDataChannelEvent() {
  }
  RtcDataChannelEvent.builtin$cls = "RtcDataChannelEvent";
  if (!"name" in RtcDataChannelEvent)
    RtcDataChannelEvent.name = "RtcDataChannelEvent";
  $desc = $collectedClasses.RtcDataChannelEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RtcDataChannelEvent.prototype = $desc;
  function RtcDtmfToneChangeEvent() {
  }
  RtcDtmfToneChangeEvent.builtin$cls = "RtcDtmfToneChangeEvent";
  if (!"name" in RtcDtmfToneChangeEvent)
    RtcDtmfToneChangeEvent.name = "RtcDtmfToneChangeEvent";
  $desc = $collectedClasses.RtcDtmfToneChangeEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RtcDtmfToneChangeEvent.prototype = $desc;
  function RtcIceCandidateEvent() {
  }
  RtcIceCandidateEvent.builtin$cls = "RtcIceCandidateEvent";
  if (!"name" in RtcIceCandidateEvent)
    RtcIceCandidateEvent.name = "RtcIceCandidateEvent";
  $desc = $collectedClasses.RtcIceCandidateEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RtcIceCandidateEvent.prototype = $desc;
  function ScriptElement() {
  }
  ScriptElement.builtin$cls = "ScriptElement";
  if (!"name" in ScriptElement)
    ScriptElement.name = "ScriptElement";
  $desc = $collectedClasses.ScriptElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ScriptElement.prototype = $desc;
  ScriptElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function SecurityPolicyViolationEvent() {
  }
  SecurityPolicyViolationEvent.builtin$cls = "SecurityPolicyViolationEvent";
  if (!"name" in SecurityPolicyViolationEvent)
    SecurityPolicyViolationEvent.name = "SecurityPolicyViolationEvent";
  $desc = $collectedClasses.SecurityPolicyViolationEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SecurityPolicyViolationEvent.prototype = $desc;
  function SelectElement() {
  }
  SelectElement.builtin$cls = "SelectElement";
  if (!"name" in SelectElement)
    SelectElement.name = "SelectElement";
  $desc = $collectedClasses.SelectElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SelectElement.prototype = $desc;
  SelectElement.prototype.get$length = function(receiver) {
    return receiver.length;
  };
  function ShadowElement() {
  }
  ShadowElement.builtin$cls = "ShadowElement";
  if (!"name" in ShadowElement)
    ShadowElement.name = "ShadowElement";
  $desc = $collectedClasses.ShadowElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ShadowElement.prototype = $desc;
  function ShadowRoot() {
  }
  ShadowRoot.builtin$cls = "ShadowRoot";
  if (!"name" in ShadowRoot)
    ShadowRoot.name = "ShadowRoot";
  $desc = $collectedClasses.ShadowRoot;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ShadowRoot.prototype = $desc;
  function SourceElement() {
  }
  SourceElement.builtin$cls = "SourceElement";
  if (!"name" in SourceElement)
    SourceElement.name = "SourceElement";
  $desc = $collectedClasses.SourceElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SourceElement.prototype = $desc;
  SourceElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function SpanElement() {
  }
  SpanElement.builtin$cls = "SpanElement";
  if (!"name" in SpanElement)
    SpanElement.name = "SpanElement";
  $desc = $collectedClasses.SpanElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SpanElement.prototype = $desc;
  function SpeechInputEvent() {
  }
  SpeechInputEvent.builtin$cls = "SpeechInputEvent";
  if (!"name" in SpeechInputEvent)
    SpeechInputEvent.name = "SpeechInputEvent";
  $desc = $collectedClasses.SpeechInputEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SpeechInputEvent.prototype = $desc;
  function SpeechRecognitionError() {
  }
  SpeechRecognitionError.builtin$cls = "SpeechRecognitionError";
  if (!"name" in SpeechRecognitionError)
    SpeechRecognitionError.name = "SpeechRecognitionError";
  $desc = $collectedClasses.SpeechRecognitionError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SpeechRecognitionError.prototype = $desc;
  SpeechRecognitionError.prototype.get$error = function(receiver) {
    return receiver.error;
  };
  function SpeechRecognitionEvent() {
  }
  SpeechRecognitionEvent.builtin$cls = "SpeechRecognitionEvent";
  if (!"name" in SpeechRecognitionEvent)
    SpeechRecognitionEvent.name = "SpeechRecognitionEvent";
  $desc = $collectedClasses.SpeechRecognitionEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SpeechRecognitionEvent.prototype = $desc;
  function SpeechSynthesisEvent() {
  }
  SpeechSynthesisEvent.builtin$cls = "SpeechSynthesisEvent";
  if (!"name" in SpeechSynthesisEvent)
    SpeechSynthesisEvent.name = "SpeechSynthesisEvent";
  $desc = $collectedClasses.SpeechSynthesisEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SpeechSynthesisEvent.prototype = $desc;
  function StorageEvent() {
  }
  StorageEvent.builtin$cls = "StorageEvent";
  if (!"name" in StorageEvent)
    StorageEvent.name = "StorageEvent";
  $desc = $collectedClasses.StorageEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StorageEvent.prototype = $desc;
  function StyleElement() {
  }
  StyleElement.builtin$cls = "StyleElement";
  if (!"name" in StyleElement)
    StyleElement.name = "StyleElement";
  $desc = $collectedClasses.StyleElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StyleElement.prototype = $desc;
  function TableCaptionElement() {
  }
  TableCaptionElement.builtin$cls = "TableCaptionElement";
  if (!"name" in TableCaptionElement)
    TableCaptionElement.name = "TableCaptionElement";
  $desc = $collectedClasses.TableCaptionElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TableCaptionElement.prototype = $desc;
  function TableCellElement() {
  }
  TableCellElement.builtin$cls = "TableCellElement";
  if (!"name" in TableCellElement)
    TableCellElement.name = "TableCellElement";
  $desc = $collectedClasses.TableCellElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TableCellElement.prototype = $desc;
  function TableColElement() {
  }
  TableColElement.builtin$cls = "TableColElement";
  if (!"name" in TableColElement)
    TableColElement.name = "TableColElement";
  $desc = $collectedClasses.TableColElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TableColElement.prototype = $desc;
  function TableElement() {
  }
  TableElement.builtin$cls = "TableElement";
  if (!"name" in TableElement)
    TableElement.name = "TableElement";
  $desc = $collectedClasses.TableElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TableElement.prototype = $desc;
  function TableRowElement() {
  }
  TableRowElement.builtin$cls = "TableRowElement";
  if (!"name" in TableRowElement)
    TableRowElement.name = "TableRowElement";
  $desc = $collectedClasses.TableRowElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TableRowElement.prototype = $desc;
  function TableSectionElement() {
  }
  TableSectionElement.builtin$cls = "TableSectionElement";
  if (!"name" in TableSectionElement)
    TableSectionElement.name = "TableSectionElement";
  $desc = $collectedClasses.TableSectionElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TableSectionElement.prototype = $desc;
  function TemplateElement() {
  }
  TemplateElement.builtin$cls = "TemplateElement";
  if (!"name" in TemplateElement)
    TemplateElement.name = "TemplateElement";
  $desc = $collectedClasses.TemplateElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TemplateElement.prototype = $desc;
  function Text() {
  }
  Text.builtin$cls = "Text";
  if (!"name" in Text)
    Text.name = "Text";
  $desc = $collectedClasses.Text;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Text.prototype = $desc;
  function TextAreaElement() {
  }
  TextAreaElement.builtin$cls = "TextAreaElement";
  if (!"name" in TextAreaElement)
    TextAreaElement.name = "TextAreaElement";
  $desc = $collectedClasses.TextAreaElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TextAreaElement.prototype = $desc;
  function TextEvent() {
  }
  TextEvent.builtin$cls = "TextEvent";
  if (!"name" in TextEvent)
    TextEvent.name = "TextEvent";
  $desc = $collectedClasses.TextEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TextEvent.prototype = $desc;
  function TitleElement() {
  }
  TitleElement.builtin$cls = "TitleElement";
  if (!"name" in TitleElement)
    TitleElement.name = "TitleElement";
  $desc = $collectedClasses.TitleElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TitleElement.prototype = $desc;
  function TouchEvent() {
  }
  TouchEvent.builtin$cls = "TouchEvent";
  if (!"name" in TouchEvent)
    TouchEvent.name = "TouchEvent";
  $desc = $collectedClasses.TouchEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TouchEvent.prototype = $desc;
  function TrackElement() {
  }
  TrackElement.builtin$cls = "TrackElement";
  if (!"name" in TrackElement)
    TrackElement.name = "TrackElement";
  $desc = $collectedClasses.TrackElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TrackElement.prototype = $desc;
  TrackElement.prototype.set$src = function(receiver, v) {
    return receiver.src = v;
  };
  function TrackEvent() {
  }
  TrackEvent.builtin$cls = "TrackEvent";
  if (!"name" in TrackEvent)
    TrackEvent.name = "TrackEvent";
  $desc = $collectedClasses.TrackEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TrackEvent.prototype = $desc;
  function TransitionEvent() {
  }
  TransitionEvent.builtin$cls = "TransitionEvent";
  if (!"name" in TransitionEvent)
    TransitionEvent.name = "TransitionEvent";
  $desc = $collectedClasses.TransitionEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TransitionEvent.prototype = $desc;
  function UIEvent() {
  }
  UIEvent.builtin$cls = "UIEvent";
  if (!"name" in UIEvent)
    UIEvent.name = "UIEvent";
  $desc = $collectedClasses.UIEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UIEvent.prototype = $desc;
  function UListElement() {
  }
  UListElement.builtin$cls = "UListElement";
  if (!"name" in UListElement)
    UListElement.name = "UListElement";
  $desc = $collectedClasses.UListElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UListElement.prototype = $desc;
  function UnknownElement() {
  }
  UnknownElement.builtin$cls = "UnknownElement";
  if (!"name" in UnknownElement)
    UnknownElement.name = "UnknownElement";
  $desc = $collectedClasses.UnknownElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UnknownElement.prototype = $desc;
  function VideoElement() {
  }
  VideoElement.builtin$cls = "VideoElement";
  if (!"name" in VideoElement)
    VideoElement.name = "VideoElement";
  $desc = $collectedClasses.VideoElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  VideoElement.prototype = $desc;
  function WheelEvent() {
  }
  WheelEvent.builtin$cls = "WheelEvent";
  if (!"name" in WheelEvent)
    WheelEvent.name = "WheelEvent";
  $desc = $collectedClasses.WheelEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  WheelEvent.prototype = $desc;
  function Window() {
  }
  Window.builtin$cls = "Window";
  if (!"name" in Window)
    Window.name = "Window";
  $desc = $collectedClasses.Window;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Window.prototype = $desc;
  function _Attr() {
  }
  _Attr.builtin$cls = "_Attr";
  if (!"name" in _Attr)
    _Attr.name = "_Attr";
  $desc = $collectedClasses._Attr;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Attr.prototype = $desc;
  function _ClientRect() {
  }
  _ClientRect.builtin$cls = "_ClientRect";
  if (!"name" in _ClientRect)
    _ClientRect.name = "_ClientRect";
  $desc = $collectedClasses._ClientRect;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _ClientRect.prototype = $desc;
  _ClientRect.prototype.get$height = function(receiver) {
    return receiver.height;
  };
  _ClientRect.prototype.get$left = function(receiver) {
    return receiver.left;
  };
  _ClientRect.prototype.get$top = function(receiver) {
    return receiver.top;
  };
  _ClientRect.prototype.get$width = function(receiver) {
    return receiver.width;
  };
  function _Entity() {
  }
  _Entity.builtin$cls = "_Entity";
  if (!"name" in _Entity)
    _Entity.name = "_Entity";
  $desc = $collectedClasses._Entity;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Entity.prototype = $desc;
  function _HTMLAppletElement() {
  }
  _HTMLAppletElement.builtin$cls = "_HTMLAppletElement";
  if (!"name" in _HTMLAppletElement)
    _HTMLAppletElement.name = "_HTMLAppletElement";
  $desc = $collectedClasses._HTMLAppletElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HTMLAppletElement.prototype = $desc;
  function _HTMLBaseFontElement() {
  }
  _HTMLBaseFontElement.builtin$cls = "_HTMLBaseFontElement";
  if (!"name" in _HTMLBaseFontElement)
    _HTMLBaseFontElement.name = "_HTMLBaseFontElement";
  $desc = $collectedClasses._HTMLBaseFontElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HTMLBaseFontElement.prototype = $desc;
  function _HTMLDirectoryElement() {
  }
  _HTMLDirectoryElement.builtin$cls = "_HTMLDirectoryElement";
  if (!"name" in _HTMLDirectoryElement)
    _HTMLDirectoryElement.name = "_HTMLDirectoryElement";
  $desc = $collectedClasses._HTMLDirectoryElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HTMLDirectoryElement.prototype = $desc;
  function _HTMLFontElement() {
  }
  _HTMLFontElement.builtin$cls = "_HTMLFontElement";
  if (!"name" in _HTMLFontElement)
    _HTMLFontElement.name = "_HTMLFontElement";
  $desc = $collectedClasses._HTMLFontElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HTMLFontElement.prototype = $desc;
  function _HTMLFrameElement() {
  }
  _HTMLFrameElement.builtin$cls = "_HTMLFrameElement";
  if (!"name" in _HTMLFrameElement)
    _HTMLFrameElement.name = "_HTMLFrameElement";
  $desc = $collectedClasses._HTMLFrameElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HTMLFrameElement.prototype = $desc;
  function _HTMLFrameSetElement() {
  }
  _HTMLFrameSetElement.builtin$cls = "_HTMLFrameSetElement";
  if (!"name" in _HTMLFrameSetElement)
    _HTMLFrameSetElement.name = "_HTMLFrameSetElement";
  $desc = $collectedClasses._HTMLFrameSetElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HTMLFrameSetElement.prototype = $desc;
  function _HTMLMarqueeElement() {
  }
  _HTMLMarqueeElement.builtin$cls = "_HTMLMarqueeElement";
  if (!"name" in _HTMLMarqueeElement)
    _HTMLMarqueeElement.name = "_HTMLMarqueeElement";
  $desc = $collectedClasses._HTMLMarqueeElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HTMLMarqueeElement.prototype = $desc;
  function _MutationEvent() {
  }
  _MutationEvent.builtin$cls = "_MutationEvent";
  if (!"name" in _MutationEvent)
    _MutationEvent.name = "_MutationEvent";
  $desc = $collectedClasses._MutationEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _MutationEvent.prototype = $desc;
  function _Notation() {
  }
  _Notation.builtin$cls = "_Notation";
  if (!"name" in _Notation)
    _Notation.name = "_Notation";
  $desc = $collectedClasses._Notation;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Notation.prototype = $desc;
  function _XMLHttpRequestProgressEvent() {
  }
  _XMLHttpRequestProgressEvent.builtin$cls = "_XMLHttpRequestProgressEvent";
  if (!"name" in _XMLHttpRequestProgressEvent)
    _XMLHttpRequestProgressEvent.name = "_XMLHttpRequestProgressEvent";
  $desc = $collectedClasses._XMLHttpRequestProgressEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _XMLHttpRequestProgressEvent.prototype = $desc;
  function VersionChangeEvent() {
  }
  VersionChangeEvent.builtin$cls = "VersionChangeEvent";
  if (!"name" in VersionChangeEvent)
    VersionChangeEvent.name = "VersionChangeEvent";
  $desc = $collectedClasses.VersionChangeEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  VersionChangeEvent.prototype = $desc;
  function AElement() {
  }
  AElement.builtin$cls = "AElement";
  if (!"name" in AElement)
    AElement.name = "AElement";
  $desc = $collectedClasses.AElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AElement.prototype = $desc;
  function AltGlyphElement() {
  }
  AltGlyphElement.builtin$cls = "AltGlyphElement";
  if (!"name" in AltGlyphElement)
    AltGlyphElement.name = "AltGlyphElement";
  $desc = $collectedClasses.AltGlyphElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AltGlyphElement.prototype = $desc;
  function AnimateElement() {
  }
  AnimateElement.builtin$cls = "AnimateElement";
  if (!"name" in AnimateElement)
    AnimateElement.name = "AnimateElement";
  $desc = $collectedClasses.AnimateElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimateElement.prototype = $desc;
  function AnimateMotionElement() {
  }
  AnimateMotionElement.builtin$cls = "AnimateMotionElement";
  if (!"name" in AnimateMotionElement)
    AnimateMotionElement.name = "AnimateMotionElement";
  $desc = $collectedClasses.AnimateMotionElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimateMotionElement.prototype = $desc;
  function AnimateTransformElement() {
  }
  AnimateTransformElement.builtin$cls = "AnimateTransformElement";
  if (!"name" in AnimateTransformElement)
    AnimateTransformElement.name = "AnimateTransformElement";
  $desc = $collectedClasses.AnimateTransformElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimateTransformElement.prototype = $desc;
  function AnimatedLength() {
  }
  AnimatedLength.builtin$cls = "AnimatedLength";
  if (!"name" in AnimatedLength)
    AnimatedLength.name = "AnimatedLength";
  $desc = $collectedClasses.AnimatedLength;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimatedLength.prototype = $desc;
  function AnimatedLengthList() {
  }
  AnimatedLengthList.builtin$cls = "AnimatedLengthList";
  if (!"name" in AnimatedLengthList)
    AnimatedLengthList.name = "AnimatedLengthList";
  $desc = $collectedClasses.AnimatedLengthList;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimatedLengthList.prototype = $desc;
  function AnimatedNumber() {
  }
  AnimatedNumber.builtin$cls = "AnimatedNumber";
  if (!"name" in AnimatedNumber)
    AnimatedNumber.name = "AnimatedNumber";
  $desc = $collectedClasses.AnimatedNumber;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimatedNumber.prototype = $desc;
  function AnimatedNumberList() {
  }
  AnimatedNumberList.builtin$cls = "AnimatedNumberList";
  if (!"name" in AnimatedNumberList)
    AnimatedNumberList.name = "AnimatedNumberList";
  $desc = $collectedClasses.AnimatedNumberList;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimatedNumberList.prototype = $desc;
  function AnimationElement() {
  }
  AnimationElement.builtin$cls = "AnimationElement";
  if (!"name" in AnimationElement)
    AnimationElement.name = "AnimationElement";
  $desc = $collectedClasses.AnimationElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AnimationElement.prototype = $desc;
  function CircleElement() {
  }
  CircleElement.builtin$cls = "CircleElement";
  if (!"name" in CircleElement)
    CircleElement.name = "CircleElement";
  $desc = $collectedClasses.CircleElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CircleElement.prototype = $desc;
  function ClipPathElement() {
  }
  ClipPathElement.builtin$cls = "ClipPathElement";
  if (!"name" in ClipPathElement)
    ClipPathElement.name = "ClipPathElement";
  $desc = $collectedClasses.ClipPathElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ClipPathElement.prototype = $desc;
  function DefsElement() {
  }
  DefsElement.builtin$cls = "DefsElement";
  if (!"name" in DefsElement)
    DefsElement.name = "DefsElement";
  $desc = $collectedClasses.DefsElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DefsElement.prototype = $desc;
  function DescElement() {
  }
  DescElement.builtin$cls = "DescElement";
  if (!"name" in DescElement)
    DescElement.name = "DescElement";
  $desc = $collectedClasses.DescElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DescElement.prototype = $desc;
  function EllipseElement() {
  }
  EllipseElement.builtin$cls = "EllipseElement";
  if (!"name" in EllipseElement)
    EllipseElement.name = "EllipseElement";
  $desc = $collectedClasses.EllipseElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  EllipseElement.prototype = $desc;
  function FEBlendElement() {
  }
  FEBlendElement.builtin$cls = "FEBlendElement";
  if (!"name" in FEBlendElement)
    FEBlendElement.name = "FEBlendElement";
  $desc = $collectedClasses.FEBlendElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEBlendElement.prototype = $desc;
  FEBlendElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEBlendElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEColorMatrixElement() {
  }
  FEColorMatrixElement.builtin$cls = "FEColorMatrixElement";
  if (!"name" in FEColorMatrixElement)
    FEColorMatrixElement.name = "FEColorMatrixElement";
  $desc = $collectedClasses.FEColorMatrixElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEColorMatrixElement.prototype = $desc;
  FEColorMatrixElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEColorMatrixElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEComponentTransferElement() {
  }
  FEComponentTransferElement.builtin$cls = "FEComponentTransferElement";
  if (!"name" in FEComponentTransferElement)
    FEComponentTransferElement.name = "FEComponentTransferElement";
  $desc = $collectedClasses.FEComponentTransferElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEComponentTransferElement.prototype = $desc;
  FEComponentTransferElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEComponentTransferElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FECompositeElement() {
  }
  FECompositeElement.builtin$cls = "FECompositeElement";
  if (!"name" in FECompositeElement)
    FECompositeElement.name = "FECompositeElement";
  $desc = $collectedClasses.FECompositeElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FECompositeElement.prototype = $desc;
  FECompositeElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FECompositeElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEConvolveMatrixElement() {
  }
  FEConvolveMatrixElement.builtin$cls = "FEConvolveMatrixElement";
  if (!"name" in FEConvolveMatrixElement)
    FEConvolveMatrixElement.name = "FEConvolveMatrixElement";
  $desc = $collectedClasses.FEConvolveMatrixElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEConvolveMatrixElement.prototype = $desc;
  FEConvolveMatrixElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEConvolveMatrixElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEDiffuseLightingElement() {
  }
  FEDiffuseLightingElement.builtin$cls = "FEDiffuseLightingElement";
  if (!"name" in FEDiffuseLightingElement)
    FEDiffuseLightingElement.name = "FEDiffuseLightingElement";
  $desc = $collectedClasses.FEDiffuseLightingElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEDiffuseLightingElement.prototype = $desc;
  FEDiffuseLightingElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEDiffuseLightingElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEDisplacementMapElement() {
  }
  FEDisplacementMapElement.builtin$cls = "FEDisplacementMapElement";
  if (!"name" in FEDisplacementMapElement)
    FEDisplacementMapElement.name = "FEDisplacementMapElement";
  $desc = $collectedClasses.FEDisplacementMapElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEDisplacementMapElement.prototype = $desc;
  FEDisplacementMapElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEDisplacementMapElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEDistantLightElement() {
  }
  FEDistantLightElement.builtin$cls = "FEDistantLightElement";
  if (!"name" in FEDistantLightElement)
    FEDistantLightElement.name = "FEDistantLightElement";
  $desc = $collectedClasses.FEDistantLightElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEDistantLightElement.prototype = $desc;
  function FEFloodElement() {
  }
  FEFloodElement.builtin$cls = "FEFloodElement";
  if (!"name" in FEFloodElement)
    FEFloodElement.name = "FEFloodElement";
  $desc = $collectedClasses.FEFloodElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEFloodElement.prototype = $desc;
  FEFloodElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEFloodElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEFuncAElement() {
  }
  FEFuncAElement.builtin$cls = "FEFuncAElement";
  if (!"name" in FEFuncAElement)
    FEFuncAElement.name = "FEFuncAElement";
  $desc = $collectedClasses.FEFuncAElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEFuncAElement.prototype = $desc;
  function FEFuncBElement() {
  }
  FEFuncBElement.builtin$cls = "FEFuncBElement";
  if (!"name" in FEFuncBElement)
    FEFuncBElement.name = "FEFuncBElement";
  $desc = $collectedClasses.FEFuncBElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEFuncBElement.prototype = $desc;
  function FEFuncGElement() {
  }
  FEFuncGElement.builtin$cls = "FEFuncGElement";
  if (!"name" in FEFuncGElement)
    FEFuncGElement.name = "FEFuncGElement";
  $desc = $collectedClasses.FEFuncGElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEFuncGElement.prototype = $desc;
  function FEFuncRElement() {
  }
  FEFuncRElement.builtin$cls = "FEFuncRElement";
  if (!"name" in FEFuncRElement)
    FEFuncRElement.name = "FEFuncRElement";
  $desc = $collectedClasses.FEFuncRElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEFuncRElement.prototype = $desc;
  function FEGaussianBlurElement() {
  }
  FEGaussianBlurElement.builtin$cls = "FEGaussianBlurElement";
  if (!"name" in FEGaussianBlurElement)
    FEGaussianBlurElement.name = "FEGaussianBlurElement";
  $desc = $collectedClasses.FEGaussianBlurElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEGaussianBlurElement.prototype = $desc;
  FEGaussianBlurElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEGaussianBlurElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEImageElement() {
  }
  FEImageElement.builtin$cls = "FEImageElement";
  if (!"name" in FEImageElement)
    FEImageElement.name = "FEImageElement";
  $desc = $collectedClasses.FEImageElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEImageElement.prototype = $desc;
  FEImageElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEImageElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEMergeElement() {
  }
  FEMergeElement.builtin$cls = "FEMergeElement";
  if (!"name" in FEMergeElement)
    FEMergeElement.name = "FEMergeElement";
  $desc = $collectedClasses.FEMergeElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEMergeElement.prototype = $desc;
  FEMergeElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEMergeElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEMergeNodeElement() {
  }
  FEMergeNodeElement.builtin$cls = "FEMergeNodeElement";
  if (!"name" in FEMergeNodeElement)
    FEMergeNodeElement.name = "FEMergeNodeElement";
  $desc = $collectedClasses.FEMergeNodeElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEMergeNodeElement.prototype = $desc;
  function FEMorphologyElement() {
  }
  FEMorphologyElement.builtin$cls = "FEMorphologyElement";
  if (!"name" in FEMorphologyElement)
    FEMorphologyElement.name = "FEMorphologyElement";
  $desc = $collectedClasses.FEMorphologyElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEMorphologyElement.prototype = $desc;
  FEMorphologyElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEMorphologyElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEOffsetElement() {
  }
  FEOffsetElement.builtin$cls = "FEOffsetElement";
  if (!"name" in FEOffsetElement)
    FEOffsetElement.name = "FEOffsetElement";
  $desc = $collectedClasses.FEOffsetElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEOffsetElement.prototype = $desc;
  FEOffsetElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEOffsetElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FEPointLightElement() {
  }
  FEPointLightElement.builtin$cls = "FEPointLightElement";
  if (!"name" in FEPointLightElement)
    FEPointLightElement.name = "FEPointLightElement";
  $desc = $collectedClasses.FEPointLightElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FEPointLightElement.prototype = $desc;
  FEPointLightElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FEPointLightElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FESpecularLightingElement() {
  }
  FESpecularLightingElement.builtin$cls = "FESpecularLightingElement";
  if (!"name" in FESpecularLightingElement)
    FESpecularLightingElement.name = "FESpecularLightingElement";
  $desc = $collectedClasses.FESpecularLightingElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FESpecularLightingElement.prototype = $desc;
  FESpecularLightingElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FESpecularLightingElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FESpotLightElement() {
  }
  FESpotLightElement.builtin$cls = "FESpotLightElement";
  if (!"name" in FESpotLightElement)
    FESpotLightElement.name = "FESpotLightElement";
  $desc = $collectedClasses.FESpotLightElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FESpotLightElement.prototype = $desc;
  FESpotLightElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FESpotLightElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FETileElement() {
  }
  FETileElement.builtin$cls = "FETileElement";
  if (!"name" in FETileElement)
    FETileElement.name = "FETileElement";
  $desc = $collectedClasses.FETileElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FETileElement.prototype = $desc;
  FETileElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FETileElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FETurbulenceElement() {
  }
  FETurbulenceElement.builtin$cls = "FETurbulenceElement";
  if (!"name" in FETurbulenceElement)
    FETurbulenceElement.name = "FETurbulenceElement";
  $desc = $collectedClasses.FETurbulenceElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FETurbulenceElement.prototype = $desc;
  FETurbulenceElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FETurbulenceElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function FilterElement() {
  }
  FilterElement.builtin$cls = "FilterElement";
  if (!"name" in FilterElement)
    FilterElement.name = "FilterElement";
  $desc = $collectedClasses.FilterElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FilterElement.prototype = $desc;
  FilterElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  FilterElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function ForeignObjectElement() {
  }
  ForeignObjectElement.builtin$cls = "ForeignObjectElement";
  if (!"name" in ForeignObjectElement)
    ForeignObjectElement.name = "ForeignObjectElement";
  $desc = $collectedClasses.ForeignObjectElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ForeignObjectElement.prototype = $desc;
  ForeignObjectElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  ForeignObjectElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function GElement() {
  }
  GElement.builtin$cls = "GElement";
  if (!"name" in GElement)
    GElement.name = "GElement";
  $desc = $collectedClasses.GElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  GElement.prototype = $desc;
  function GraphicsElement() {
  }
  GraphicsElement.builtin$cls = "GraphicsElement";
  if (!"name" in GraphicsElement)
    GraphicsElement.name = "GraphicsElement";
  $desc = $collectedClasses.GraphicsElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  GraphicsElement.prototype = $desc;
  function ImageElement0() {
  }
  ImageElement0.builtin$cls = "ImageElement0";
  if (!"name" in ImageElement0)
    ImageElement0.name = "ImageElement0";
  $desc = $collectedClasses.ImageElement0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ImageElement0.prototype = $desc;
  ImageElement0.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  ImageElement0.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function LineElement() {
  }
  LineElement.builtin$cls = "LineElement";
  if (!"name" in LineElement)
    LineElement.name = "LineElement";
  $desc = $collectedClasses.LineElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LineElement.prototype = $desc;
  function LinearGradientElement() {
  }
  LinearGradientElement.builtin$cls = "LinearGradientElement";
  if (!"name" in LinearGradientElement)
    LinearGradientElement.name = "LinearGradientElement";
  $desc = $collectedClasses.LinearGradientElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LinearGradientElement.prototype = $desc;
  function MarkerElement() {
  }
  MarkerElement.builtin$cls = "MarkerElement";
  if (!"name" in MarkerElement)
    MarkerElement.name = "MarkerElement";
  $desc = $collectedClasses.MarkerElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MarkerElement.prototype = $desc;
  function MaskElement() {
  }
  MaskElement.builtin$cls = "MaskElement";
  if (!"name" in MaskElement)
    MaskElement.name = "MaskElement";
  $desc = $collectedClasses.MaskElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MaskElement.prototype = $desc;
  MaskElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  MaskElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function MetadataElement() {
  }
  MetadataElement.builtin$cls = "MetadataElement";
  if (!"name" in MetadataElement)
    MetadataElement.name = "MetadataElement";
  $desc = $collectedClasses.MetadataElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MetadataElement.prototype = $desc;
  function PathElement() {
  }
  PathElement.builtin$cls = "PathElement";
  if (!"name" in PathElement)
    PathElement.name = "PathElement";
  $desc = $collectedClasses.PathElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PathElement.prototype = $desc;
  function PatternElement() {
  }
  PatternElement.builtin$cls = "PatternElement";
  if (!"name" in PatternElement)
    PatternElement.name = "PatternElement";
  $desc = $collectedClasses.PatternElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PatternElement.prototype = $desc;
  PatternElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  PatternElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function PolygonElement() {
  }
  PolygonElement.builtin$cls = "PolygonElement";
  if (!"name" in PolygonElement)
    PolygonElement.name = "PolygonElement";
  $desc = $collectedClasses.PolygonElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PolygonElement.prototype = $desc;
  function PolylineElement() {
  }
  PolylineElement.builtin$cls = "PolylineElement";
  if (!"name" in PolylineElement)
    PolylineElement.name = "PolylineElement";
  $desc = $collectedClasses.PolylineElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PolylineElement.prototype = $desc;
  function RadialGradientElement() {
  }
  RadialGradientElement.builtin$cls = "RadialGradientElement";
  if (!"name" in RadialGradientElement)
    RadialGradientElement.name = "RadialGradientElement";
  $desc = $collectedClasses.RadialGradientElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RadialGradientElement.prototype = $desc;
  function RectElement() {
  }
  RectElement.builtin$cls = "RectElement";
  if (!"name" in RectElement)
    RectElement.name = "RectElement";
  $desc = $collectedClasses.RectElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RectElement.prototype = $desc;
  RectElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  RectElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function ScriptElement0() {
  }
  ScriptElement0.builtin$cls = "ScriptElement0";
  if (!"name" in ScriptElement0)
    ScriptElement0.name = "ScriptElement0";
  $desc = $collectedClasses.ScriptElement0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ScriptElement0.prototype = $desc;
  function SetElement() {
  }
  SetElement.builtin$cls = "SetElement";
  if (!"name" in SetElement)
    SetElement.name = "SetElement";
  $desc = $collectedClasses.SetElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SetElement.prototype = $desc;
  function StopElement() {
  }
  StopElement.builtin$cls = "StopElement";
  if (!"name" in StopElement)
    StopElement.name = "StopElement";
  $desc = $collectedClasses.StopElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StopElement.prototype = $desc;
  function StyleElement0() {
  }
  StyleElement0.builtin$cls = "StyleElement0";
  if (!"name" in StyleElement0)
    StyleElement0.name = "StyleElement0";
  $desc = $collectedClasses.StyleElement0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StyleElement0.prototype = $desc;
  function SvgDocument() {
  }
  SvgDocument.builtin$cls = "SvgDocument";
  if (!"name" in SvgDocument)
    SvgDocument.name = "SvgDocument";
  $desc = $collectedClasses.SvgDocument;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SvgDocument.prototype = $desc;
  function SvgElement() {
  }
  SvgElement.builtin$cls = "SvgElement";
  if (!"name" in SvgElement)
    SvgElement.name = "SvgElement";
  $desc = $collectedClasses.SvgElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SvgElement.prototype = $desc;
  function SvgSvgElement() {
  }
  SvgSvgElement.builtin$cls = "SvgSvgElement";
  if (!"name" in SvgSvgElement)
    SvgSvgElement.name = "SvgSvgElement";
  $desc = $collectedClasses.SvgSvgElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SvgSvgElement.prototype = $desc;
  SvgSvgElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  SvgSvgElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function SwitchElement() {
  }
  SwitchElement.builtin$cls = "SwitchElement";
  if (!"name" in SwitchElement)
    SwitchElement.name = "SwitchElement";
  $desc = $collectedClasses.SwitchElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SwitchElement.prototype = $desc;
  function SymbolElement() {
  }
  SymbolElement.builtin$cls = "SymbolElement";
  if (!"name" in SymbolElement)
    SymbolElement.name = "SymbolElement";
  $desc = $collectedClasses.SymbolElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SymbolElement.prototype = $desc;
  function TSpanElement() {
  }
  TSpanElement.builtin$cls = "TSpanElement";
  if (!"name" in TSpanElement)
    TSpanElement.name = "TSpanElement";
  $desc = $collectedClasses.TSpanElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TSpanElement.prototype = $desc;
  function TextContentElement() {
  }
  TextContentElement.builtin$cls = "TextContentElement";
  if (!"name" in TextContentElement)
    TextContentElement.name = "TextContentElement";
  $desc = $collectedClasses.TextContentElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TextContentElement.prototype = $desc;
  function TextElement() {
  }
  TextElement.builtin$cls = "TextElement";
  if (!"name" in TextElement)
    TextElement.name = "TextElement";
  $desc = $collectedClasses.TextElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TextElement.prototype = $desc;
  function TextPathElement() {
  }
  TextPathElement.builtin$cls = "TextPathElement";
  if (!"name" in TextPathElement)
    TextPathElement.name = "TextPathElement";
  $desc = $collectedClasses.TextPathElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TextPathElement.prototype = $desc;
  function TextPositioningElement() {
  }
  TextPositioningElement.builtin$cls = "TextPositioningElement";
  if (!"name" in TextPositioningElement)
    TextPositioningElement.name = "TextPositioningElement";
  $desc = $collectedClasses.TextPositioningElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TextPositioningElement.prototype = $desc;
  TextPositioningElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  TextPositioningElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function TitleElement0() {
  }
  TitleElement0.builtin$cls = "TitleElement0";
  if (!"name" in TitleElement0)
    TitleElement0.name = "TitleElement0";
  $desc = $collectedClasses.TitleElement0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TitleElement0.prototype = $desc;
  function UseElement() {
  }
  UseElement.builtin$cls = "UseElement";
  if (!"name" in UseElement)
    UseElement.name = "UseElement";
  $desc = $collectedClasses.UseElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UseElement.prototype = $desc;
  UseElement.prototype.get$x = function(receiver) {
    return receiver.x;
  };
  UseElement.prototype.get$y = function(receiver) {
    return receiver.y;
  };
  function ViewElement() {
  }
  ViewElement.builtin$cls = "ViewElement";
  if (!"name" in ViewElement)
    ViewElement.name = "ViewElement";
  $desc = $collectedClasses.ViewElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ViewElement.prototype = $desc;
  function ZoomEvent() {
  }
  ZoomEvent.builtin$cls = "ZoomEvent";
  if (!"name" in ZoomEvent)
    ZoomEvent.name = "ZoomEvent";
  $desc = $collectedClasses.ZoomEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ZoomEvent.prototype = $desc;
  function _GradientElement() {
  }
  _GradientElement.builtin$cls = "_GradientElement";
  if (!"name" in _GradientElement)
    _GradientElement.name = "_GradientElement";
  $desc = $collectedClasses._GradientElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _GradientElement.prototype = $desc;
  function _SVGAltGlyphDefElement() {
  }
  _SVGAltGlyphDefElement.builtin$cls = "_SVGAltGlyphDefElement";
  if (!"name" in _SVGAltGlyphDefElement)
    _SVGAltGlyphDefElement.name = "_SVGAltGlyphDefElement";
  $desc = $collectedClasses._SVGAltGlyphDefElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGAltGlyphDefElement.prototype = $desc;
  function _SVGAltGlyphItemElement() {
  }
  _SVGAltGlyphItemElement.builtin$cls = "_SVGAltGlyphItemElement";
  if (!"name" in _SVGAltGlyphItemElement)
    _SVGAltGlyphItemElement.name = "_SVGAltGlyphItemElement";
  $desc = $collectedClasses._SVGAltGlyphItemElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGAltGlyphItemElement.prototype = $desc;
  function _SVGAnimateColorElement() {
  }
  _SVGAnimateColorElement.builtin$cls = "_SVGAnimateColorElement";
  if (!"name" in _SVGAnimateColorElement)
    _SVGAnimateColorElement.name = "_SVGAnimateColorElement";
  $desc = $collectedClasses._SVGAnimateColorElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGAnimateColorElement.prototype = $desc;
  function _SVGComponentTransferFunctionElement() {
  }
  _SVGComponentTransferFunctionElement.builtin$cls = "_SVGComponentTransferFunctionElement";
  if (!"name" in _SVGComponentTransferFunctionElement)
    _SVGComponentTransferFunctionElement.name = "_SVGComponentTransferFunctionElement";
  $desc = $collectedClasses._SVGComponentTransferFunctionElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGComponentTransferFunctionElement.prototype = $desc;
  function _SVGCursorElement() {
  }
  _SVGCursorElement.builtin$cls = "_SVGCursorElement";
  if (!"name" in _SVGCursorElement)
    _SVGCursorElement.name = "_SVGCursorElement";
  $desc = $collectedClasses._SVGCursorElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGCursorElement.prototype = $desc;
  function _SVGFEDropShadowElement() {
  }
  _SVGFEDropShadowElement.builtin$cls = "_SVGFEDropShadowElement";
  if (!"name" in _SVGFEDropShadowElement)
    _SVGFEDropShadowElement.name = "_SVGFEDropShadowElement";
  $desc = $collectedClasses._SVGFEDropShadowElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGFEDropShadowElement.prototype = $desc;
  function _SVGFontElement() {
  }
  _SVGFontElement.builtin$cls = "_SVGFontElement";
  if (!"name" in _SVGFontElement)
    _SVGFontElement.name = "_SVGFontElement";
  $desc = $collectedClasses._SVGFontElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGFontElement.prototype = $desc;
  function _SVGFontFaceElement() {
  }
  _SVGFontFaceElement.builtin$cls = "_SVGFontFaceElement";
  if (!"name" in _SVGFontFaceElement)
    _SVGFontFaceElement.name = "_SVGFontFaceElement";
  $desc = $collectedClasses._SVGFontFaceElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGFontFaceElement.prototype = $desc;
  function _SVGFontFaceFormatElement() {
  }
  _SVGFontFaceFormatElement.builtin$cls = "_SVGFontFaceFormatElement";
  if (!"name" in _SVGFontFaceFormatElement)
    _SVGFontFaceFormatElement.name = "_SVGFontFaceFormatElement";
  $desc = $collectedClasses._SVGFontFaceFormatElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGFontFaceFormatElement.prototype = $desc;
  function _SVGFontFaceNameElement() {
  }
  _SVGFontFaceNameElement.builtin$cls = "_SVGFontFaceNameElement";
  if (!"name" in _SVGFontFaceNameElement)
    _SVGFontFaceNameElement.name = "_SVGFontFaceNameElement";
  $desc = $collectedClasses._SVGFontFaceNameElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGFontFaceNameElement.prototype = $desc;
  function _SVGFontFaceSrcElement() {
  }
  _SVGFontFaceSrcElement.builtin$cls = "_SVGFontFaceSrcElement";
  if (!"name" in _SVGFontFaceSrcElement)
    _SVGFontFaceSrcElement.name = "_SVGFontFaceSrcElement";
  $desc = $collectedClasses._SVGFontFaceSrcElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGFontFaceSrcElement.prototype = $desc;
  function _SVGFontFaceUriElement() {
  }
  _SVGFontFaceUriElement.builtin$cls = "_SVGFontFaceUriElement";
  if (!"name" in _SVGFontFaceUriElement)
    _SVGFontFaceUriElement.name = "_SVGFontFaceUriElement";
  $desc = $collectedClasses._SVGFontFaceUriElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGFontFaceUriElement.prototype = $desc;
  function _SVGGlyphElement() {
  }
  _SVGGlyphElement.builtin$cls = "_SVGGlyphElement";
  if (!"name" in _SVGGlyphElement)
    _SVGGlyphElement.name = "_SVGGlyphElement";
  $desc = $collectedClasses._SVGGlyphElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGGlyphElement.prototype = $desc;
  function _SVGGlyphRefElement() {
  }
  _SVGGlyphRefElement.builtin$cls = "_SVGGlyphRefElement";
  if (!"name" in _SVGGlyphRefElement)
    _SVGGlyphRefElement.name = "_SVGGlyphRefElement";
  $desc = $collectedClasses._SVGGlyphRefElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGGlyphRefElement.prototype = $desc;
  function _SVGHKernElement() {
  }
  _SVGHKernElement.builtin$cls = "_SVGHKernElement";
  if (!"name" in _SVGHKernElement)
    _SVGHKernElement.name = "_SVGHKernElement";
  $desc = $collectedClasses._SVGHKernElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGHKernElement.prototype = $desc;
  function _SVGMPathElement() {
  }
  _SVGMPathElement.builtin$cls = "_SVGMPathElement";
  if (!"name" in _SVGMPathElement)
    _SVGMPathElement.name = "_SVGMPathElement";
  $desc = $collectedClasses._SVGMPathElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGMPathElement.prototype = $desc;
  function _SVGMissingGlyphElement() {
  }
  _SVGMissingGlyphElement.builtin$cls = "_SVGMissingGlyphElement";
  if (!"name" in _SVGMissingGlyphElement)
    _SVGMissingGlyphElement.name = "_SVGMissingGlyphElement";
  $desc = $collectedClasses._SVGMissingGlyphElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGMissingGlyphElement.prototype = $desc;
  function _SVGVKernElement() {
  }
  _SVGVKernElement.builtin$cls = "_SVGVKernElement";
  if (!"name" in _SVGVKernElement)
    _SVGVKernElement.name = "_SVGVKernElement";
  $desc = $collectedClasses._SVGVKernElement;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SVGVKernElement.prototype = $desc;
  function AudioProcessingEvent() {
  }
  AudioProcessingEvent.builtin$cls = "AudioProcessingEvent";
  if (!"name" in AudioProcessingEvent)
    AudioProcessingEvent.name = "AudioProcessingEvent";
  $desc = $collectedClasses.AudioProcessingEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  AudioProcessingEvent.prototype = $desc;
  function OfflineAudioCompletionEvent() {
  }
  OfflineAudioCompletionEvent.builtin$cls = "OfflineAudioCompletionEvent";
  if (!"name" in OfflineAudioCompletionEvent)
    OfflineAudioCompletionEvent.name = "OfflineAudioCompletionEvent";
  $desc = $collectedClasses.OfflineAudioCompletionEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  OfflineAudioCompletionEvent.prototype = $desc;
  function ContextEvent() {
  }
  ContextEvent.builtin$cls = "ContextEvent";
  if (!"name" in ContextEvent)
    ContextEvent.name = "ContextEvent";
  $desc = $collectedClasses.ContextEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ContextEvent.prototype = $desc;
  function SqlError() {
  }
  SqlError.builtin$cls = "SqlError";
  if (!"name" in SqlError)
    SqlError.name = "SqlError";
  $desc = $collectedClasses.SqlError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  SqlError.prototype = $desc;
  function TypedData() {
  }
  TypedData.builtin$cls = "TypedData";
  if (!"name" in TypedData)
    TypedData.name = "TypedData";
  $desc = $collectedClasses.TypedData;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TypedData.prototype = $desc;
  function Uint8List() {
  }
  Uint8List.builtin$cls = "Uint8List";
  if (!"name" in Uint8List)
    Uint8List.name = "Uint8List";
  $desc = $collectedClasses.Uint8List;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Uint8List.prototype = $desc;
  function JS_CONST(code) {
    this.code = code;
  }
  JS_CONST.builtin$cls = "JS_CONST";
  if (!"name" in JS_CONST)
    JS_CONST.name = "JS_CONST";
  $desc = $collectedClasses.JS_CONST;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JS_CONST.prototype = $desc;
  function Interceptor() {
  }
  Interceptor.builtin$cls = "Interceptor";
  if (!"name" in Interceptor)
    Interceptor.name = "Interceptor";
  $desc = $collectedClasses.Interceptor;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Interceptor.prototype = $desc;
  function JSBool() {
  }
  JSBool.builtin$cls = "bool";
  if (!"name" in JSBool)
    JSBool.name = "JSBool";
  $desc = $collectedClasses.JSBool;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JSBool.prototype = $desc;
  function JSNull() {
  }
  JSNull.builtin$cls = "JSNull";
  if (!"name" in JSNull)
    JSNull.name = "JSNull";
  $desc = $collectedClasses.JSNull;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JSNull.prototype = $desc;
  function JavaScriptObject() {
  }
  JavaScriptObject.builtin$cls = "JavaScriptObject";
  if (!"name" in JavaScriptObject)
    JavaScriptObject.name = "JavaScriptObject";
  $desc = $collectedClasses.JavaScriptObject;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JavaScriptObject.prototype = $desc;
  function PlainJavaScriptObject() {
  }
  PlainJavaScriptObject.builtin$cls = "PlainJavaScriptObject";
  if (!"name" in PlainJavaScriptObject)
    PlainJavaScriptObject.name = "PlainJavaScriptObject";
  $desc = $collectedClasses.PlainJavaScriptObject;
  if ($desc instanceof Array)
    $desc = $desc[1];
  PlainJavaScriptObject.prototype = $desc;
  function UnknownJavaScriptObject() {
  }
  UnknownJavaScriptObject.builtin$cls = "UnknownJavaScriptObject";
  if (!"name" in UnknownJavaScriptObject)
    UnknownJavaScriptObject.name = "UnknownJavaScriptObject";
  $desc = $collectedClasses.UnknownJavaScriptObject;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UnknownJavaScriptObject.prototype = $desc;
  function JSArray() {
  }
  JSArray.builtin$cls = "List";
  if (!"name" in JSArray)
    JSArray.name = "JSArray";
  $desc = $collectedClasses.JSArray;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JSArray.prototype = $desc;
  function JSNumber() {
  }
  JSNumber.builtin$cls = "num";
  if (!"name" in JSNumber)
    JSNumber.name = "JSNumber";
  $desc = $collectedClasses.JSNumber;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JSNumber.prototype = $desc;
  function JSInt() {
  }
  JSInt.builtin$cls = "int";
  if (!"name" in JSInt)
    JSInt.name = "JSInt";
  $desc = $collectedClasses.JSInt;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JSInt.prototype = $desc;
  function JSDouble() {
  }
  JSDouble.builtin$cls = "double";
  if (!"name" in JSDouble)
    JSDouble.name = "JSDouble";
  $desc = $collectedClasses.JSDouble;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JSDouble.prototype = $desc;
  function JSString() {
  }
  JSString.builtin$cls = "String";
  if (!"name" in JSString)
    JSString.name = "JSString";
  $desc = $collectedClasses.JSString;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JSString.prototype = $desc;
  function startRootIsolate_closure(entry_0) {
    this.entry_0 = entry_0;
  }
  startRootIsolate_closure.builtin$cls = "startRootIsolate_closure";
  if (!"name" in startRootIsolate_closure)
    startRootIsolate_closure.name = "startRootIsolate_closure";
  $desc = $collectedClasses.startRootIsolate_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  startRootIsolate_closure.prototype = $desc;
  function startRootIsolate_closure0(entry_1) {
    this.entry_1 = entry_1;
  }
  startRootIsolate_closure0.builtin$cls = "startRootIsolate_closure0";
  if (!"name" in startRootIsolate_closure0)
    startRootIsolate_closure0.name = "startRootIsolate_closure0";
  $desc = $collectedClasses.startRootIsolate_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  startRootIsolate_closure0.prototype = $desc;
  function _Manager(nextIsolateId, currentManagerId, nextManagerId, currentContext, rootContext, topEventLoop, fromCommandLine, isWorker, supportsWorkers, isolates, mainManager, managers, entry) {
    this.nextIsolateId = nextIsolateId;
    this.currentManagerId = currentManagerId;
    this.nextManagerId = nextManagerId;
    this.currentContext = currentContext;
    this.rootContext = rootContext;
    this.topEventLoop = topEventLoop;
    this.fromCommandLine = fromCommandLine;
    this.isWorker = isWorker;
    this.supportsWorkers = supportsWorkers;
    this.isolates = isolates;
    this.mainManager = mainManager;
    this.managers = managers;
    this.entry = entry;
  }
  _Manager.builtin$cls = "_Manager";
  if (!"name" in _Manager)
    _Manager.name = "_Manager";
  $desc = $collectedClasses._Manager;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Manager.prototype = $desc;
  function _IsolateContext(id, ports, weakPorts, isolateStatics) {
    this.id = id;
    this.ports = ports;
    this.weakPorts = weakPorts;
    this.isolateStatics = isolateStatics;
  }
  _IsolateContext.builtin$cls = "_IsolateContext";
  if (!"name" in _IsolateContext)
    _IsolateContext.name = "_IsolateContext";
  $desc = $collectedClasses._IsolateContext;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _IsolateContext.prototype = $desc;
  _IsolateContext.prototype.get$isolateStatics = function() {
    return this.isolateStatics;
  };
  function _EventLoop(events, activeTimerCount) {
    this.events = events;
    this.activeTimerCount = activeTimerCount;
  }
  _EventLoop.builtin$cls = "_EventLoop";
  if (!"name" in _EventLoop)
    _EventLoop.name = "_EventLoop";
  $desc = $collectedClasses._EventLoop;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _EventLoop.prototype = $desc;
  function _EventLoop__runHelper_next(this_0) {
    this.this_0 = this_0;
  }
  _EventLoop__runHelper_next.builtin$cls = "_EventLoop__runHelper_next";
  if (!"name" in _EventLoop__runHelper_next)
    _EventLoop__runHelper_next.name = "_EventLoop__runHelper_next";
  $desc = $collectedClasses._EventLoop__runHelper_next;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _EventLoop__runHelper_next.prototype = $desc;
  function _IsolateEvent(isolate, fn, message) {
    this.isolate = isolate;
    this.fn = fn;
    this.message = message;
  }
  _IsolateEvent.builtin$cls = "_IsolateEvent";
  if (!"name" in _IsolateEvent)
    _IsolateEvent.name = "_IsolateEvent";
  $desc = $collectedClasses._IsolateEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _IsolateEvent.prototype = $desc;
  function _MainManagerStub() {
  }
  _MainManagerStub.builtin$cls = "_MainManagerStub";
  if (!"name" in _MainManagerStub)
    _MainManagerStub.name = "_MainManagerStub";
  $desc = $collectedClasses._MainManagerStub;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _MainManagerStub.prototype = $desc;
  function IsolateNatives__processWorkerMessage_closure(entryPoint_0, args_1, message_2, isSpawnUri_3, replyTo_4) {
    this.entryPoint_0 = entryPoint_0;
    this.args_1 = args_1;
    this.message_2 = message_2;
    this.isSpawnUri_3 = isSpawnUri_3;
    this.replyTo_4 = replyTo_4;
  }
  IsolateNatives__processWorkerMessage_closure.builtin$cls = "IsolateNatives__processWorkerMessage_closure";
  if (!"name" in IsolateNatives__processWorkerMessage_closure)
    IsolateNatives__processWorkerMessage_closure.name = "IsolateNatives__processWorkerMessage_closure";
  $desc = $collectedClasses.IsolateNatives__processWorkerMessage_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  IsolateNatives__processWorkerMessage_closure.prototype = $desc;
  function _BaseSendPort() {
  }
  _BaseSendPort.builtin$cls = "_BaseSendPort";
  if (!"name" in _BaseSendPort)
    _BaseSendPort.name = "_BaseSendPort";
  $desc = $collectedClasses._BaseSendPort;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BaseSendPort.prototype = $desc;
  function _NativeJsSendPort(_receivePort, _isolateId) {
    this._receivePort = _receivePort;
    this._isolateId = _isolateId;
  }
  _NativeJsSendPort.builtin$cls = "_NativeJsSendPort";
  if (!"name" in _NativeJsSendPort)
    _NativeJsSendPort.name = "_NativeJsSendPort";
  $desc = $collectedClasses._NativeJsSendPort;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _NativeJsSendPort.prototype = $desc;
  function _NativeJsSendPort_send_closure(box_0, this_1, shouldSerialize_2) {
    this.box_0 = box_0;
    this.this_1 = this_1;
    this.shouldSerialize_2 = shouldSerialize_2;
  }
  _NativeJsSendPort_send_closure.builtin$cls = "_NativeJsSendPort_send_closure";
  if (!"name" in _NativeJsSendPort_send_closure)
    _NativeJsSendPort_send_closure.name = "_NativeJsSendPort_send_closure";
  $desc = $collectedClasses._NativeJsSendPort_send_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _NativeJsSendPort_send_closure.prototype = $desc;
  function _WorkerSendPort(_workerId, _receivePortId, _isolateId) {
    this._workerId = _workerId;
    this._receivePortId = _receivePortId;
    this._isolateId = _isolateId;
  }
  _WorkerSendPort.builtin$cls = "_WorkerSendPort";
  if (!"name" in _WorkerSendPort)
    _WorkerSendPort.name = "_WorkerSendPort";
  $desc = $collectedClasses._WorkerSendPort;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _WorkerSendPort.prototype = $desc;
  function RawReceivePortImpl(_id, _handler, _isClosed) {
    this._id = _id;
    this._handler = _handler;
    this._isClosed = _isClosed;
  }
  RawReceivePortImpl.builtin$cls = "RawReceivePortImpl";
  if (!"name" in RawReceivePortImpl)
    RawReceivePortImpl.name = "RawReceivePortImpl";
  $desc = $collectedClasses.RawReceivePortImpl;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RawReceivePortImpl.prototype = $desc;
  RawReceivePortImpl.prototype.get$_id = function() {
    return this._id;
  };
  RawReceivePortImpl.prototype.get$_isClosed = function() {
    return this._isClosed;
  };
  function ReceivePortImpl(_rawPort, _controller) {
    this._rawPort = _rawPort;
    this._controller = _controller;
  }
  ReceivePortImpl.builtin$cls = "ReceivePortImpl";
  if (!"name" in ReceivePortImpl)
    ReceivePortImpl.name = "ReceivePortImpl";
  $desc = $collectedClasses.ReceivePortImpl;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ReceivePortImpl.prototype = $desc;
  function _JsSerializer(_nextFreeRefId, _visited) {
    this._nextFreeRefId = _nextFreeRefId;
    this._visited = _visited;
  }
  _JsSerializer.builtin$cls = "_JsSerializer";
  if (!"name" in _JsSerializer)
    _JsSerializer.name = "_JsSerializer";
  $desc = $collectedClasses._JsSerializer;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _JsSerializer.prototype = $desc;
  function _JsCopier(_visited) {
    this._visited = _visited;
  }
  _JsCopier.builtin$cls = "_JsCopier";
  if (!"name" in _JsCopier)
    _JsCopier.name = "_JsCopier";
  $desc = $collectedClasses._JsCopier;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _JsCopier.prototype = $desc;
  function _JsDeserializer(_deserialized) {
    this._deserialized = _deserialized;
  }
  _JsDeserializer.builtin$cls = "_JsDeserializer";
  if (!"name" in _JsDeserializer)
    _JsDeserializer.name = "_JsDeserializer";
  $desc = $collectedClasses._JsDeserializer;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _JsDeserializer.prototype = $desc;
  function _JsVisitedMap(tagged) {
    this.tagged = tagged;
  }
  _JsVisitedMap.builtin$cls = "_JsVisitedMap";
  if (!"name" in _JsVisitedMap)
    _JsVisitedMap.name = "_JsVisitedMap";
  $desc = $collectedClasses._JsVisitedMap;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _JsVisitedMap.prototype = $desc;
  function _MessageTraverserVisitedMap() {
  }
  _MessageTraverserVisitedMap.builtin$cls = "_MessageTraverserVisitedMap";
  if (!"name" in _MessageTraverserVisitedMap)
    _MessageTraverserVisitedMap.name = "_MessageTraverserVisitedMap";
  $desc = $collectedClasses._MessageTraverserVisitedMap;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _MessageTraverserVisitedMap.prototype = $desc;
  function _MessageTraverser() {
  }
  _MessageTraverser.builtin$cls = "_MessageTraverser";
  if (!"name" in _MessageTraverser)
    _MessageTraverser.name = "_MessageTraverser";
  $desc = $collectedClasses._MessageTraverser;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _MessageTraverser.prototype = $desc;
  function _Copier() {
  }
  _Copier.builtin$cls = "_Copier";
  if (!"name" in _Copier)
    _Copier.name = "_Copier";
  $desc = $collectedClasses._Copier;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Copier.prototype = $desc;
  function _Copier_visitMap_closure(box_0, this_1) {
    this.box_0 = box_0;
    this.this_1 = this_1;
  }
  _Copier_visitMap_closure.builtin$cls = "_Copier_visitMap_closure";
  if (!"name" in _Copier_visitMap_closure)
    _Copier_visitMap_closure.name = "_Copier_visitMap_closure";
  $desc = $collectedClasses._Copier_visitMap_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Copier_visitMap_closure.prototype = $desc;
  function _Serializer() {
  }
  _Serializer.builtin$cls = "_Serializer";
  if (!"name" in _Serializer)
    _Serializer.name = "_Serializer";
  $desc = $collectedClasses._Serializer;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Serializer.prototype = $desc;
  function _Deserializer() {
  }
  _Deserializer.builtin$cls = "_Deserializer";
  if (!"name" in _Deserializer)
    _Deserializer.name = "_Deserializer";
  $desc = $collectedClasses._Deserializer;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Deserializer.prototype = $desc;
  function TimerImpl(_once, _inEventLoop, _handle) {
    this._once = _once;
    this._inEventLoop = _inEventLoop;
    this._handle = _handle;
  }
  TimerImpl.builtin$cls = "TimerImpl";
  if (!"name" in TimerImpl)
    TimerImpl.name = "TimerImpl";
  $desc = $collectedClasses.TimerImpl;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TimerImpl.prototype = $desc;
  function TimerImpl_internalCallback(this_0, callback_1) {
    this.this_0 = this_0;
    this.callback_1 = callback_1;
  }
  TimerImpl_internalCallback.builtin$cls = "TimerImpl_internalCallback";
  if (!"name" in TimerImpl_internalCallback)
    TimerImpl_internalCallback.name = "TimerImpl_internalCallback";
  $desc = $collectedClasses.TimerImpl_internalCallback;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TimerImpl_internalCallback.prototype = $desc;
  function TimerImpl_internalCallback0(this_2, callback_3) {
    this.this_2 = this_2;
    this.callback_3 = callback_3;
  }
  TimerImpl_internalCallback0.builtin$cls = "TimerImpl_internalCallback0";
  if (!"name" in TimerImpl_internalCallback0)
    TimerImpl_internalCallback0.name = "TimerImpl_internalCallback0";
  $desc = $collectedClasses.TimerImpl_internalCallback0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TimerImpl_internalCallback0.prototype = $desc;
  function ReflectionInfo(jsFunction, data, isAccessor, requiredParameterCount, optionalParameterCount, areOptionalParametersNamed, functionType) {
    this.jsFunction = jsFunction;
    this.data = data;
    this.isAccessor = isAccessor;
    this.requiredParameterCount = requiredParameterCount;
    this.optionalParameterCount = optionalParameterCount;
    this.areOptionalParametersNamed = areOptionalParametersNamed;
    this.functionType = functionType;
  }
  ReflectionInfo.builtin$cls = "ReflectionInfo";
  if (!"name" in ReflectionInfo)
    ReflectionInfo.name = "ReflectionInfo";
  $desc = $collectedClasses.ReflectionInfo;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ReflectionInfo.prototype = $desc;
  function TypeErrorDecoder(_pattern, _arguments, _argumentsExpr, _expr, _method, _receiver) {
    this._pattern = _pattern;
    this._arguments = _arguments;
    this._argumentsExpr = _argumentsExpr;
    this._expr = _expr;
    this._method = _method;
    this._receiver = _receiver;
  }
  TypeErrorDecoder.builtin$cls = "TypeErrorDecoder";
  if (!"name" in TypeErrorDecoder)
    TypeErrorDecoder.name = "TypeErrorDecoder";
  $desc = $collectedClasses.TypeErrorDecoder;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TypeErrorDecoder.prototype = $desc;
  TypeErrorDecoder.prototype.get$_receiver = function() {
    return this._receiver;
  };
  function NullError(_message, _method) {
    this._message = _message;
    this._method = _method;
  }
  NullError.builtin$cls = "NullError";
  if (!"name" in NullError)
    NullError.name = "NullError";
  $desc = $collectedClasses.NullError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  NullError.prototype = $desc;
  function JsNoSuchMethodError(_message, _method, _receiver) {
    this._message = _message;
    this._method = _method;
    this._receiver = _receiver;
  }
  JsNoSuchMethodError.builtin$cls = "JsNoSuchMethodError";
  if (!"name" in JsNoSuchMethodError)
    JsNoSuchMethodError.name = "JsNoSuchMethodError";
  $desc = $collectedClasses.JsNoSuchMethodError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  JsNoSuchMethodError.prototype = $desc;
  JsNoSuchMethodError.prototype.get$_receiver = function() {
    return this._receiver;
  };
  function UnknownJsTypeError(_message) {
    this._message = _message;
  }
  UnknownJsTypeError.builtin$cls = "UnknownJsTypeError";
  if (!"name" in UnknownJsTypeError)
    UnknownJsTypeError.name = "UnknownJsTypeError";
  $desc = $collectedClasses.UnknownJsTypeError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UnknownJsTypeError.prototype = $desc;
  function unwrapException_saveStackTrace(ex_0) {
    this.ex_0 = ex_0;
  }
  unwrapException_saveStackTrace.builtin$cls = "unwrapException_saveStackTrace";
  if (!"name" in unwrapException_saveStackTrace)
    unwrapException_saveStackTrace.name = "unwrapException_saveStackTrace";
  $desc = $collectedClasses.unwrapException_saveStackTrace;
  if ($desc instanceof Array)
    $desc = $desc[1];
  unwrapException_saveStackTrace.prototype = $desc;
  function _StackTrace(_exception, _trace) {
    this._exception = _exception;
    this._trace = _trace;
  }
  _StackTrace.builtin$cls = "_StackTrace";
  if (!"name" in _StackTrace)
    _StackTrace.name = "_StackTrace";
  $desc = $collectedClasses._StackTrace;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StackTrace.prototype = $desc;
  function invokeClosure_closure(closure_0) {
    this.closure_0 = closure_0;
  }
  invokeClosure_closure.builtin$cls = "invokeClosure_closure";
  if (!"name" in invokeClosure_closure)
    invokeClosure_closure.name = "invokeClosure_closure";
  $desc = $collectedClasses.invokeClosure_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  invokeClosure_closure.prototype = $desc;
  function invokeClosure_closure0(closure_1, arg1_2) {
    this.closure_1 = closure_1;
    this.arg1_2 = arg1_2;
  }
  invokeClosure_closure0.builtin$cls = "invokeClosure_closure0";
  if (!"name" in invokeClosure_closure0)
    invokeClosure_closure0.name = "invokeClosure_closure0";
  $desc = $collectedClasses.invokeClosure_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  invokeClosure_closure0.prototype = $desc;
  function invokeClosure_closure1(closure_3, arg1_4, arg2_5) {
    this.closure_3 = closure_3;
    this.arg1_4 = arg1_4;
    this.arg2_5 = arg2_5;
  }
  invokeClosure_closure1.builtin$cls = "invokeClosure_closure1";
  if (!"name" in invokeClosure_closure1)
    invokeClosure_closure1.name = "invokeClosure_closure1";
  $desc = $collectedClasses.invokeClosure_closure1;
  if ($desc instanceof Array)
    $desc = $desc[1];
  invokeClosure_closure1.prototype = $desc;
  function invokeClosure_closure2(closure_6, arg1_7, arg2_8, arg3_9) {
    this.closure_6 = closure_6;
    this.arg1_7 = arg1_7;
    this.arg2_8 = arg2_8;
    this.arg3_9 = arg3_9;
  }
  invokeClosure_closure2.builtin$cls = "invokeClosure_closure2";
  if (!"name" in invokeClosure_closure2)
    invokeClosure_closure2.name = "invokeClosure_closure2";
  $desc = $collectedClasses.invokeClosure_closure2;
  if ($desc instanceof Array)
    $desc = $desc[1];
  invokeClosure_closure2.prototype = $desc;
  function invokeClosure_closure3(closure_10, arg1_11, arg2_12, arg3_13, arg4_14) {
    this.closure_10 = closure_10;
    this.arg1_11 = arg1_11;
    this.arg2_12 = arg2_12;
    this.arg3_13 = arg3_13;
    this.arg4_14 = arg4_14;
  }
  invokeClosure_closure3.builtin$cls = "invokeClosure_closure3";
  if (!"name" in invokeClosure_closure3)
    invokeClosure_closure3.name = "invokeClosure_closure3";
  $desc = $collectedClasses.invokeClosure_closure3;
  if ($desc instanceof Array)
    $desc = $desc[1];
  invokeClosure_closure3.prototype = $desc;
  function Closure() {
  }
  Closure.builtin$cls = "Closure";
  if (!"name" in Closure)
    Closure.name = "Closure";
  $desc = $collectedClasses.Closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Closure.prototype = $desc;
  function TearOffClosure() {
  }
  TearOffClosure.builtin$cls = "TearOffClosure";
  if (!"name" in TearOffClosure)
    TearOffClosure.name = "TearOffClosure";
  $desc = $collectedClasses.TearOffClosure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TearOffClosure.prototype = $desc;
  function BoundClosure(_self, _target, _receiver, __js_helper$_name) {
    this._self = _self;
    this._target = _target;
    this._receiver = _receiver;
    this.__js_helper$_name = __js_helper$_name;
  }
  BoundClosure.builtin$cls = "BoundClosure";
  if (!"name" in BoundClosure)
    BoundClosure.name = "BoundClosure";
  $desc = $collectedClasses.BoundClosure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  BoundClosure.prototype = $desc;
  BoundClosure.prototype.get$_self = function() {
    return this._self;
  };
  BoundClosure.prototype.get$_receiver = function() {
    return this._receiver;
  };
  function RuntimeError(message) {
    this.message = message;
  }
  RuntimeError.builtin$cls = "RuntimeError";
  if (!"name" in RuntimeError)
    RuntimeError.name = "RuntimeError";
  $desc = $collectedClasses.RuntimeError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RuntimeError.prototype = $desc;
  function RuntimeType() {
  }
  RuntimeType.builtin$cls = "RuntimeType";
  if (!"name" in RuntimeType)
    RuntimeType.name = "RuntimeType";
  $desc = $collectedClasses.RuntimeType;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RuntimeType.prototype = $desc;
  function RuntimeFunctionType(returnType, parameterTypes, optionalParameterTypes, namedParameters) {
    this.returnType = returnType;
    this.parameterTypes = parameterTypes;
    this.optionalParameterTypes = optionalParameterTypes;
    this.namedParameters = namedParameters;
  }
  RuntimeFunctionType.builtin$cls = "RuntimeFunctionType";
  if (!"name" in RuntimeFunctionType)
    RuntimeFunctionType.name = "RuntimeFunctionType";
  $desc = $collectedClasses.RuntimeFunctionType;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RuntimeFunctionType.prototype = $desc;
  function DynamicRuntimeType() {
  }
  DynamicRuntimeType.builtin$cls = "DynamicRuntimeType";
  if (!"name" in DynamicRuntimeType)
    DynamicRuntimeType.name = "DynamicRuntimeType";
  $desc = $collectedClasses.DynamicRuntimeType;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DynamicRuntimeType.prototype = $desc;
  function TypeImpl(_typeName, _unmangledName) {
    this._typeName = _typeName;
    this._unmangledName = _unmangledName;
  }
  TypeImpl.builtin$cls = "TypeImpl";
  if (!"name" in TypeImpl)
    TypeImpl.name = "TypeImpl";
  $desc = $collectedClasses.TypeImpl;
  if ($desc instanceof Array)
    $desc = $desc[1];
  TypeImpl.prototype = $desc;
  function initHooks_closure(getTag_0) {
    this.getTag_0 = getTag_0;
  }
  initHooks_closure.builtin$cls = "initHooks_closure";
  if (!"name" in initHooks_closure)
    initHooks_closure.name = "initHooks_closure";
  $desc = $collectedClasses.initHooks_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  initHooks_closure.prototype = $desc;
  function initHooks_closure0(getUnknownTag_1) {
    this.getUnknownTag_1 = getUnknownTag_1;
  }
  initHooks_closure0.builtin$cls = "initHooks_closure0";
  if (!"name" in initHooks_closure0)
    initHooks_closure0.name = "initHooks_closure0";
  $desc = $collectedClasses.initHooks_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  initHooks_closure0.prototype = $desc;
  function initHooks_closure1(prototypeForTag_2) {
    this.prototypeForTag_2 = prototypeForTag_2;
  }
  initHooks_closure1.builtin$cls = "initHooks_closure1";
  if (!"name" in initHooks_closure1)
    initHooks_closure1.name = "initHooks_closure1";
  $desc = $collectedClasses.initHooks_closure1;
  if ($desc instanceof Array)
    $desc = $desc[1];
  initHooks_closure1.prototype = $desc;
  function Balls(root, lastTime, balls) {
    this.root = root;
    this.lastTime = lastTime;
    this.balls = balls;
  }
  Balls.builtin$cls = "Balls";
  if (!"name" in Balls)
    Balls.name = "Balls";
  $desc = $collectedClasses.Balls;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Balls.prototype = $desc;
  function Balls_tick_closure(delta_0) {
    this.delta_0 = delta_0;
  }
  Balls_tick_closure.builtin$cls = "Balls_tick_closure";
  if (!"name" in Balls_tick_closure)
    Balls_tick_closure.name = "Balls_tick_closure";
  $desc = $collectedClasses.Balls_tick_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Balls_tick_closure.prototype = $desc;
  function Balls_collideBalls_closure(this_0, delta_1) {
    this.this_0 = this_0;
    this.delta_1 = delta_1;
  }
  Balls_collideBalls_closure.builtin$cls = "Balls_collideBalls_closure";
  if (!"name" in Balls_collideBalls_closure)
    Balls_collideBalls_closure.name = "Balls_collideBalls_closure";
  $desc = $collectedClasses.Balls_collideBalls_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Balls_collideBalls_closure.prototype = $desc;
  function Balls_collideBalls__closure(this_2, delta_3, b0_4) {
    this.this_2 = this_2;
    this.delta_3 = delta_3;
    this.b0_4 = b0_4;
  }
  Balls_collideBalls__closure.builtin$cls = "Balls_collideBalls__closure";
  if (!"name" in Balls_collideBalls__closure)
    Balls_collideBalls__closure.name = "Balls_collideBalls__closure";
  $desc = $collectedClasses.Balls_collideBalls__closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Balls_collideBalls__closure.prototype = $desc;
  function Ball(root, elem, x, y, vx, vy, ax, ay, age) {
    this.root = root;
    this.elem = elem;
    this.x = x;
    this.y = y;
    this.vx = vx;
    this.vy = vy;
    this.ax = ax;
    this.ay = ay;
    this.age = age;
  }
  Ball.builtin$cls = "Ball";
  if (!"name" in Ball)
    Ball.name = "Ball";
  $desc = $collectedClasses.Ball;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Ball.prototype = $desc;
  Ball.prototype.get$x = function(receiver) {
    return this.x;
  };
  Ball.prototype.get$y = function(receiver) {
    return this.y;
  };
  Ball.prototype.get$vx = function() {
    return this.vx;
  };
  function CountDownClock(hours, minutes, seconds, displayedHour, displayedMinute, displayedSecond, balls) {
    this.hours = hours;
    this.minutes = minutes;
    this.seconds = seconds;
    this.displayedHour = displayedHour;
    this.displayedMinute = displayedMinute;
    this.displayedSecond = displayedSecond;
    this.balls = balls;
  }
  CountDownClock.builtin$cls = "CountDownClock";
  if (!"name" in CountDownClock)
    CountDownClock.name = "CountDownClock";
  $desc = $collectedClasses.CountDownClock;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CountDownClock.prototype = $desc;
  function ClockNumber(app, root, imgs, pixels, ballColor) {
    this.app = app;
    this.root = root;
    this.imgs = imgs;
    this.pixels = pixels;
    this.ballColor = ballColor;
  }
  ClockNumber.builtin$cls = "ClockNumber";
  if (!"name" in ClockNumber)
    ClockNumber.name = "ClockNumber";
  $desc = $collectedClasses.ClockNumber;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ClockNumber.prototype = $desc;
  function ClockNumber_setPixels_closure(this_0, img_1) {
    this.this_0 = this_0;
    this.img_1 = img_1;
  }
  ClockNumber_setPixels_closure.builtin$cls = "ClockNumber_setPixels_closure";
  if (!"name" in ClockNumber_setPixels_closure)
    ClockNumber_setPixels_closure.name = "ClockNumber_setPixels_closure";
  $desc = $collectedClasses.ClockNumber_setPixels_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ClockNumber_setPixels_closure.prototype = $desc;
  function Colon(root) {
    this.root = root;
  }
  Colon.builtin$cls = "Colon";
  if (!"name" in Colon)
    Colon.name = "Colon";
  $desc = $collectedClasses.Colon;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Colon.prototype = $desc;
  function ListIterator(_iterable, _dev$_length, _index, _dev$_current) {
    this._iterable = _iterable;
    this._dev$_length = _dev$_length;
    this._index = _index;
    this._dev$_current = _dev$_current;
  }
  ListIterator.builtin$cls = "ListIterator";
  if (!"name" in ListIterator)
    ListIterator.name = "ListIterator";
  $desc = $collectedClasses.ListIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ListIterator.prototype = $desc;
  function MappedIterable(_iterable, _f) {
    this._iterable = _iterable;
    this._f = _f;
  }
  MappedIterable.builtin$cls = "MappedIterable";
  if (!"name" in MappedIterable)
    MappedIterable.name = "MappedIterable";
  $desc = $collectedClasses.MappedIterable;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MappedIterable.prototype = $desc;
  function EfficientLengthMappedIterable(_iterable, _f) {
    this._iterable = _iterable;
    this._f = _f;
  }
  EfficientLengthMappedIterable.builtin$cls = "EfficientLengthMappedIterable";
  if (!"name" in EfficientLengthMappedIterable)
    EfficientLengthMappedIterable.name = "EfficientLengthMappedIterable";
  $desc = $collectedClasses.EfficientLengthMappedIterable;
  if ($desc instanceof Array)
    $desc = $desc[1];
  EfficientLengthMappedIterable.prototype = $desc;
  function MappedIterator(_dev$_current, _iterator, _f) {
    this._dev$_current = _dev$_current;
    this._iterator = _iterator;
    this._f = _f;
  }
  MappedIterator.builtin$cls = "MappedIterator";
  if (!"name" in MappedIterator)
    MappedIterator.name = "MappedIterator";
  $desc = $collectedClasses.MappedIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  MappedIterator.prototype = $desc;
  function FixedLengthListMixin() {
  }
  FixedLengthListMixin.builtin$cls = "FixedLengthListMixin";
  if (!"name" in FixedLengthListMixin)
    FixedLengthListMixin.name = "FixedLengthListMixin";
  $desc = $collectedClasses.FixedLengthListMixin;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FixedLengthListMixin.prototype = $desc;
  function _AsyncError(error, stackTrace) {
    this.error = error;
    this.stackTrace = stackTrace;
  }
  _AsyncError.builtin$cls = "_AsyncError";
  if (!"name" in _AsyncError)
    _AsyncError.name = "_AsyncError";
  $desc = $collectedClasses._AsyncError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _AsyncError.prototype = $desc;
  _AsyncError.prototype.get$error = function(receiver) {
    return this.error;
  };
  _AsyncError.prototype.get$stackTrace = function() {
    return this.stackTrace;
  };
  function Future() {
  }
  Future.builtin$cls = "Future";
  if (!"name" in Future)
    Future.name = "Future";
  $desc = $collectedClasses.Future;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Future.prototype = $desc;
  function _Future(_state, _zone, _resultOrListeners, _nextListener, _onValueCallback, _errorTestCallback, _onErrorCallback, _whenCompleteActionCallback) {
    this._state = _state;
    this._zone = _zone;
    this._resultOrListeners = _resultOrListeners;
    this._nextListener = _nextListener;
    this._onValueCallback = _onValueCallback;
    this._errorTestCallback = _errorTestCallback;
    this._onErrorCallback = _onErrorCallback;
    this._whenCompleteActionCallback = _whenCompleteActionCallback;
  }
  _Future.builtin$cls = "_Future";
  if (!"name" in _Future)
    _Future.name = "_Future";
  $desc = $collectedClasses._Future;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future.prototype = $desc;
  _Future.prototype.get$_zone = function() {
    return this._zone;
  };
  _Future.prototype.get$_nextListener = function() {
    return this._nextListener;
  };
  function _Future__addListener_closure(this_0, listener_1) {
    this.this_0 = this_0;
    this.listener_1 = listener_1;
  }
  _Future__addListener_closure.builtin$cls = "_Future__addListener_closure";
  if (!"name" in _Future__addListener_closure)
    _Future__addListener_closure.name = "_Future__addListener_closure";
  $desc = $collectedClasses._Future__addListener_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__addListener_closure.prototype = $desc;
  function _Future__chainFutures_closure(target_0) {
    this.target_0 = target_0;
  }
  _Future__chainFutures_closure.builtin$cls = "_Future__chainFutures_closure";
  if (!"name" in _Future__chainFutures_closure)
    _Future__chainFutures_closure.name = "_Future__chainFutures_closure";
  $desc = $collectedClasses._Future__chainFutures_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__chainFutures_closure.prototype = $desc;
  function _Future__chainFutures_closure0(target_1) {
    this.target_1 = target_1;
  }
  _Future__chainFutures_closure0.builtin$cls = "_Future__chainFutures_closure0";
  if (!"name" in _Future__chainFutures_closure0)
    _Future__chainFutures_closure0.name = "_Future__chainFutures_closure0";
  $desc = $collectedClasses._Future__chainFutures_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__chainFutures_closure0.prototype = $desc;
  function _Future__asyncComplete_closure(this_0, value_1) {
    this.this_0 = this_0;
    this.value_1 = value_1;
  }
  _Future__asyncComplete_closure.builtin$cls = "_Future__asyncComplete_closure";
  if (!"name" in _Future__asyncComplete_closure)
    _Future__asyncComplete_closure.name = "_Future__asyncComplete_closure";
  $desc = $collectedClasses._Future__asyncComplete_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__asyncComplete_closure.prototype = $desc;
  function _Future__propagateToListeners_closure(box_2, listener_3) {
    this.box_2 = box_2;
    this.listener_3 = listener_3;
  }
  _Future__propagateToListeners_closure.builtin$cls = "_Future__propagateToListeners_closure";
  if (!"name" in _Future__propagateToListeners_closure)
    _Future__propagateToListeners_closure.name = "_Future__propagateToListeners_closure";
  $desc = $collectedClasses._Future__propagateToListeners_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__propagateToListeners_closure.prototype = $desc;
  function _Future__propagateToListeners_closure0(box_2, box_1, hasError_4, listener_5) {
    this.box_2 = box_2;
    this.box_1 = box_1;
    this.hasError_4 = hasError_4;
    this.listener_5 = listener_5;
  }
  _Future__propagateToListeners_closure0.builtin$cls = "_Future__propagateToListeners_closure0";
  if (!"name" in _Future__propagateToListeners_closure0)
    _Future__propagateToListeners_closure0.name = "_Future__propagateToListeners_closure0";
  $desc = $collectedClasses._Future__propagateToListeners_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__propagateToListeners_closure0.prototype = $desc;
  function _Future__propagateToListeners__closure(box_2, listener_6) {
    this.box_2 = box_2;
    this.listener_6 = listener_6;
  }
  _Future__propagateToListeners__closure.builtin$cls = "_Future__propagateToListeners__closure";
  if (!"name" in _Future__propagateToListeners__closure)
    _Future__propagateToListeners__closure.name = "_Future__propagateToListeners__closure";
  $desc = $collectedClasses._Future__propagateToListeners__closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__propagateToListeners__closure.prototype = $desc;
  function _Future__propagateToListeners__closure0(box_0, listener_7) {
    this.box_0 = box_0;
    this.listener_7 = listener_7;
  }
  _Future__propagateToListeners__closure0.builtin$cls = "_Future__propagateToListeners__closure0";
  if (!"name" in _Future__propagateToListeners__closure0)
    _Future__propagateToListeners__closure0.name = "_Future__propagateToListeners__closure0";
  $desc = $collectedClasses._Future__propagateToListeners__closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _Future__propagateToListeners__closure0.prototype = $desc;
  function Stream() {
  }
  Stream.builtin$cls = "Stream";
  if (!"name" in Stream)
    Stream.name = "Stream";
  $desc = $collectedClasses.Stream;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Stream.prototype = $desc;
  function Stream_forEach_closure(box_0, this_1, action_2, future_3) {
    this.box_0 = box_0;
    this.this_1 = this_1;
    this.action_2 = action_2;
    this.future_3 = future_3;
  }
  Stream_forEach_closure.builtin$cls = "Stream_forEach_closure";
  if (!"name" in Stream_forEach_closure)
    Stream_forEach_closure.name = "Stream_forEach_closure";
  $desc = $collectedClasses.Stream_forEach_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Stream_forEach_closure.prototype = $desc;
  function Stream_forEach__closure(action_4, element_5) {
    this.action_4 = action_4;
    this.element_5 = element_5;
  }
  Stream_forEach__closure.builtin$cls = "Stream_forEach__closure";
  if (!"name" in Stream_forEach__closure)
    Stream_forEach__closure.name = "Stream_forEach__closure";
  $desc = $collectedClasses.Stream_forEach__closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Stream_forEach__closure.prototype = $desc;
  function Stream_forEach__closure0() {
  }
  Stream_forEach__closure0.builtin$cls = "Stream_forEach__closure0";
  if (!"name" in Stream_forEach__closure0)
    Stream_forEach__closure0.name = "Stream_forEach__closure0";
  $desc = $collectedClasses.Stream_forEach__closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Stream_forEach__closure0.prototype = $desc;
  function Stream_forEach_closure0(future_6) {
    this.future_6 = future_6;
  }
  Stream_forEach_closure0.builtin$cls = "Stream_forEach_closure0";
  if (!"name" in Stream_forEach_closure0)
    Stream_forEach_closure0.name = "Stream_forEach_closure0";
  $desc = $collectedClasses.Stream_forEach_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Stream_forEach_closure0.prototype = $desc;
  function Stream_length_closure(box_0) {
    this.box_0 = box_0;
  }
  Stream_length_closure.builtin$cls = "Stream_length_closure";
  if (!"name" in Stream_length_closure)
    Stream_length_closure.name = "Stream_length_closure";
  $desc = $collectedClasses.Stream_length_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Stream_length_closure.prototype = $desc;
  function Stream_length_closure0(box_0, future_1) {
    this.box_0 = box_0;
    this.future_1 = future_1;
  }
  Stream_length_closure0.builtin$cls = "Stream_length_closure0";
  if (!"name" in Stream_length_closure0)
    Stream_length_closure0.name = "Stream_length_closure0";
  $desc = $collectedClasses.Stream_length_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Stream_length_closure0.prototype = $desc;
  function StreamSubscription() {
  }
  StreamSubscription.builtin$cls = "StreamSubscription";
  if (!"name" in StreamSubscription)
    StreamSubscription.name = "StreamSubscription";
  $desc = $collectedClasses.StreamSubscription;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StreamSubscription.prototype = $desc;
  function _StreamController() {
  }
  _StreamController.builtin$cls = "_StreamController";
  if (!"name" in _StreamController)
    _StreamController.name = "_StreamController";
  $desc = $collectedClasses._StreamController;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StreamController.prototype = $desc;
  function _StreamController__subscribe_closure(this_0) {
    this.this_0 = this_0;
  }
  _StreamController__subscribe_closure.builtin$cls = "_StreamController__subscribe_closure";
  if (!"name" in _StreamController__subscribe_closure)
    _StreamController__subscribe_closure.name = "_StreamController__subscribe_closure";
  $desc = $collectedClasses._StreamController__subscribe_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StreamController__subscribe_closure.prototype = $desc;
  function _StreamController__recordCancel_complete(this_0) {
    this.this_0 = this_0;
  }
  _StreamController__recordCancel_complete.builtin$cls = "_StreamController__recordCancel_complete";
  if (!"name" in _StreamController__recordCancel_complete)
    _StreamController__recordCancel_complete.name = "_StreamController__recordCancel_complete";
  $desc = $collectedClasses._StreamController__recordCancel_complete;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StreamController__recordCancel_complete.prototype = $desc;
  function _SyncStreamControllerDispatch() {
  }
  _SyncStreamControllerDispatch.builtin$cls = "_SyncStreamControllerDispatch";
  if (!"name" in _SyncStreamControllerDispatch)
    _SyncStreamControllerDispatch.name = "_SyncStreamControllerDispatch";
  $desc = $collectedClasses._SyncStreamControllerDispatch;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SyncStreamControllerDispatch.prototype = $desc;
  function _AsyncStreamControllerDispatch() {
  }
  _AsyncStreamControllerDispatch.builtin$cls = "_AsyncStreamControllerDispatch";
  if (!"name" in _AsyncStreamControllerDispatch)
    _AsyncStreamControllerDispatch.name = "_AsyncStreamControllerDispatch";
  $desc = $collectedClasses._AsyncStreamControllerDispatch;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _AsyncStreamControllerDispatch.prototype = $desc;
  function _AsyncStreamController(_onListen, _onPause, _onResume, _onCancel, _varData, _state, _doneFuture) {
    this._onListen = _onListen;
    this._onPause = _onPause;
    this._onResume = _onResume;
    this._onCancel = _onCancel;
    this._varData = _varData;
    this._state = _state;
    this._doneFuture = _doneFuture;
  }
  _AsyncStreamController.builtin$cls = "_AsyncStreamController";
  if (!"name" in _AsyncStreamController)
    _AsyncStreamController.name = "_AsyncStreamController";
  $desc = $collectedClasses._AsyncStreamController;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _AsyncStreamController.prototype = $desc;
  _AsyncStreamController.prototype.get$_onListen = function() {
    return this._onListen;
  };
  _AsyncStreamController.prototype.get$_onPause = function() {
    return this._onPause;
  };
  _AsyncStreamController.prototype.get$_onResume = function() {
    return this._onResume;
  };
  _AsyncStreamController.prototype.get$_onCancel = function() {
    return this._onCancel;
  };
  function _StreamController__AsyncStreamControllerDispatch() {
  }
  _StreamController__AsyncStreamControllerDispatch.builtin$cls = "_StreamController__AsyncStreamControllerDispatch";
  if (!"name" in _StreamController__AsyncStreamControllerDispatch)
    _StreamController__AsyncStreamControllerDispatch.name = "_StreamController__AsyncStreamControllerDispatch";
  $desc = $collectedClasses._StreamController__AsyncStreamControllerDispatch;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StreamController__AsyncStreamControllerDispatch.prototype = $desc;
  function _SyncStreamController(_onListen, _onPause, _onResume, _onCancel, _varData, _state, _doneFuture) {
    this._onListen = _onListen;
    this._onPause = _onPause;
    this._onResume = _onResume;
    this._onCancel = _onCancel;
    this._varData = _varData;
    this._state = _state;
    this._doneFuture = _doneFuture;
  }
  _SyncStreamController.builtin$cls = "_SyncStreamController";
  if (!"name" in _SyncStreamController)
    _SyncStreamController.name = "_SyncStreamController";
  $desc = $collectedClasses._SyncStreamController;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _SyncStreamController.prototype = $desc;
  _SyncStreamController.prototype.get$_onListen = function() {
    return this._onListen;
  };
  _SyncStreamController.prototype.get$_onPause = function() {
    return this._onPause;
  };
  _SyncStreamController.prototype.get$_onResume = function() {
    return this._onResume;
  };
  _SyncStreamController.prototype.get$_onCancel = function() {
    return this._onCancel;
  };
  function _StreamController__SyncStreamControllerDispatch() {
  }
  _StreamController__SyncStreamControllerDispatch.builtin$cls = "_StreamController__SyncStreamControllerDispatch";
  if (!"name" in _StreamController__SyncStreamControllerDispatch)
    _StreamController__SyncStreamControllerDispatch.name = "_StreamController__SyncStreamControllerDispatch";
  $desc = $collectedClasses._StreamController__SyncStreamControllerDispatch;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StreamController__SyncStreamControllerDispatch.prototype = $desc;
  function _ControllerStream(_async$_controller) {
    this._async$_controller = _async$_controller;
  }
  _ControllerStream.builtin$cls = "_ControllerStream";
  if (!"name" in _ControllerStream)
    _ControllerStream.name = "_ControllerStream";
  $desc = $collectedClasses._ControllerStream;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _ControllerStream.prototype = $desc;
  function _ControllerSubscription(_async$_controller, _onData, _onError, _onDone, _zone, _state, _cancelFuture, _pending) {
    this._async$_controller = _async$_controller;
    this._onData = _onData;
    this._onError = _onError;
    this._onDone = _onDone;
    this._zone = _zone;
    this._state = _state;
    this._cancelFuture = _cancelFuture;
    this._pending = _pending;
  }
  _ControllerSubscription.builtin$cls = "_ControllerSubscription";
  if (!"name" in _ControllerSubscription)
    _ControllerSubscription.name = "_ControllerSubscription";
  $desc = $collectedClasses._ControllerSubscription;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _ControllerSubscription.prototype = $desc;
  function _EventSink() {
  }
  _EventSink.builtin$cls = "_EventSink";
  if (!"name" in _EventSink)
    _EventSink.name = "_EventSink";
  $desc = $collectedClasses._EventSink;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _EventSink.prototype = $desc;
  function _BufferingStreamSubscription(_onData, _onError, _onDone, _zone, _state, _cancelFuture, _pending) {
    this._onData = _onData;
    this._onError = _onError;
    this._onDone = _onDone;
    this._zone = _zone;
    this._state = _state;
    this._cancelFuture = _cancelFuture;
    this._pending = _pending;
  }
  _BufferingStreamSubscription.builtin$cls = "_BufferingStreamSubscription";
  if (!"name" in _BufferingStreamSubscription)
    _BufferingStreamSubscription.name = "_BufferingStreamSubscription";
  $desc = $collectedClasses._BufferingStreamSubscription;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BufferingStreamSubscription.prototype = $desc;
  _BufferingStreamSubscription.prototype.get$_zone = function() {
    return this._zone;
  };
  function _BufferingStreamSubscription__sendDone_sendDone(this_0) {
    this.this_0 = this_0;
  }
  _BufferingStreamSubscription__sendDone_sendDone.builtin$cls = "_BufferingStreamSubscription__sendDone_sendDone";
  if (!"name" in _BufferingStreamSubscription__sendDone_sendDone)
    _BufferingStreamSubscription__sendDone_sendDone.name = "_BufferingStreamSubscription__sendDone_sendDone";
  $desc = $collectedClasses._BufferingStreamSubscription__sendDone_sendDone;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BufferingStreamSubscription__sendDone_sendDone.prototype = $desc;
  function _StreamImpl() {
  }
  _StreamImpl.builtin$cls = "_StreamImpl";
  if (!"name" in _StreamImpl)
    _StreamImpl.name = "_StreamImpl";
  $desc = $collectedClasses._StreamImpl;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StreamImpl.prototype = $desc;
  function _DelayedEvent(next) {
    this.next = next;
  }
  _DelayedEvent.builtin$cls = "_DelayedEvent";
  if (!"name" in _DelayedEvent)
    _DelayedEvent.name = "_DelayedEvent";
  $desc = $collectedClasses._DelayedEvent;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _DelayedEvent.prototype = $desc;
  _DelayedEvent.prototype.get$next = function() {
    return this.next;
  };
  _DelayedEvent.prototype.set$next = function(v) {
    return this.next = v;
  };
  function _DelayedData(value, next) {
    this.value = value;
    this.next = next;
  }
  _DelayedData.builtin$cls = "_DelayedData";
  if (!"name" in _DelayedData)
    _DelayedData.name = "_DelayedData";
  $desc = $collectedClasses._DelayedData;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _DelayedData.prototype = $desc;
  function _DelayedDone() {
  }
  _DelayedDone.builtin$cls = "_DelayedDone";
  if (!"name" in _DelayedDone)
    _DelayedDone.name = "_DelayedDone";
  $desc = $collectedClasses._DelayedDone;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _DelayedDone.prototype = $desc;
  function _PendingEvents() {
  }
  _PendingEvents.builtin$cls = "_PendingEvents";
  if (!"name" in _PendingEvents)
    _PendingEvents.name = "_PendingEvents";
  $desc = $collectedClasses._PendingEvents;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _PendingEvents.prototype = $desc;
  function _PendingEvents_schedule_closure(this_0, dispatch_1) {
    this.this_0 = this_0;
    this.dispatch_1 = dispatch_1;
  }
  _PendingEvents_schedule_closure.builtin$cls = "_PendingEvents_schedule_closure";
  if (!"name" in _PendingEvents_schedule_closure)
    _PendingEvents_schedule_closure.name = "_PendingEvents_schedule_closure";
  $desc = $collectedClasses._PendingEvents_schedule_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _PendingEvents_schedule_closure.prototype = $desc;
  function _StreamImplEvents(firstPendingEvent, lastPendingEvent, _state) {
    this.firstPendingEvent = firstPendingEvent;
    this.lastPendingEvent = lastPendingEvent;
    this._state = _state;
  }
  _StreamImplEvents.builtin$cls = "_StreamImplEvents";
  if (!"name" in _StreamImplEvents)
    _StreamImplEvents.name = "_StreamImplEvents";
  $desc = $collectedClasses._StreamImplEvents;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _StreamImplEvents.prototype = $desc;
  function _cancelAndError_closure(future_0, error_1, stackTrace_2) {
    this.future_0 = future_0;
    this.error_1 = error_1;
    this.stackTrace_2 = stackTrace_2;
  }
  _cancelAndError_closure.builtin$cls = "_cancelAndError_closure";
  if (!"name" in _cancelAndError_closure)
    _cancelAndError_closure.name = "_cancelAndError_closure";
  $desc = $collectedClasses._cancelAndError_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _cancelAndError_closure.prototype = $desc;
  function _cancelAndErrorClosure_closure(subscription_0, future_1) {
    this.subscription_0 = subscription_0;
    this.future_1 = future_1;
  }
  _cancelAndErrorClosure_closure.builtin$cls = "_cancelAndErrorClosure_closure";
  if (!"name" in _cancelAndErrorClosure_closure)
    _cancelAndErrorClosure_closure.name = "_cancelAndErrorClosure_closure";
  $desc = $collectedClasses._cancelAndErrorClosure_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _cancelAndErrorClosure_closure.prototype = $desc;
  function _BaseZone() {
  }
  _BaseZone.builtin$cls = "_BaseZone";
  if (!"name" in _BaseZone)
    _BaseZone.name = "_BaseZone";
  $desc = $collectedClasses._BaseZone;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BaseZone.prototype = $desc;
  function _BaseZone_bindCallback_closure(this_0, registered_1) {
    this.this_0 = this_0;
    this.registered_1 = registered_1;
  }
  _BaseZone_bindCallback_closure.builtin$cls = "_BaseZone_bindCallback_closure";
  if (!"name" in _BaseZone_bindCallback_closure)
    _BaseZone_bindCallback_closure.name = "_BaseZone_bindCallback_closure";
  $desc = $collectedClasses._BaseZone_bindCallback_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BaseZone_bindCallback_closure.prototype = $desc;
  function _BaseZone_bindCallback_closure0(this_2, registered_3) {
    this.this_2 = this_2;
    this.registered_3 = registered_3;
  }
  _BaseZone_bindCallback_closure0.builtin$cls = "_BaseZone_bindCallback_closure0";
  if (!"name" in _BaseZone_bindCallback_closure0)
    _BaseZone_bindCallback_closure0.name = "_BaseZone_bindCallback_closure0";
  $desc = $collectedClasses._BaseZone_bindCallback_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BaseZone_bindCallback_closure0.prototype = $desc;
  function _BaseZone_bindUnaryCallback_closure(this_0, registered_1) {
    this.this_0 = this_0;
    this.registered_1 = registered_1;
  }
  _BaseZone_bindUnaryCallback_closure.builtin$cls = "_BaseZone_bindUnaryCallback_closure";
  if (!"name" in _BaseZone_bindUnaryCallback_closure)
    _BaseZone_bindUnaryCallback_closure.name = "_BaseZone_bindUnaryCallback_closure";
  $desc = $collectedClasses._BaseZone_bindUnaryCallback_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BaseZone_bindUnaryCallback_closure.prototype = $desc;
  function _BaseZone_bindUnaryCallback_closure0(this_2, registered_3) {
    this.this_2 = this_2;
    this.registered_3 = registered_3;
  }
  _BaseZone_bindUnaryCallback_closure0.builtin$cls = "_BaseZone_bindUnaryCallback_closure0";
  if (!"name" in _BaseZone_bindUnaryCallback_closure0)
    _BaseZone_bindUnaryCallback_closure0.name = "_BaseZone_bindUnaryCallback_closure0";
  $desc = $collectedClasses._BaseZone_bindUnaryCallback_closure0;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _BaseZone_bindUnaryCallback_closure0.prototype = $desc;
  function _rootHandleUncaughtError_closure(error_0, stackTrace_1) {
    this.error_0 = error_0;
    this.stackTrace_1 = stackTrace_1;
  }
  _rootHandleUncaughtError_closure.builtin$cls = "_rootHandleUncaughtError_closure";
  if (!"name" in _rootHandleUncaughtError_closure)
    _rootHandleUncaughtError_closure.name = "_rootHandleUncaughtError_closure";
  $desc = $collectedClasses._rootHandleUncaughtError_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _rootHandleUncaughtError_closure.prototype = $desc;
  function _rootHandleUncaughtError__closure(error_2, stackTrace_3) {
    this.error_2 = error_2;
    this.stackTrace_3 = stackTrace_3;
  }
  _rootHandleUncaughtError__closure.builtin$cls = "_rootHandleUncaughtError__closure";
  if (!"name" in _rootHandleUncaughtError__closure)
    _rootHandleUncaughtError__closure.name = "_rootHandleUncaughtError__closure";
  $desc = $collectedClasses._rootHandleUncaughtError__closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _rootHandleUncaughtError__closure.prototype = $desc;
  function _RootZone() {
  }
  _RootZone.builtin$cls = "_RootZone";
  if (!"name" in _RootZone)
    _RootZone.name = "_RootZone";
  $desc = $collectedClasses._RootZone;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _RootZone.prototype = $desc;
  function _HashMap(_collection$_length, _strings, _nums, _rest, _keys) {
    this._collection$_length = _collection$_length;
    this._strings = _strings;
    this._nums = _nums;
    this._rest = _rest;
    this._keys = _keys;
  }
  _HashMap.builtin$cls = "_HashMap";
  if (!"name" in _HashMap)
    _HashMap.name = "_HashMap";
  $desc = $collectedClasses._HashMap;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HashMap.prototype = $desc;
  function _HashMap_values_closure(this_0) {
    this.this_0 = this_0;
  }
  _HashMap_values_closure.builtin$cls = "_HashMap_values_closure";
  if (!"name" in _HashMap_values_closure)
    _HashMap_values_closure.name = "_HashMap_values_closure";
  $desc = $collectedClasses._HashMap_values_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HashMap_values_closure.prototype = $desc;
  function HashMapKeyIterable(_map) {
    this._map = _map;
  }
  HashMapKeyIterable.builtin$cls = "HashMapKeyIterable";
  if (!"name" in HashMapKeyIterable)
    HashMapKeyIterable.name = "HashMapKeyIterable";
  $desc = $collectedClasses.HashMapKeyIterable;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HashMapKeyIterable.prototype = $desc;
  function HashMapKeyIterator(_map, _keys, _offset, _collection$_current) {
    this._map = _map;
    this._keys = _keys;
    this._offset = _offset;
    this._collection$_current = _collection$_current;
  }
  HashMapKeyIterator.builtin$cls = "HashMapKeyIterator";
  if (!"name" in HashMapKeyIterator)
    HashMapKeyIterator.name = "HashMapKeyIterator";
  $desc = $collectedClasses.HashMapKeyIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HashMapKeyIterator.prototype = $desc;
  function _LinkedHashMap(_collection$_length, _strings, _nums, _rest, _first, _last, _modifications) {
    this._collection$_length = _collection$_length;
    this._strings = _strings;
    this._nums = _nums;
    this._rest = _rest;
    this._first = _first;
    this._last = _last;
    this._modifications = _modifications;
  }
  _LinkedHashMap.builtin$cls = "_LinkedHashMap";
  if (!"name" in _LinkedHashMap)
    _LinkedHashMap.name = "_LinkedHashMap";
  $desc = $collectedClasses._LinkedHashMap;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _LinkedHashMap.prototype = $desc;
  function _LinkedHashMap_values_closure(this_0) {
    this.this_0 = this_0;
  }
  _LinkedHashMap_values_closure.builtin$cls = "_LinkedHashMap_values_closure";
  if (!"name" in _LinkedHashMap_values_closure)
    _LinkedHashMap_values_closure.name = "_LinkedHashMap_values_closure";
  $desc = $collectedClasses._LinkedHashMap_values_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _LinkedHashMap_values_closure.prototype = $desc;
  function LinkedHashMapCell(_key, _value, _next, _previous) {
    this._key = _key;
    this._value = _value;
    this._next = _next;
    this._previous = _previous;
  }
  LinkedHashMapCell.builtin$cls = "LinkedHashMapCell";
  if (!"name" in LinkedHashMapCell)
    LinkedHashMapCell.name = "LinkedHashMapCell";
  $desc = $collectedClasses.LinkedHashMapCell;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LinkedHashMapCell.prototype = $desc;
  LinkedHashMapCell.prototype.get$_key = function() {
    return this._key;
  };
  LinkedHashMapCell.prototype.get$_value = function() {
    return this._value;
  };
  LinkedHashMapCell.prototype.set$_value = function(v) {
    return this._value = v;
  };
  LinkedHashMapCell.prototype.get$_next = function() {
    return this._next;
  };
  LinkedHashMapCell.prototype.set$_next = function(v) {
    return this._next = v;
  };
  LinkedHashMapCell.prototype.get$_previous = function() {
    return this._previous;
  };
  LinkedHashMapCell.prototype.set$_previous = function(v) {
    return this._previous = v;
  };
  function LinkedHashMapKeyIterable(_map) {
    this._map = _map;
  }
  LinkedHashMapKeyIterable.builtin$cls = "LinkedHashMapKeyIterable";
  if (!"name" in LinkedHashMapKeyIterable)
    LinkedHashMapKeyIterable.name = "LinkedHashMapKeyIterable";
  $desc = $collectedClasses.LinkedHashMapKeyIterable;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LinkedHashMapKeyIterable.prototype = $desc;
  function LinkedHashMapKeyIterator(_map, _modifications, _cell, _collection$_current) {
    this._map = _map;
    this._modifications = _modifications;
    this._cell = _cell;
    this._collection$_current = _collection$_current;
  }
  LinkedHashMapKeyIterator.builtin$cls = "LinkedHashMapKeyIterator";
  if (!"name" in LinkedHashMapKeyIterator)
    LinkedHashMapKeyIterator.name = "LinkedHashMapKeyIterator";
  $desc = $collectedClasses.LinkedHashMapKeyIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LinkedHashMapKeyIterator.prototype = $desc;
  function _HashSet() {
  }
  _HashSet.builtin$cls = "_HashSet";
  if (!"name" in _HashSet)
    _HashSet.name = "_HashSet";
  $desc = $collectedClasses._HashSet;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HashSet.prototype = $desc;
  function _IdentityHashSet(_collection$_length, _strings, _nums, _rest, _elements) {
    this._collection$_length = _collection$_length;
    this._strings = _strings;
    this._nums = _nums;
    this._rest = _rest;
    this._elements = _elements;
  }
  _IdentityHashSet.builtin$cls = "_IdentityHashSet";
  if (!"name" in _IdentityHashSet)
    _IdentityHashSet.name = "_IdentityHashSet";
  $desc = $collectedClasses._IdentityHashSet;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _IdentityHashSet.prototype = $desc;
  function HashSetIterator(_set, _elements, _offset, _collection$_current) {
    this._set = _set;
    this._elements = _elements;
    this._offset = _offset;
    this._collection$_current = _collection$_current;
  }
  HashSetIterator.builtin$cls = "HashSetIterator";
  if (!"name" in HashSetIterator)
    HashSetIterator.name = "HashSetIterator";
  $desc = $collectedClasses.HashSetIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  HashSetIterator.prototype = $desc;
  function _LinkedHashSet(_collection$_length, _strings, _nums, _rest, _first, _last, _modifications) {
    this._collection$_length = _collection$_length;
    this._strings = _strings;
    this._nums = _nums;
    this._rest = _rest;
    this._first = _first;
    this._last = _last;
    this._modifications = _modifications;
  }
  _LinkedHashSet.builtin$cls = "_LinkedHashSet";
  if (!"name" in _LinkedHashSet)
    _LinkedHashSet.name = "_LinkedHashSet";
  $desc = $collectedClasses._LinkedHashSet;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _LinkedHashSet.prototype = $desc;
  function LinkedHashSetCell(_element, _next, _previous) {
    this._element = _element;
    this._next = _next;
    this._previous = _previous;
  }
  LinkedHashSetCell.builtin$cls = "LinkedHashSetCell";
  if (!"name" in LinkedHashSetCell)
    LinkedHashSetCell.name = "LinkedHashSetCell";
  $desc = $collectedClasses.LinkedHashSetCell;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LinkedHashSetCell.prototype = $desc;
  LinkedHashSetCell.prototype.get$_element = function() {
    return this._element;
  };
  LinkedHashSetCell.prototype.get$_next = function() {
    return this._next;
  };
  LinkedHashSetCell.prototype.set$_next = function(v) {
    return this._next = v;
  };
  LinkedHashSetCell.prototype.get$_previous = function() {
    return this._previous;
  };
  LinkedHashSetCell.prototype.set$_previous = function(v) {
    return this._previous = v;
  };
  function LinkedHashSetIterator(_set, _modifications, _cell, _collection$_current) {
    this._set = _set;
    this._modifications = _modifications;
    this._cell = _cell;
    this._collection$_current = _collection$_current;
  }
  LinkedHashSetIterator.builtin$cls = "LinkedHashSetIterator";
  if (!"name" in LinkedHashSetIterator)
    LinkedHashSetIterator.name = "LinkedHashSetIterator";
  $desc = $collectedClasses.LinkedHashSetIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  LinkedHashSetIterator.prototype = $desc;
  function _HashSetBase() {
  }
  _HashSetBase.builtin$cls = "_HashSetBase";
  if (!"name" in _HashSetBase)
    _HashSetBase.name = "_HashSetBase";
  $desc = $collectedClasses._HashSetBase;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _HashSetBase.prototype = $desc;
  function IterableBase() {
  }
  IterableBase.builtin$cls = "IterableBase";
  if (!"name" in IterableBase)
    IterableBase.name = "IterableBase";
  $desc = $collectedClasses.IterableBase;
  if ($desc instanceof Array)
    $desc = $desc[1];
  IterableBase.prototype = $desc;
  function ListMixin() {
  }
  ListMixin.builtin$cls = "ListMixin";
  if (!"name" in ListMixin)
    ListMixin.name = "ListMixin";
  $desc = $collectedClasses.ListMixin;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ListMixin.prototype = $desc;
  function Maps_mapToString_closure(box_0, result_1) {
    this.box_0 = box_0;
    this.result_1 = result_1;
  }
  Maps_mapToString_closure.builtin$cls = "Maps_mapToString_closure";
  if (!"name" in Maps_mapToString_closure)
    Maps_mapToString_closure.name = "Maps_mapToString_closure";
  $desc = $collectedClasses.Maps_mapToString_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Maps_mapToString_closure.prototype = $desc;
  function ListQueue(_table, _head, _tail, _modificationCount) {
    this._table = _table;
    this._head = _head;
    this._tail = _tail;
    this._modificationCount = _modificationCount;
  }
  ListQueue.builtin$cls = "ListQueue";
  if (!"name" in ListQueue)
    ListQueue.name = "ListQueue";
  $desc = $collectedClasses.ListQueue;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ListQueue.prototype = $desc;
  function _ListQueueIterator(_queue, _end, _modificationCount, _collection$_position, _collection$_current) {
    this._queue = _queue;
    this._end = _end;
    this._modificationCount = _modificationCount;
    this._collection$_position = _collection$_position;
    this._collection$_current = _collection$_current;
  }
  _ListQueueIterator.builtin$cls = "_ListQueueIterator";
  if (!"name" in _ListQueueIterator)
    _ListQueueIterator.name = "_ListQueueIterator";
  $desc = $collectedClasses._ListQueueIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _ListQueueIterator.prototype = $desc;
  function NoSuchMethodError_toString_closure(box_0) {
    this.box_0 = box_0;
  }
  NoSuchMethodError_toString_closure.builtin$cls = "NoSuchMethodError_toString_closure";
  if (!"name" in NoSuchMethodError_toString_closure)
    NoSuchMethodError_toString_closure.name = "NoSuchMethodError_toString_closure";
  $desc = $collectedClasses.NoSuchMethodError_toString_closure;
  if ($desc instanceof Array)
    $desc = $desc[1];
  NoSuchMethodError_toString_closure.prototype = $desc;
  function DateTime(millisecondsSinceEpoch, isUtc) {
    this.millisecondsSinceEpoch = millisecondsSinceEpoch;
    this.isUtc = isUtc;
  }
  DateTime.builtin$cls = "DateTime";
  if (!"name" in DateTime)
    DateTime.name = "DateTime";
  $desc = $collectedClasses.DateTime;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DateTime.prototype = $desc;
  function DateTime_toString_fourDigits() {
  }
  DateTime_toString_fourDigits.builtin$cls = "DateTime_toString_fourDigits";
  if (!"name" in DateTime_toString_fourDigits)
    DateTime_toString_fourDigits.name = "DateTime_toString_fourDigits";
  $desc = $collectedClasses.DateTime_toString_fourDigits;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DateTime_toString_fourDigits.prototype = $desc;
  function DateTime_toString_threeDigits() {
  }
  DateTime_toString_threeDigits.builtin$cls = "DateTime_toString_threeDigits";
  if (!"name" in DateTime_toString_threeDigits)
    DateTime_toString_threeDigits.name = "DateTime_toString_threeDigits";
  $desc = $collectedClasses.DateTime_toString_threeDigits;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DateTime_toString_threeDigits.prototype = $desc;
  function DateTime_toString_twoDigits() {
  }
  DateTime_toString_twoDigits.builtin$cls = "DateTime_toString_twoDigits";
  if (!"name" in DateTime_toString_twoDigits)
    DateTime_toString_twoDigits.name = "DateTime_toString_twoDigits";
  $desc = $collectedClasses.DateTime_toString_twoDigits;
  if ($desc instanceof Array)
    $desc = $desc[1];
  DateTime_toString_twoDigits.prototype = $desc;
  function Duration(_duration) {
    this._duration = _duration;
  }
  Duration.builtin$cls = "Duration";
  if (!"name" in Duration)
    Duration.name = "Duration";
  $desc = $collectedClasses.Duration;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Duration.prototype = $desc;
  Duration.prototype.get$_duration = function() {
    return this._duration;
  };
  function Duration_toString_sixDigits() {
  }
  Duration_toString_sixDigits.builtin$cls = "Duration_toString_sixDigits";
  if (!"name" in Duration_toString_sixDigits)
    Duration_toString_sixDigits.name = "Duration_toString_sixDigits";
  $desc = $collectedClasses.Duration_toString_sixDigits;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Duration_toString_sixDigits.prototype = $desc;
  function Duration_toString_twoDigits() {
  }
  Duration_toString_twoDigits.builtin$cls = "Duration_toString_twoDigits";
  if (!"name" in Duration_toString_twoDigits)
    Duration_toString_twoDigits.name = "Duration_toString_twoDigits";
  $desc = $collectedClasses.Duration_toString_twoDigits;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Duration_toString_twoDigits.prototype = $desc;
  function Error() {
  }
  Error.builtin$cls = "Error";
  if (!"name" in Error)
    Error.name = "Error";
  $desc = $collectedClasses.Error;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Error.prototype = $desc;
  function NullThrownError() {
  }
  NullThrownError.builtin$cls = "NullThrownError";
  if (!"name" in NullThrownError)
    NullThrownError.name = "NullThrownError";
  $desc = $collectedClasses.NullThrownError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  NullThrownError.prototype = $desc;
  function ArgumentError(message) {
    this.message = message;
  }
  ArgumentError.builtin$cls = "ArgumentError";
  if (!"name" in ArgumentError)
    ArgumentError.name = "ArgumentError";
  $desc = $collectedClasses.ArgumentError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ArgumentError.prototype = $desc;
  function RangeError(message) {
    this.message = message;
  }
  RangeError.builtin$cls = "RangeError";
  if (!"name" in RangeError)
    RangeError.name = "RangeError";
  $desc = $collectedClasses.RangeError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  RangeError.prototype = $desc;
  function UnsupportedError(message) {
    this.message = message;
  }
  UnsupportedError.builtin$cls = "UnsupportedError";
  if (!"name" in UnsupportedError)
    UnsupportedError.name = "UnsupportedError";
  $desc = $collectedClasses.UnsupportedError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UnsupportedError.prototype = $desc;
  function UnimplementedError(message) {
    this.message = message;
  }
  UnimplementedError.builtin$cls = "UnimplementedError";
  if (!"name" in UnimplementedError)
    UnimplementedError.name = "UnimplementedError";
  $desc = $collectedClasses.UnimplementedError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  UnimplementedError.prototype = $desc;
  function StateError(message) {
    this.message = message;
  }
  StateError.builtin$cls = "StateError";
  if (!"name" in StateError)
    StateError.name = "StateError";
  $desc = $collectedClasses.StateError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StateError.prototype = $desc;
  function ConcurrentModificationError(modifiedObject) {
    this.modifiedObject = modifiedObject;
  }
  ConcurrentModificationError.builtin$cls = "ConcurrentModificationError";
  if (!"name" in ConcurrentModificationError)
    ConcurrentModificationError.name = "ConcurrentModificationError";
  $desc = $collectedClasses.ConcurrentModificationError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ConcurrentModificationError.prototype = $desc;
  function StackOverflowError() {
  }
  StackOverflowError.builtin$cls = "StackOverflowError";
  if (!"name" in StackOverflowError)
    StackOverflowError.name = "StackOverflowError";
  $desc = $collectedClasses.StackOverflowError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StackOverflowError.prototype = $desc;
  function CyclicInitializationError(variableName) {
    this.variableName = variableName;
  }
  CyclicInitializationError.builtin$cls = "CyclicInitializationError";
  if (!"name" in CyclicInitializationError)
    CyclicInitializationError.name = "CyclicInitializationError";
  $desc = $collectedClasses.CyclicInitializationError;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CyclicInitializationError.prototype = $desc;
  function _ExceptionImplementation(message) {
    this.message = message;
  }
  _ExceptionImplementation.builtin$cls = "_ExceptionImplementation";
  if (!"name" in _ExceptionImplementation)
    _ExceptionImplementation.name = "_ExceptionImplementation";
  $desc = $collectedClasses._ExceptionImplementation;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _ExceptionImplementation.prototype = $desc;
  function Expando(name) {
    this.name = name;
  }
  Expando.builtin$cls = "Expando";
  if (!"name" in Expando)
    Expando.name = "Expando";
  $desc = $collectedClasses.Expando;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Expando.prototype = $desc;
  function Iterator() {
  }
  Iterator.builtin$cls = "Iterator";
  if (!"name" in Iterator)
    Iterator.name = "Iterator";
  $desc = $collectedClasses.Iterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Iterator.prototype = $desc;
  function Null() {
  }
  Null.builtin$cls = "Null";
  if (!"name" in Null)
    Null.name = "Null";
  $desc = $collectedClasses.Null;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Null.prototype = $desc;
  function Object() {
  }
  Object.builtin$cls = "Object";
  if (!"name" in Object)
    Object.name = "Object";
  $desc = $collectedClasses.Object;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Object.prototype = $desc;
  function StackTrace() {
  }
  StackTrace.builtin$cls = "StackTrace";
  if (!"name" in StackTrace)
    StackTrace.name = "StackTrace";
  $desc = $collectedClasses.StackTrace;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StackTrace.prototype = $desc;
  function StringBuffer(_contents) {
    this._contents = _contents;
  }
  StringBuffer.builtin$cls = "StringBuffer";
  if (!"name" in StringBuffer)
    StringBuffer.name = "StringBuffer";
  $desc = $collectedClasses.StringBuffer;
  if ($desc instanceof Array)
    $desc = $desc[1];
  StringBuffer.prototype = $desc;
  StringBuffer.prototype.get$_contents = function() {
    return this._contents;
  };
  function Symbol() {
  }
  Symbol.builtin$cls = "Symbol";
  if (!"name" in Symbol)
    Symbol.name = "Symbol";
  $desc = $collectedClasses.Symbol;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Symbol.prototype = $desc;
  function Interceptor_CssStyleDeclarationBase() {
  }
  Interceptor_CssStyleDeclarationBase.builtin$cls = "Interceptor_CssStyleDeclarationBase";
  if (!"name" in Interceptor_CssStyleDeclarationBase)
    Interceptor_CssStyleDeclarationBase.name = "Interceptor_CssStyleDeclarationBase";
  $desc = $collectedClasses.Interceptor_CssStyleDeclarationBase;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Interceptor_CssStyleDeclarationBase.prototype = $desc;
  function CssStyleDeclarationBase() {
  }
  CssStyleDeclarationBase.builtin$cls = "CssStyleDeclarationBase";
  if (!"name" in CssStyleDeclarationBase)
    CssStyleDeclarationBase.name = "CssStyleDeclarationBase";
  $desc = $collectedClasses.CssStyleDeclarationBase;
  if ($desc instanceof Array)
    $desc = $desc[1];
  CssStyleDeclarationBase.prototype = $desc;
  function Interceptor_ListMixin() {
  }
  Interceptor_ListMixin.builtin$cls = "Interceptor_ListMixin";
  if (!"name" in Interceptor_ListMixin)
    Interceptor_ListMixin.name = "Interceptor_ListMixin";
  $desc = $collectedClasses.Interceptor_ListMixin;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Interceptor_ListMixin.prototype = $desc;
  function Interceptor_ListMixin_ImmutableListMixin() {
  }
  Interceptor_ListMixin_ImmutableListMixin.builtin$cls = "Interceptor_ListMixin_ImmutableListMixin";
  if (!"name" in Interceptor_ListMixin_ImmutableListMixin)
    Interceptor_ListMixin_ImmutableListMixin.name = "Interceptor_ListMixin_ImmutableListMixin";
  $desc = $collectedClasses.Interceptor_ListMixin_ImmutableListMixin;
  if ($desc instanceof Array)
    $desc = $desc[1];
  Interceptor_ListMixin_ImmutableListMixin.prototype = $desc;
  function ImmutableListMixin() {
  }
  ImmutableListMixin.builtin$cls = "ImmutableListMixin";
  if (!"name" in ImmutableListMixin)
    ImmutableListMixin.name = "ImmutableListMixin";
  $desc = $collectedClasses.ImmutableListMixin;
  if ($desc instanceof Array)
    $desc = $desc[1];
  ImmutableListMixin.prototype = $desc;
  function FixedSizeListIterator(_array, _length, _position, _current) {
    this._array = _array;
    this._length = _length;
    this._position = _position;
    this._current = _current;
  }
  FixedSizeListIterator.builtin$cls = "FixedSizeListIterator";
  if (!"name" in FixedSizeListIterator)
    FixedSizeListIterator.name = "FixedSizeListIterator";
  $desc = $collectedClasses.FixedSizeListIterator;
  if ($desc instanceof Array)
    $desc = $desc[1];
  FixedSizeListIterator.prototype = $desc;
  function _JSRandom() {
  }
  _JSRandom.builtin$cls = "_JSRandom";
  if (!"name" in _JSRandom)
    _JSRandom.name = "_JSRandom";
  $desc = $collectedClasses._JSRandom;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _JSRandom.prototype = $desc;
  function _NativeTypedArray() {
  }
  _NativeTypedArray.builtin$cls = "_NativeTypedArray";
  if (!"name" in _NativeTypedArray)
    _NativeTypedArray.name = "_NativeTypedArray";
  $desc = $collectedClasses._NativeTypedArray;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _NativeTypedArray.prototype = $desc;
  function _NativeTypedArrayOfInt() {
  }
  _NativeTypedArrayOfInt.builtin$cls = "_NativeTypedArrayOfInt";
  if (!"name" in _NativeTypedArrayOfInt)
    _NativeTypedArrayOfInt.name = "_NativeTypedArrayOfInt";
  $desc = $collectedClasses._NativeTypedArrayOfInt;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _NativeTypedArrayOfInt.prototype = $desc;
  function _NativeTypedArray_ListMixin() {
  }
  _NativeTypedArray_ListMixin.builtin$cls = "_NativeTypedArray_ListMixin";
  if (!"name" in _NativeTypedArray_ListMixin)
    _NativeTypedArray_ListMixin.name = "_NativeTypedArray_ListMixin";
  $desc = $collectedClasses._NativeTypedArray_ListMixin;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _NativeTypedArray_ListMixin.prototype = $desc;
  function _NativeTypedArray_ListMixin_FixedLengthListMixin() {
  }
  _NativeTypedArray_ListMixin_FixedLengthListMixin.builtin$cls = "_NativeTypedArray_ListMixin_FixedLengthListMixin";
  if (!"name" in _NativeTypedArray_ListMixin_FixedLengthListMixin)
    _NativeTypedArray_ListMixin_FixedLengthListMixin.name = "_NativeTypedArray_ListMixin_FixedLengthListMixin";
  $desc = $collectedClasses._NativeTypedArray_ListMixin_FixedLengthListMixin;
  if ($desc instanceof Array)
    $desc = $desc[1];
  _NativeTypedArray_ListMixin_FixedLengthListMixin.prototype = $desc;
  return [HtmlElement, AnchorElement, AnimationEvent, AreaElement, AudioElement, AutocompleteErrorEvent, BRElement, BaseElement, BeforeLoadEvent, BeforeUnloadEvent, BodyElement, ButtonElement, CDataSection, CanvasElement, CharacterData, CloseEvent, Comment, CompositionEvent, ContentElement, CssFontFaceLoadEvent, CssStyleDeclaration, CustomEvent, DListElement, DataListElement, DetailsElement, DeviceMotionEvent, DeviceOrientationEvent, DialogElement, DivElement, Document, DocumentFragment, DocumentType, DomError, DomException, Element, EmbedElement, ErrorEvent, Event, EventTarget, FieldSetElement, FileError, FocusEvent, FormElement, HRElement, HashChangeEvent, HeadElement, HeadingElement, HtmlDocument, HtmlHtmlElement, IFrameElement, ImageElement, InputElement, KeyboardEvent, KeygenElement, LIElement, LabelElement, LegendElement, LinkElement, MapElement, MediaElement, MediaError, MediaKeyError, MediaKeyEvent, MediaKeyMessageEvent, MediaKeyNeededEvent, MediaStream, MediaStreamEvent, MediaStreamTrackEvent, MenuElement, MessageEvent, MetaElement, MeterElement, MidiConnectionEvent, MidiMessageEvent, ModElement, MouseEvent, Navigator, NavigatorUserMediaError, Node, NodeList, OListElement, ObjectElement, OptGroupElement, OptionElement, OutputElement, OverflowEvent, PageTransitionEvent, ParagraphElement, ParamElement, PopStateEvent, PositionError, PreElement, ProcessingInstruction, ProgressElement, ProgressEvent, QuoteElement, ResourceProgressEvent, RtcDataChannelEvent, RtcDtmfToneChangeEvent, RtcIceCandidateEvent, ScriptElement, SecurityPolicyViolationEvent, SelectElement, ShadowElement, ShadowRoot, SourceElement, SpanElement, SpeechInputEvent, SpeechRecognitionError, SpeechRecognitionEvent, SpeechSynthesisEvent, StorageEvent, StyleElement, TableCaptionElement, TableCellElement, TableColElement, TableElement, TableRowElement, TableSectionElement, TemplateElement, Text, TextAreaElement, TextEvent, TitleElement, TouchEvent, TrackElement, TrackEvent, TransitionEvent, UIEvent, UListElement, UnknownElement, VideoElement, WheelEvent, Window, _Attr, _ClientRect, _Entity, _HTMLAppletElement, _HTMLBaseFontElement, _HTMLDirectoryElement, _HTMLFontElement, _HTMLFrameElement, _HTMLFrameSetElement, _HTMLMarqueeElement, _MutationEvent, _Notation, _XMLHttpRequestProgressEvent, VersionChangeEvent, AElement, AltGlyphElement, AnimateElement, AnimateMotionElement, AnimateTransformElement, AnimatedLength, AnimatedLengthList, AnimatedNumber, AnimatedNumberList, AnimationElement, CircleElement, ClipPathElement, DefsElement, DescElement, EllipseElement, FEBlendElement, FEColorMatrixElement, FEComponentTransferElement, FECompositeElement, FEConvolveMatrixElement, FEDiffuseLightingElement, FEDisplacementMapElement, FEDistantLightElement, FEFloodElement, FEFuncAElement, FEFuncBElement, FEFuncGElement, FEFuncRElement, FEGaussianBlurElement, FEImageElement, FEMergeElement, FEMergeNodeElement, FEMorphologyElement, FEOffsetElement, FEPointLightElement, FESpecularLightingElement, FESpotLightElement, FETileElement, FETurbulenceElement, FilterElement, ForeignObjectElement, GElement, GraphicsElement, ImageElement0, LineElement, LinearGradientElement, MarkerElement, MaskElement, MetadataElement, PathElement, PatternElement, PolygonElement, PolylineElement, RadialGradientElement, RectElement, ScriptElement0, SetElement, StopElement, StyleElement0, SvgDocument, SvgElement, SvgSvgElement, SwitchElement, SymbolElement, TSpanElement, TextContentElement, TextElement, TextPathElement, TextPositioningElement, TitleElement0, UseElement, ViewElement, ZoomEvent, _GradientElement, _SVGAltGlyphDefElement, _SVGAltGlyphItemElement, _SVGAnimateColorElement, _SVGComponentTransferFunctionElement, _SVGCursorElement, _SVGFEDropShadowElement, _SVGFontElement, _SVGFontFaceElement, _SVGFontFaceFormatElement, _SVGFontFaceNameElement, _SVGFontFaceSrcElement, _SVGFontFaceUriElement, _SVGGlyphElement, _SVGGlyphRefElement, _SVGHKernElement, _SVGMPathElement, _SVGMissingGlyphElement, _SVGVKernElement, AudioProcessingEvent, OfflineAudioCompletionEvent, ContextEvent, SqlError, TypedData, Uint8List, JS_CONST, Interceptor, JSBool, JSNull, JavaScriptObject, PlainJavaScriptObject, UnknownJavaScriptObject, JSArray, JSNumber, JSInt, JSDouble, JSString, startRootIsolate_closure, startRootIsolate_closure0, _Manager, _IsolateContext, _EventLoop, _EventLoop__runHelper_next, _IsolateEvent, _MainManagerStub, IsolateNatives__processWorkerMessage_closure, _BaseSendPort, _NativeJsSendPort, _NativeJsSendPort_send_closure, _WorkerSendPort, RawReceivePortImpl, ReceivePortImpl, _JsSerializer, _JsCopier, _JsDeserializer, _JsVisitedMap, _MessageTraverserVisitedMap, _MessageTraverser, _Copier, _Copier_visitMap_closure, _Serializer, _Deserializer, TimerImpl, TimerImpl_internalCallback, TimerImpl_internalCallback0, ReflectionInfo, TypeErrorDecoder, NullError, JsNoSuchMethodError, UnknownJsTypeError, unwrapException_saveStackTrace, _StackTrace, invokeClosure_closure, invokeClosure_closure0, invokeClosure_closure1, invokeClosure_closure2, invokeClosure_closure3, Closure, TearOffClosure, BoundClosure, RuntimeError, RuntimeType, RuntimeFunctionType, DynamicRuntimeType, TypeImpl, initHooks_closure, initHooks_closure0, initHooks_closure1, Balls, Balls_tick_closure, Balls_collideBalls_closure, Balls_collideBalls__closure, Ball, CountDownClock, ClockNumber, ClockNumber_setPixels_closure, Colon, ListIterator, MappedIterable, EfficientLengthMappedIterable, MappedIterator, FixedLengthListMixin, _AsyncError, Future, _Future, _Future__addListener_closure, _Future__chainFutures_closure, _Future__chainFutures_closure0, _Future__asyncComplete_closure, _Future__propagateToListeners_closure, _Future__propagateToListeners_closure0, _Future__propagateToListeners__closure, _Future__propagateToListeners__closure0, Stream, Stream_forEach_closure, Stream_forEach__closure, Stream_forEach__closure0, Stream_forEach_closure0, Stream_length_closure, Stream_length_closure0, StreamSubscription, _StreamController, _StreamController__subscribe_closure, _StreamController__recordCancel_complete, _SyncStreamControllerDispatch, _AsyncStreamControllerDispatch, _AsyncStreamController, _StreamController__AsyncStreamControllerDispatch, _SyncStreamController, _StreamController__SyncStreamControllerDispatch, _ControllerStream, _ControllerSubscription, _EventSink, _BufferingStreamSubscription, _BufferingStreamSubscription__sendDone_sendDone, _StreamImpl, _DelayedEvent, _DelayedData, _DelayedDone, _PendingEvents, _PendingEvents_schedule_closure, _StreamImplEvents, _cancelAndError_closure, _cancelAndErrorClosure_closure, _BaseZone, _BaseZone_bindCallback_closure, _BaseZone_bindCallback_closure0, _BaseZone_bindUnaryCallback_closure, _BaseZone_bindUnaryCallback_closure0, _rootHandleUncaughtError_closure, _rootHandleUncaughtError__closure, _RootZone, _HashMap, _HashMap_values_closure, HashMapKeyIterable, HashMapKeyIterator, _LinkedHashMap, _LinkedHashMap_values_closure, LinkedHashMapCell, LinkedHashMapKeyIterable, LinkedHashMapKeyIterator, _HashSet, _IdentityHashSet, HashSetIterator, _LinkedHashSet, LinkedHashSetCell, LinkedHashSetIterator, _HashSetBase, IterableBase, ListMixin, Maps_mapToString_closure, ListQueue, _ListQueueIterator, NoSuchMethodError_toString_closure, DateTime, DateTime_toString_fourDigits, DateTime_toString_threeDigits, DateTime_toString_twoDigits, Duration, Duration_toString_sixDigits, Duration_toString_twoDigits, Error, NullThrownError, ArgumentError, RangeError, UnsupportedError, UnimplementedError, StateError, ConcurrentModificationError, StackOverflowError, CyclicInitializationError, _ExceptionImplementation, Expando, Iterator, Null, Object, StackTrace, StringBuffer, Symbol, Interceptor_CssStyleDeclarationBase, CssStyleDeclarationBase, Interceptor_ListMixin, Interceptor_ListMixin_ImmutableListMixin, ImmutableListMixin, FixedSizeListIterator, _JSRandom, _NativeTypedArray, _NativeTypedArrayOfInt, _NativeTypedArray_ListMixin, _NativeTypedArray_ListMixin_FixedLengthListMixin];
}
