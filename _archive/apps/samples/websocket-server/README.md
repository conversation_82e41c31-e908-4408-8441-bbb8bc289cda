<a target="_blank" href="https://chrome.google.com/webstore/detail/pkbpddppnkjmlbgliipgmhjeialadokj">![Try it now in CWS](https://raw.github.com/GoogleChrome/chrome-extensions-samples/main/_archive/apps/tryitnowbutton.png "Click here to install this sample from the Chrome Web Store")</a>


# Http WebSocket Server

An example Http and WebSocket server implementation with a sample app which serves itself and allows connected clients to chat with each other. The API is very similar to NodeJS with the intention that this could be an easy drop-in replacement for a NodeJS served application.

## APIs

* [Sockets](https://developer.chrome.com/apps/sockets_tcpServer)
* [Runtime](https://developer.chrome.com/docs/extensions/reference/app_runtime)
* [Window](https://developer.chrome.com/docs/extensions/reference/app_window)


## Screenshot
![screenshot](/_archive/apps/samples/websocket-server/assets/screenshot_1280_800.png)
