{"extName": {"message": "News Reader (by Google)"}, "extDesc": {"message": "Displays the latest stories from Google News in a popup."}, "ext_default_title": {"message": "Google News"}, "1": {"message": "Top Stories"}, "n": {"message": "Nation"}, "w": {"message": "World"}, "b": {"message": "Business"}, "t": {"message": "Science/Technology"}, "e": {"message": "Entertainment"}, "s": {"message": "Sports"}, "m": {"message": "Health"}, "po": {"message": "Most Popular"}, "options": {"message": "Options"}, "more_stories": {"message": "More stories"}, "direction": {"message": "ltr"}, "country": {"message": "Country:"}, "topic": {"message": "Topics:"}, "save": {"message": "Save"}, "saveStatus": {"message": "Options saved"}, "storyCount": {"message": "Number of stories:"}, "newsOption": {"message": "Google News Options"}, "customText": {"message": "Custom Topics:"}, "maximumTopics": {"message": "(Maximum $count$)", "placeholders": {"count": {"content": "$1"}}}, "submitButton": {"message": "Add"}, "deleteTitle": {"message": "Delete"}, "invalidChars": {"message": "Invalid character(s)"}, "noTopic": {"message": "At least one Topic must be selected"}, "fetchError": {"message": "Error: Failed to fetch news stories."}, "wrongTopic": {"message": "Error: Not a valid feed."}, "noStory": {"message": "No story right now"}}