button,
input[type='button'],
input[type='submit'] {
  -webkit-border-radius: 2px;
  -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  background: -webkit-linear-gradient(#fafafa, #f4f4f4 40%, #e5e5e5);
  border: 1px solid #aaa;
  color: #444;
  font-size: inherit;
  margin-bottom: 0px;
  min-width: 4em;
  padding: 3px 12px 3px 12px;
}

button:hover,
input[type='button']:hover,
input[type='submit']:hover {
  -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
  background: #ebebeb -webkit-linear-gradient(#fefefe, #f8f8f8 40%, #e9e9e9);
  border-color: #999;
  color: #222;
}

button:active,
input[type='button']:active,
input[type='submit']:active {
  -webkit-box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);
  background: #ebebeb -webkit-linear-gradient(#f4f4f4, #efefef 40%, #dcdcdc);
  color: #333;
}

button[disabled],
input[type='button'][disabled],
input[type='submit'][disabled],
button[disabled]:hover,
input[type='button'][disabled]:hover,
input[type='submit'][disabled]:hover {
  -webkit-box-shadow: none;
  background: -webkit-linear-gradient(#fafafa, #f4f4f4 40%, #e5e5e5);
  border-color: #aaa;
  color: #888;
}
