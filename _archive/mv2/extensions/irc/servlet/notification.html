<html>
  <head>
    <script src="util.js" type="text/javascript"></script>
    <style>
body {
  margin: 0px;
  padding: 0px;
  -webkit-user-select: none;
}

#notification {
  width: 300px;
  height: 50px;
  position: fixed;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-align: stretch;
}

#title {
  display: -webkit-box;
  -webkit-box-flex: 0;
  padding: 2px 4px 2px 4px;
  background-color: #AAAAAA;
  color: white;
  font-family: Verdana, sans-serif;
  font-size: 10px;
}

#content {
  display: -webkit-box;
  -webkit-box-flex: 1;
  color: #444444;
  font-family: "Lucida Console", Monospace;
  font-size: 12px;
  padding: 4px;
  text-overflow: ellipsis;
}
    </style>
    <script>
window.onload = function() {
  var argString = location.search.substring(location.search.indexOf("?") + 1);
  var tokens = argString.split("&");
  var args = {};
  tokens.forEach(function(token) {
    var keyVal = token.split("=");
    args[keyVal[0]] = decodeURIComponent(keyVal[1]);
  });

  $('title').innerText = args.title;
  $('content').innerText = args.content;
}
    </script>   
  </head>
  <body onclick="window.close();">
    <div id="notification">
      <div id="title"></div>
      <div id="content"></div>
    </div>
  </body>
</html>
