// Popup脚本 - 处理用户界面交互

document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Popup界面已加载');
    
    const processBtn = document.getElementById('processBtn');
    const statusDiv = document.getElementById('status');
    
    // 更新状态显示
    function updateStatus(message, type = 'info') {
        statusDiv.className = `status ${type}`;
        statusDiv.innerHTML = message;
        console.log(`📊 状态更新: ${type} - ${message}`);
    }
    
    // 显示加载状态
    function showLoading() {
        processBtn.disabled = true;
        processBtn.innerHTML = '<span class="loading"></span>处理中...';
        updateStatus('正在处理页面文本，请稍候...', 'processing');
    }
    
    // 恢复按钮状态
    function resetButton() {
        processBtn.disabled = false;
        processBtn.innerHTML = '🚀 开始处理文本';
    }
    
    // 处理按钮点击
    processBtn.addEventListener('click', function() {
        console.log('🖱️ 用户点击处理按钮');
        
        showLoading();
        
        // 发送消息到background script
        chrome.runtime.sendMessage(
            { action: 'processText' },
            function(response) {
                resetButton();
                
                if (chrome.runtime.lastError) {
                    console.error('❌ 发送消息失败:', chrome.runtime.lastError);
                    updateStatus('❌ 扩展通信失败: ' + chrome.runtime.lastError.message, 'error');
                    return;
                }
                
                console.log('📨 收到响应:', response);
                
                if (response && response.success) {
                    updateStatus('✅ ' + response.message, 'success');
                    
                    // 3秒后自动关闭popup
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                } else {
                    const errorMsg = response ? response.message : '未知错误';
                    updateStatus('❌ 处理失败: ' + errorMsg, 'error');
                }
            }
        );
    });
    
    // 检查当前标签页状态
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        if (tabs[0]) {
            const url = tabs[0].url;
            console.log('📄 当前页面:', url);
            
            // 检查是否是支持的页面
            if (url.startsWith('chrome://') || url.startsWith('chrome-extension://') || url.startsWith('edge://')) {
                updateStatus('⚠️ 当前页面不支持文本处理（浏览器内部页面）', 'error');
                processBtn.disabled = true;
                processBtn.innerHTML = '❌ 页面不支持';
            } else if (url.startsWith('file://')) {
                updateStatus('💡 本地文件页面，确保已允许访问文件URL', 'info');
            } else {
                updateStatus('✅ 页面支持文本处理，点击按钮开始', 'info');
            }
        } else {
            updateStatus('❌ 无法获取当前页面信息', 'error');
            processBtn.disabled = true;
        }
    });
    
    // 监听来自content script的消息（如果有的话）
    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
        if (request.action === 'updatePopupStatus') {
            updateStatus(request.message, request.type || 'info');
        }
    });
    
    console.log('✅ Popup脚本初始化完成');
});
