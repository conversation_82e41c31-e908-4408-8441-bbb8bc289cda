{"manifest_version": 3, "name": "Page Text Logger", "version": "1.0", "description": "Click the extension icon to log current page text content to console", "icons": {"16": "images/icon-16.png"}, "permissions": ["activeTab", "scripting"], "host_permissions": ["<all_urls>", "https://*/*", "http://*/*"], "action": {"default_popup": "popup.html", "default_title": "文本处理器"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-simple.js"], "run_at": "document_end"}]}