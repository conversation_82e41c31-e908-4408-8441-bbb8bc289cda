_To create a README for your sample_

1. _Copy this file to your sample folder._
1. _Enter the requested information below._
1. _Delete these instructions._

_For API samples use the name of the API. For example, a sample about the `chrome.declarativeNetRequest` would simply be called "chrome.declarativeNetRequest". (Do not use special formatting in headings.) For functional samples, the title should be what the sample demonstrates_

# Title

_Describe what the sample demonstrates. If this is an API sample, link to the API._

This sample demonstrates ...

## Overview

_Describe how the sample demonstrates the API or use case and briefly describe how to use it._

## Implementation Notes

_Add any information that doesn't fit elsewhere in the README._

## Running this extension

1. Clone this repository.
2. Load this directory in Chrome as an [unpacked extension](https://developer.chrome.com/docs/extensions/mv3/getstarted/development-basics/#load-unpacked).
3. _Add the rest of the instructions here_
