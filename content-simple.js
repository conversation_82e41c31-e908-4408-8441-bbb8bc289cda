// Chrome扩展 - 简化版文本处理脚本
// 纯内存跟踪，页面刷新自动重置，API失败可重试

(function() {
  'use strict';

  console.log('🚀 Chrome扩展文本处理脚本已加载（简化版）');

  // 全局变量 - 纯内存存储，页面刷新自动重置
  // 注意：在content script中使用window，在background script中使用globalThis
  const globalScope = typeof window !== 'undefined' ? window : globalThis;

  globalScope.processedTextNodes = globalScope.processedTextNodes || new WeakSet();
  globalScope.processedNodeIds = globalScope.processedNodeIds || new Set();
  globalScope.processingNodes = globalScope.processingNodes || new Set(); // 正在处理中的节点
  globalScope.isProcessing = globalScope.isProcessing || false;

  // API调用函数
  async function callTextProcessingAPI(text, timeout = 10000) {
    try {
      console.log(`📤 发送API请求: "${text.substring(0, 50)}..."`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch('http://localhost:8000/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: text }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`📦 API响应:`, data);

      if (data.status === 'success' && data.result) {
        console.log(`✅ API处理成功: "${data.result.substring(0, 50)}..."`);
        return data.result;
      } else {
        throw new Error('API响应格式异常或处理失败');
      }
    } catch (error) {
      console.error(`❌ API调用失败: ${error.message}`);
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  // 生成节点唯一ID
  function generateNodeId(textNode) {
    try {
      if (!textNode || !textNode.parentElement) {
        return null;
      }

      // 生成DOM路径
      const path = [];
      let element = textNode.parentElement;
      
      while (element && element !== document.body) {
        let selector = element.tagName.toLowerCase();
        
        if (element.id) {
          selector += `#${element.id}`;
        } else if (element.className) {
          const classes = element.className.trim().split(/\s+/).slice(0, 2);
          if (classes.length > 0) {
            selector += `.${classes.join('.')}`;
          }
        }
        
        // 添加位置信息
        const siblings = Array.from(element.parentElement?.children || []);
        const index = siblings.indexOf(element);
        if (index > 0) {
          selector += `:nth-child(${index + 1})`;
        }
        
        path.unshift(selector);
        element = element.parentElement;
      }

      // 结合路径和文本内容生成唯一ID
      const elementPath = path.join(' > ');
      const textContent = textNode.textContent.trim().substring(0, 100);
      const nodeId = `${elementPath}::${textContent}`;

      return nodeId;
    } catch (error) {
      console.warn('生成节点ID失败:', error);
      return null;
    }
  }

  // 检查节点是否已被处理或正在处理
  function isNodeProcessed(textNode) {
    const nodeId = generateNodeId(textNode);
    if (!nodeId) return false;

    // 检查1：已处理的节点（WeakSet - 最快）
    if (globalScope.processedTextNodes.has(textNode)) {
      return true;
    }

    // 检查2：已处理的节点ID（Set - 快速）
    if (globalScope.processedNodeIds.has(nodeId)) {
      return true;
    }

    // 检查3：正在处理中的节点（防止并发重复）
    if (globalScope.processingNodes.has(nodeId)) {
      console.log(`⏳ 节点正在处理中，跳过: "${textNode.textContent.substring(0, 30)}..."`);
      return true;
    }

    return false;
  }

  // 标记节点为正在处理中
  function markNodeAsProcessing(textNode) {
    const nodeId = generateNodeId(textNode);
    if (nodeId) {
      globalScope.processingNodes.add(nodeId);
      console.log(`⏳ 标记节点为处理中: "${textNode.textContent.substring(0, 30)}..."`);
      return nodeId;
    }
    return null;
  }

  // 标记节点为已处理
  function markNodeAsProcessed(textNode, nodeId) {
    // 添加到已处理集合
    globalScope.processedTextNodes.add(textNode);
    if (nodeId) {
      globalScope.processedNodeIds.add(nodeId);
      // 从处理中集合移除
      globalScope.processingNodes.delete(nodeId);
    }
    console.log(`✅ 标记节点为已处理: "${textNode.textContent.substring(0, 30)}..."`);
  }

  // 清理节点处理中状态（API失败时调用）
  function clearNodeProcessing(textNode, nodeId) {
    if (nodeId) {
      globalScope.processingNodes.delete(nodeId);
      console.log(`🧹 清理节点处理中状态: "${textNode.textContent.substring(0, 30)}..."`);
    }
  }

  // 获取所有文本节点
  function getAllTextNodes() {
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: function(node) {
          const text = node.textContent.trim();
          const parent = node.parentElement;
          
          if (!text || !parent) {
            return NodeFilter.FILTER_REJECT;
          }

          // 排除脚本、样式、代码块等
          const excludeTags = ['SCRIPT', 'STYLE', 'CODE', 'PRE', 'TEXTAREA', 'INPUT'];
          if (excludeTags.includes(parent.tagName)) {
            return NodeFilter.FILTER_REJECT;
          }

          // 排除太短或太长的文本
          if (text.length < 5 || text.length > 500) {
            return NodeFilter.FILTER_REJECT;
          }

          return NodeFilter.FILTER_ACCEPT;
        }
      }
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }

    return textNodes;
  }

  // 并发控制器
  class ConcurrencyController {
    constructor(maxConcurrent = 3) {
      this.maxConcurrent = maxConcurrent;
      this.running = 0;
      this.queue = [];
    }

    async execute(task) {
      return new Promise((resolve, reject) => {
        this.queue.push({ task, resolve, reject });
        this.processQueue();
      });
    }

    async processQueue() {
      if (this.running >= this.maxConcurrent || this.queue.length === 0) {
        return;
      }

      this.running++;
      const { task, resolve, reject } = this.queue.shift();

      try {
        const result = await task();
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        this.running--;
        this.processQueue();
      }
    }
  }

  // 主处理函数
  async function processTextNodes(description = '') {
    if (globalScope.isProcessing) {
      console.log('⏸️ 已有处理任务在进行中，跳过');
      return;
    }

    globalScope.isProcessing = true;

    try {
      console.log(`🚀 ${description}开始处理文本节点...`);

      // 获取所有文本节点
      const textNodes = getAllTextNodes();
      console.log(`📋 找到 ${textNodes.length} 个文本节点`);

      // 过滤出未处理的节点
      const validTextNodes = textNodes.filter(node => !isNodeProcessed(node));
      
      if (validTextNodes.length === 0) {
        console.log('✨ 没有新的文本节点需要处理');
        return;
      }

      console.log(`🎯 需要处理 ${validTextNodes.length} 个新节点`);

      // 初始化并发控制器
      const concurrencyController = new ConcurrencyController(3);

      // 创建处理任务
      const tasks = validTextNodes.map(textNode => {
        return concurrencyController.execute(async () => {
          const originalText = textNode.textContent;
          const nodeId = markNodeAsProcessing(textNode); // 立即标记为处理中
          
          try {
            const processedText = await callTextProcessingAPI(originalText);
            
            // 替换文本内容
            textNode.textContent = processedText;
            
            // 标记为已处理
            markNodeAsProcessed(textNode, nodeId);
            
            return { success: true, originalText, processedText };
          } catch (error) {
            // API失败，清理处理中状态，允许下次重试
            clearNodeProcessing(textNode, nodeId);
            console.error(`❌ 处理失败: "${originalText.substring(0, 30)}..." - ${error.message}`);
            return { success: false, originalText, error: error.message };
          }
        });
      });

      // 等待所有任务完成
      console.log(`🚀 开始并发执行 ${tasks.length} 个处理任务...`);
      const results = await Promise.allSettled(tasks);

      // 统计结果
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success)).length;

      console.log(`📊 处理完成 - 成功: ${successful}, 失败: ${failed}`);
      
      if (failed > 0) {
        console.log('💡 失败的节点下次点击扩展时可以重新处理');
      }

    } catch (error) {
      console.error('❌ 处理过程中发生错误:', error);
    } finally {
      globalScope.isProcessing = false;
      console.log('🏁 处理任务结束');
    }
  }

  // 监听来自popup的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'processText') {
      processTextNodes('用户触发 - ')
        .then(() => {
          sendResponse({ success: true, message: '文本处理完成' });
        })
        .catch(error => {
          sendResponse({ success: false, message: error.message });
        });
      return true; // 保持消息通道开放
    }
  });

  // 页面加载完成后的初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      console.log('📄 页面加载完成，扩展已准备就绪');
    });
  } else {
    console.log('📄 页面已加载，扩展已准备就绪');
  }

})();
